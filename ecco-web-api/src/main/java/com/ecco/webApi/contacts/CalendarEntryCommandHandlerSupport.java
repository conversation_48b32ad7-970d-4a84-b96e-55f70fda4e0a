package com.ecco.webApi.contacts;

import com.ecco.calendar.CombinedEntry;
import com.ecco.calendar.core.*;
import com.ecco.calendar.dom.EventType;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dao.CustomEventRecurringRepository;
import com.ecco.dao.CustomEventRepository;
import com.ecco.dom.*;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.service.EventService;
import com.ecco.webApi.calendar.CalendarEntryCommandViewModel;
import com.ecco.calendar.dom.MedDate;
import com.ecco.calendar.core.Recurrence.RecurrenceHandle;
import com.ecco.calendar.core.util.DateTimeUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.*;
import org.joda.time.chrono.ISOChronology;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Creates/Update a calendar entry.
 * This mimics the current approach which updates events using a POST in EntityController through EventTypeDefinition.
 * Updates are done on CustomEventImpl with an AOP in place to then update the underlying calendar.
 * Therefore we maintain consistency in both calendar systems using this approach for now.
 *
 * There was an AOP to save non-iCal events to the iCal calendar system.
 * This is now integrated with direct calls in this handler.
 * This handler now manages all calendar operations - syncing of non-iCal events to iCal events (currently one-way).
 * Previously, the AOP allowed any entity to be intercepted, and its calendar captured and processed, as follows:
 *      - if the operation was setEntity, the iCal and non-iCal data was created (with the calendar UID generated and set in both)
 *      - if the operation was deleteEntity, the iCal was deleted and the non-iCal was assumed to be deleted with the entity
 *      - if the operation was updateEntity, the iCal and non-iCal data was updated (if isForDeletion was set, the iCal data was deleted, non-iCal was assumed)
 *              NB isForDeletion was unlikely to be followed closely as it just left a fairly safe dangling entry in the iCal calendar
*/
@Component
public class CalendarEntryCommandHandlerSupport {

    @Nonnull private final EventService eventService;
    @Nonnull private final IndividualRepository individualRepository;
    @Nonnull private final ServiceRecipientRepository serviceRecipientRepository;
    @Nonnull private final CalendarService calendarService;
    @Nonnull private final CustomEventRepository nonRecurringRepository;
    @Nonnull private final CustomEventRecurringRepository recurringRepository;
    @Nonnull private final EntityUriMapper entityUriMapper;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    public CalendarEntryCommandHandlerSupport(@Nonnull EventService eventService,
                                              @Nonnull IndividualRepository individualRepository,
                                              @Nonnull ServiceRecipientRepository serviceRecipientRepository,
                                              @Nonnull CalendarService calendarService,
                                              @Nonnull CustomEventRepository customEventNonRecurringRepository,
                                              @Nonnull CustomEventRecurringRepository customEventRecurringRepository,
                                              @Nonnull EntityUriMapper entityUriMapper) {
        this.eventService = eventService;
        this.calendarService = calendarService;
        this.individualRepository = individualRepository;
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.nonRecurringRepository = customEventNonRecurringRepository;
        this.recurringRepository = customEventRecurringRepository;
        this.entityUriMapper = entityUriMapper;
    }

    /**
     * Create a user event.
     * Mimic the existing calendar click GET (in calendar.ts), and pass through the same 'setUpNewCalendarEntry' sanitiser.
     * TODO mimic 'withClient' in tsx when on the client file - see 'withClient' creating "&clientContactId=${param.mainAttendeeContactId}&referralId=${param.referralId}&serviceRecipientId=${param.serviceRecipientId}"
     */
    public String addCalendarEntry(@Nullable Integer ownerServiceRecipientIdOptional, long ownerContactId, CalendarEntryCommandViewModel cmdVM) {

        // Notes on the owner of the event:
        //   Without a UI to create an event in someone else's calendar, the expectation has been to use the first
        //   attendee as the owner (see previous logic in EventServiceImpl#persistAndSyncAddNonRecurringCalendarEntry and DEV-2061 comments).
        //   This hasn't worked well in cases due to the order of the attendees (including the owner) being jumbled.
        //   1) When on a non-client calendar, the current user’s contactId is the target (which is the contactId sent in the
        //   command) and therefore all will appear on the event despite who the actual owner is.
        //   2) When on a client file, the client’s contactId is the target and therefore all will appear on the event despite
        //   who the actual owner is (and the current user will not be one of them).
        //   3) System generated client events are not muddled since they don't pass through the handler which has the issue (5e27e208).
        //   The new code currently works the same way, except now the owner is correct. To rectify the (probably few) historical
        //   issues, we have allowed all client appts to be editable - see 8e4bab58.

        List<? extends CalendarableEntity> attendees = getCalendarableAttendees(cmdVM);
        Individual ownerContact = individualRepository.findById(ownerContactId).orElseThrow();
        Integer ownerContactIdOptional = ownerServiceRecipientIdOptional != null ? null : Long.valueOf(ownerContactId).intValue();
        if (cmdVM.recurringStarted()) {
            return this.addRecurringCalendarEntry(ownerServiceRecipientIdOptional, ownerContactIdOptional, ownerContact, attendees, cmdVM);
        } else {
            CustomEventImpl e = addNonRecurringCalendarEntry(ownerServiceRecipientIdOptional, ownerContactIdOptional, ownerContact, attendees, cmdVM);
            return eventService.persistAndSyncAddNonRecurringCalendarEntry(e).getUid();
        }
    }

    private List<? extends CalendarableEntity> getCalendarableAttendees(CalendarEntryCommandViewModel cmdVM) {
        List<Long> attendeeContactIds = new ArrayList<>();

        // try calendarIds
        boolean hasOtherContactIds = cmdVM.getContactIds() != null && cmdVM.getContactIds().to.size() > 0;
        if (hasOtherContactIds) {
            attendeeContactIds.addAll(cmdVM.getContactIds().to);
            // TODO remove the contacts lookup into a tuple of id/calendarId for preSaveCalendarEntry - to confirm we don't get any other details

        // try EventAttendees
        } else {
            boolean hasOtherAttendeeVms = cmdVM.getAttendees() != null && cmdVM.getAttendees().length > 0;
            if (hasOtherAttendeeVms) {
                // TODO sort that we load contacts to get the id, to later load contacts
                attendeeContactIds.addAll(Arrays.stream(cmdVM.getAttendees()).map(a -> individualRepository.findByCalendarId(a.calendarId).orElseThrow().getId()).collect(Collectors.toList()));
            }
        }

        return individualRepository.findAllById(attendeeContactIds)
                .stream()
                .sorted(Comparator.comparingInt(a -> attendeeContactIds.indexOf(a.getId())))
                .collect(Collectors.toList());
    }

    private CustomEventImpl addNonRecurringCalendarEntry(@Nullable Integer ownerServiceRecipientIdOptional,
                                                         @Nullable Integer ownerContactIdOptional,
                                                         @Nonnull Individual ownerContact,
                                                         List<? extends CalendarableEntity> attendees,
                                                         CalendarEntryCommandViewModel cmdVM) {
        ListDefinitionEntry eventCategory = (cmdVM.getEventCategoryId() != null)
                ? getListDefinitionEntryRef(cmdVM.getEventCategoryId().to)
                : null;

        return NonRecurringUtils.addNonRecurringCalendarEntry(ownerServiceRecipientIdOptional, ownerContactIdOptional, ownerContact, attendees, eventCategory, cmdVM);
    }

    /**
     * Edit a user event. Potentially we could allow editing a system generated event - eg moving the time, but not yet.
     * Mimic the existing calendar click GET (in calendar.ts), and pass through the same 'setupEditCalendarEntry' sanitiser.
     * NB Repeating events themselves (the parent) are not updated currently - see DemandSchedule.createOrUpdateRecurringCalendarEvent
     */
    public String updateCalendarEntry(URI updatedBy, CalendarEntryCommandViewModel cmdVM) {
        boolean isRecurrence = calendarService.isRecurrence(cmdVM.eventUuid);

        if (isRecurrence) {
            updateRecurringCalendarEntry(updatedBy, cmdVM);

        } else {
            CustomEventImpl eView = nonRecurringRepository.findOneByUid(cmdVM.eventUuid).orElseThrow();
            CustomEventImpl e = entityManager.getReference(CustomEventImpl.class, eView.getId());
            //Hibernate.initialize(e);
            ListDefinitionEntry eventCategory = (cmdVM.getEventCategoryId() != null)
                    ? getListDefinitionEntryRef(cmdVM.getEventCategoryId().to)
                    : null;

            List<? extends CalendarableEntity> attendees = getCalendarableAttendees(cmdVM);
            Individual ownerContact = e.getServiceRecipientId() != null
                    ? serviceRecipientRepository.findOne(e.getServiceRecipientId()).getContact()
                    : individualRepository.findOne(e.getContactId());
            NonRecurringUtils.updateNonRecurringCalendarEntry(e, eventCategory, ownerContact, attendees, cmdVM);

            eventService.persistAndSyncUpdateNonRecurringCalendarEntry(e);

            // if started recurring - we error in the command view model because that is odd behaviour
        }

        return cmdVM.getEventUuid();
    }

    public void removeCalendarEntry(CalendarEntryCommandViewModel cmdVM) {
        boolean isRecurrence = calendarService.isRecurrence(cmdVM.eventUuid);

        if (isRecurrence) {
            // remove instance
            // NB we can never delete a master recurrence - we need to set 'every weeks' to zero for that
            removeRecurringCalendarEntry(cmdVM.eventUuid);

        } else {
            this.removeNonRecurringCalendarEntry(cmdVM.eventUuid);
        }
    }

    private void removeNonRecurringCalendarEntry(String uuid) {
        CombinedEntry ce = eventService.getEntry(uuid);
        CustomEventImpl e = (CustomEventImpl) ce.getEventEntry();
        assert e != null;
        eventService.persistAndSyncRemoveNonRecurringCalendarEntry(e);
    }

    private String addRecurringCalendarEntry(@Nullable Integer ownerServiceRecipientId,
                                             @Nullable Integer ownerContactId,
                                             @Nonnull Individual ownerContact,
                                             List<? extends CalendarableEntity> attendees,
                                             CalendarEntryCommandViewModel cmdVM) {

        // utilise the same logic of the non-recurring entry so that we can use the adapter with the latest details
        CustomEventImpl nonRecurringEvent = addNonRecurringCalendarEntry(ownerServiceRecipientId, ownerContactId, ownerContact, attendees, cmdVM);
        RecurringEntryDefinition recurringDef = new RecurringEntryDefinitionAdapter(nonRecurringEvent, cmdVM, this.entityUriMapper);

        CustomEventRecurringImpl recurringEvent = new CustomEventRecurringImpl(ownerServiceRecipientId, ownerContactId != null ? Long.valueOf(ownerContactId) : null);
        // other could imply a user event
        recurringEvent.setEventType(EventType.Other);

        if (cmdVM.getEventCategoryId() != null) {
            ListDefinitionEntry eventCategory = getListDefinitionEntryRef(cmdVM.getEventCategoryId().to);
            recurringEvent.setEventCategory(eventCategory);
        }

        final RecurringEntry recurringEntry = calendarService.createRecurringEntryNonRota(ownerContact.getCalendarId(), recurringDef);
        String recurringEntryHandle = recurringEntry.getHandle().toString();

        // TODO RecurringEntryDefinitionAdapter.attendees should be created so this can be handled in calendarService
        // add attendees (join their calendar's to the event)
        // following how individual events create attendees via addItemToCollection
        for (CalendarableEntity attendee : attendees) {
            // don't join if we have already assigned to the main calendar
            if (StringUtils.equals(attendee.getCalendarId(), ownerContact.getCalendarId())) {
                continue;
            }
            // attendee is set as an additional parent
            try {
                calendarService.addAttendeesToEntry(attendee.getCalendarId(), recurringEntryHandle);
            } catch (CalendarException e) {
                throw new CalendarException(e.getMessage(), e.getCause());
            }
        }

        recurringEvent.setUid(recurringEntryHandle);
        recurringRepository.save(recurringEvent);

        return recurringEntryHandle;
    }

    private void updateRecurringCalendarEntry(URI updatedBy, CalendarEntryCommandViewModel cmdVM) {
        Entry icalEntry = eventService.getEntry(cmdVM.getEventUuid()).getIcalEntry();
        String masterUuid = calendarService.getEntryHandleFromRecurrenceHandle(RecurrenceHandle.fromString(cmdVM.getEventUuid())).toString();
        CustomEventRecurringImpl localEntity = recurringRepository.findOneByUid(masterUuid).orElseThrow();

        // allow the ical instance to be edited
        // we do not need to create an ecco-specific 'event' in this work
        String title = cmdVM.getTitle() != null ? cmdVM.getTitle().to : null;
        DateTime startDateTime = icalEntry.getStart();
        if ((cmdVM.getStartTime() != null) && cmdVM.getStartTime().to != null) {
            startDateTime = icalEntry.getStart().toLocalDate().toDateTime(cmdVM.getStartTime().to);
        }
        if ((cmdVM.getStartDate() != null) && (cmdVM.getStartDate().to != null)) {
            startDateTime = cmdVM.getStartDate().to.toDateTime(startDateTime.toLocalTime());
        }
        DateTime endDateTime = icalEntry.getEnd();
        if ((cmdVM.getEndTime() != null) && cmdVM.getEndTime().to != null) {
            assert icalEntry.getEnd() != null; // FIXME: We're allowed to edit an all day event to not all day. This breaks
            endDateTime = icalEntry.getEnd().toLocalDate().toDateTime(cmdVM.getEndTime().to);
        }
        if ((cmdVM.getEndDate() != null) && (cmdVM.getEndDate().to != null)) {
            assert endDateTime != null;
            endDateTime = cmdVM.getEndDate().to.toDateTime(endDateTime.toLocalTime());
        }
        long duration = new Duration(startDateTime, endDateTime).getStandardMinutes();
        calendarService.editRecurrence(RecurrenceHandle.fromString(cmdVM.eventUuid), title, startDateTime, (int) duration, updatedBy);

        // change the category on the eventsrecurring in case the ecco-specific bits have changed
        if (cmdVM.getEventCategoryId() != null) {
            ListDefinitionEntry lde = getListDefinitionEntryRef(cmdVM.getEventCategoryId().to);
            localEntity.setEventCategory(lde);
        }

        if (cmdVM.recurringStopped()) {
            // curtail the master entry to this date - only successful if we've not modified or got data saved against items (ie not concrete)
            endDateTime = endDateTime == null ? startDateTime : endDateTime;
            calendarService.updateRecurringEntryBoundsEnd(calendarService.getEntryHandleFromRecurrenceHandle(RecurrenceHandle.fromString(cmdVM.eventUuid)),
                    endDateTime.toLocalDate());
        }

        // if cmdVM.recurringChanged() - we prevent this in the VM, as just like editing a non-recurring to be recurring,
        // changing the recurrence is a bit weird what to do with the one we are editing
    }

    private void removeRecurringCalendarEntry(String uuid) {
        // drop this instance
        // NB to delete the master, set the 'every' to 0
        // TODO check if evidence against them - this is currently done in the ui (or could do 'confirmed' etc)
        calendarService.dropRecurrence(RecurrenceHandle.fromString(uuid));
        // see calendarService.deleteEntry(cmdVM.eventUuid);
        // see DemandSchedule removeRecurringCalendarEvent
        // see calendarService.deleteEntry(getRecurringEntryHandle().toString());

        // TODO how do we ever delete a master?
        //recurringRepository.deleteByUid(Recurrence.Handle.fromString(uuid).toString());
    }

    private ListDefinitionEntry getListDefinitionEntryRef(Integer to) {
        return to == null ? null : entityManager.getReference(ListDefinitionEntry.class, to);
    }

    static class RecurringEntryDefinitionAdapter implements RecurringEntryDefinition {
        private final CustomEventImpl event;
        private final CalendarEntryCommandViewModel entry;
        private final EntityUriMapper entityUriMapper;

        RecurringEntryDefinitionAdapter(CustomEventImpl event, CalendarEntryCommandViewModel entry, EntityUriMapper entityUriMapper) {
            this.event = event;
            this.entry = entry;
            this.entityUriMapper = entityUriMapper;
        }

        @Override
        public String getTitle() {
            return event.getNameToPersist();
        }

        @Override
        public String getDescription() {
            return null;
        }

        @Override
        public DateTime getStart() {
            return DateTimeUtils.convertToDateTime(
                    event.getEventDateToPersist(), true, event.getEventDateToPersist().hasTime(), event.getTimeZone());
        }

        @Override
        public Duration getDuration() {
            // if allDay and no end date, then set 1 day duration.
            // This is one approach that avoids us specifying getStart() as Date (not a DateTime)
            // This is currently used for new events only, so this is the only source of 'allDay'
            if (event.getEventEndDateToPersist() != null && entry.getAllDay() != null && entry.getAllDay().to) {
                return Duration.standardDays(1);
            } else {
                // use the end date/time provided to work out the duration
                DateTime jodaEnd = DateTimeUtils.convertToDateTime(event.getEventEndDateToPersist(), true, event.getEventDateToPersist().hasTime(), event.getTimeZone());
                return new Duration(getStart(), jodaEnd);
            }
        }

        @Override
        public LocalDate getScheduleEndDate() {
            // prior to using getRepeatEndDate, getEndDate was used - but this was the same as used in getDuration
            // thereby creating a long event that repeated until the same end date
            // see RecurringEntryDefinitionAdapter.getDuration which called by addNonRecurringCalendarEntry above
            return entry.getRepeatEndDate() != null ? entry.getRepeatEndDate().to : null;
        }

        @Override
        public Set<Integer> getCalendarDays() {

            // REPEATING DAYS - specified from command tests currently
            if (entry.getRepeatEveryDays() != null && entry.getRepeatEveryDays().to != null) {
                DaysOfWeek days = DaysOfWeek.fromBits(entry.getRepeatEveryDays().to);
                return days.toCalendarDaySet();
            }

            // REPEATING DAY - specified from the startDate
            // the UI for calendar entry assumes its repeating at the startDate, since there is no UI for repeating
            // more days currently - however, this is the default behaviour anyway, so we can just return empty
            return Set.of();
        }

        @Override
        public String getIntervalType() {
            // intervalType not handled in normal calendar events yet
            return "WK";
        }

        @Override
        public Integer getIntervalFrequency() {
            return (entry.getRepeatEveryWeeks() != null && entry.getRepeatEveryWeeks().to != null)
                    ? entry.getRepeatEveryWeeks().to : 1;
        }

        @Override
        public URI getManagedByUri() {
            // TODO: this doesn't work because the ID is null at the time this is created.
            return null;//return entityUriMapper.uriForEntity(demandSchedule);
        }

        @Override
        public URI getUpdatedByUri() {
            // TODO: this doesn't work because the ID is null at the time this is created.
            return null;
        }
    }

    /**
     * Wrap all non-recurring logic in one place.
     */
    static class NonRecurringUtils {

        public static CustomEventImpl addNonRecurringCalendarEntry(@Nullable Integer ownerServiceRecipientIdOptional,
                                                                   @Nullable Integer ownerContactIdOptional,
                                                                   @Nonnull Individual ownerContact,
                                                                   List<? extends CalendarableEntity> attendees,
                                                                   ListDefinitionEntry eventCategory,
                                                                   CalendarEntryCommandViewModel cmdVM) {
            int day = cmdVM.getStartDate().to.dayOfMonth().get();
            int monthZeroBased = cmdVM.getStartDate().to.monthOfYear().get() - 1;
            int year = cmdVM.getStartDate().to.getYear();
            boolean allDay = cmdVM.getAllDay() != null ? cmdVM.getAllDay().to : false;
            Integer hour = null;
            Integer minute = null;
            if (cmdVM.getStartTime() != null && cmdVM.getStartTime().to != null) {
                hour = cmdVM.getStartTime().to.hourOfDay().get();
                minute = cmdVM.getStartTime().to.getMinuteOfHour();
            }
            String tz = "Europe/London";
            int minutesDuration = 240;
            CustomEventImpl e = CustomEventImpl.setUpNewCalendarEntry(day, monthZeroBased, year, allDay, hour, minute,
                                                                      minutesDuration, tz, ownerServiceRecipientIdOptional, ownerContactIdOptional);

            NonRecurringUtils.applyNonRecurringChanges(e, ownerContact, attendees, eventCategory, cmdVM, true);
            return e;
        }

        public static void updateNonRecurringCalendarEntry(CustomEventImpl e,
                                                           ListDefinitionEntry eventCategory,
                                                           @Nonnull Individual ownerContact,
                                                           List<? extends CalendarableEntity> attendees,
                                                           CalendarEntryCommandViewModel cmdVM) {
            //CustomEventImpl e = eventService.getManagedEventByUid(cmdVM.eventUuid);
            if (!cmdVM.hasChanges()) {
                return;
            }

            // only edit if we are not a generated system event, which means we have a CustomEvent, and not generated
            if (e == null || e.isGenerated()) {
                throw new IllegalArgumentException("cannot edit generated events");
            }

            CustomEventImpl.setupEditCalendarEntry(e);
            // NB don't change the locationContact - we just assume its always with the mainAttendee
            NonRecurringUtils.applyNonRecurringChanges(e, ownerContact, attendees, eventCategory, cmdVM, false);
        }

        /**
         * Update the calendar entry.
         * This mimics the current approach which updates events using a POST in EntityController through EventTypeDefinition.
         * Updates are done on CustomEventImpl with an AOP in place to then update the underlying calendar.
         * Therefore we maintain consistency in both calendar systems using this approach for now.
         *
         * Currently GET request in calendar.ts open the event via EventTypeDefinition which is placed in @SessionAttribute,
         * so that any further changes from the popup are applied. Those changes allowed are:
         *  - name
         *  - type ('other' sets as -1)
         *  - start time (if not on month view)
         *  - duration (when not on month view, this avoids negative end time complications)
         *  - attendees (only the ability to tick yourself or not - on new entries only)
         */
        public static void applyNonRecurringChanges(CustomEventImpl event,
                                                    @Nonnull Individual ownerContact,
                                                    List<? extends CalendarableEntity> attendees,
                                                    ListDefinitionEntry listDefEventCategory,
                                                    CalendarEntryCommandViewModel cmdVM,
                                                    boolean isNew) {

            // make it clear cmdVM.contactIds only applies to new events currently - see comments there
            if (isNew) {
                event.setEventType(EventType.Other); // other could imply a user event

                // set the contact location
                // this might not be right for every event - indeed, current custom events don't set this because the contact isn't fully populated
                event.setLocationContact(ownerContact);
            }

            if (cmdVM.hasChanges()) {

                // eventType - a system property, don't change

                // title
                if (cmdVM.getTitle() != null) {
                    event.setName(cmdVM.getTitle().to);
                }

                // eventCategory
                if (cmdVM.getEventCategoryId() != null) {
                    event.setEventCategory(listDefEventCategory);
                }

                // minutesDuration is the only available end date in the previous UI, so we did mimic existing calendar edits,
                // which meant converting our own endDate/time into minutesSpent for now.
                // HOWEVER, we are now allowing 'allday' in the UI, and already have the end from the command that we trust,
                // so we break the mould and avoid setting minutesDuration in the handler.
                // Setting to zero skips updateEndDateFromDuration which sets the end date for cosmo when we already know it
                // but its needed because its been provided for us from setupEditCalendarEntry.updateDurationFromDates.
                event.setMinutesDuration(0);

                // allDay save true: doesn't exist on CustomEventImpl, but the eventDate's MedTime being null indicates all day (see ItemEventConverterImpl)
                // allDay save false: a time must exist, which is set separately.
                // allDay load already comes from the underlying calendar - but this handler is about setting
                boolean allDaySet = cmdVM.getAllDay() != null && cmdVM.getAllDay().to;
                if (allDaySet) {
                    // mimic allDay when creating a new event - see CustomEventImpl.setUpNewCalendarEntry
                    // where allDay sets no time on the event date, which is then checked in ItemEventConverterImpl.convertItemEvent.
                    event.getEventDateToPersist().setTimeEnabled(false);
                    event.getEventDateToPersist().setTime(null);
                }

                // if we are changing the start date
                // NB minutesDuration certainly avoided timezone issues - but is a bit weird when spanning multiple days
                if ((cmdVM.getStartDate() != null) || cmdVM.getStartTime() != null) {
                    DateTimeZone tz = NonRecurringUtils.getDateTimeZoneFromCmd(cmdVM);
                    LocalTime startTime = NonRecurringUtils.getEventStartTime(event, cmdVM, tz);
                    DateTime dtStartInstant = NonRecurringUtils.getEventStartDate(startTime, event, cmdVM, tz);
                    MedDate startDate = DateTimeUtils.convertToMedDate(dtStartInstant, startTime != null);
                    event.setEventDate(startDate);
                }
                // if we are changing the end date
                // NB minutesDuration certainly avoided timezone issues - but is a bit weird when spanning multiple days
                if ((cmdVM.getEndDate() != null) || cmdVM.getEndTime() != null) {
                    DateTimeZone tz = NonRecurringUtils.getDateTimeZoneFromCmd(cmdVM);
                    LocalTime endTime = NonRecurringUtils.getEventEndTime(event, cmdVM, tz);
                    DateTime dtEndInstant = NonRecurringUtils.getEventEndDate(endTime, event, cmdVM, tz);
                    MedDate endDate = DateTimeUtils.convertToMedDate(dtEndInstant, endTime != null);
                    event.setEventEndDate(endDate);
                }
            }

            CustomEventImpl.preSaveCalendarEntry(event, isNew, attendees, ownerContact);
        }

        private static DateTime getEventStartDate(LocalTime time, CustomEventImpl event, CalendarEntryCommandViewModel cmdVM, DateTimeZone tz) {
            // convert the latest end date to a new local end date
            LocalDate date = (cmdVM.getStartDate() != null) && (cmdVM.getStartDate().to != null)
                    ? cmdVM.getStartDate().to
                    : DateTimeUtils.convertToDateTime(event.getEventDateToPersist(), true, false, tz)
                    .toLocalDate();

            LocalDateTime dtStart = time != null
                    ? date.toLocalDateTime(time)
                    : date.toLocalDateTime(LocalTime.MIDNIGHT);
            return dtStart.toDateTime(tz);
        }

        private static LocalTime getEventStartTime(CustomEventImpl event, CalendarEntryCommandViewModel cmdVM, DateTimeZone tz) {
            // convert the latest end time to a new local time
            return (cmdVM.getStartTime() != null) && (cmdVM.getStartTime().to != null)
                    ? cmdVM.getStartTime().to
                    : ((event.getEventDateToPersist() != null) && (event.getEventDateToPersist().hasTime()))
                        ? new LocalTime(event.getEventDateToPersist().getTime().getHour(), event.getEventDateToPersist().getTime().getMinute(),
                    0, 0, ISOChronology.getInstance(tz))
                        : null;
        }

        private static DateTime getEventEndDate(LocalTime time, CustomEventImpl event, CalendarEntryCommandViewModel cmdVM, DateTimeZone tz) {
            // convert the latest end date to a new local end date
            LocalDate date = (cmdVM.getEndDate() != null) && (cmdVM.getEndDate().to != null)
                    ? cmdVM.getEndDate().to
                    : !event.getEventEndDateToPersist().hasDetails()
                        ? DateTimeUtils.convertToDateTime(event.getEventDateToPersist(), true, false, tz).toLocalDate()
                        : DateTimeUtils.convertToDateTime(event.getEventEndDateToPersist(), true, false, tz).toLocalDate();

            LocalDateTime dtEnd = time != null
                    ? date.toLocalDateTime(time)
                    : date.toLocalDateTime(LocalTime.MIDNIGHT);

            return dtEnd.toDateTime(tz);
        }

        private static LocalTime getEventEndTime(CustomEventImpl event, CalendarEntryCommandViewModel cmdVM, DateTimeZone tz) {
            // convert the latest end time to a new local time
            return (cmdVM.getEndTime() != null) && (cmdVM.getEndTime().to != null)
                    ? cmdVM.getEndTime().to
                    : ((event.getEventEndDateToPersist() != null) && (event.getEventEndDateToPersist().hasTime()))
                    ? new LocalTime(event.getEventEndDateToPersist().getTime().getHour(), event.getEventEndDateToPersist().getTime().getMinute(),
                    0, 0, ISOChronology.getInstance(tz))
                    : null;
        }

        private static DateTimeZone getDateTimeZoneFromCmd(CalendarEntryCommandViewModel cmdVM) {
            // choose a timezone prioritised by incoming, existing, or London
            // NB event.getTimeZone() is transient! It does the same job as our cmd tzId but for the legacy controller
            return (cmdVM.getTzId() != null && cmdVM.getTzId().to != null)
                    ? DateTimeZone.forID(cmdVM.getTzId().to)
                    : DateTimeZone.forID("Europe/London");
        }

    }

}
