package com.ecco.webApi.serviceConfig;

import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nonnull;

/**
 * Base command for configuration operations on questionnaires
 */
public class OutcomeCloneCommandViewModel extends BaseCommandViewModel {

    private OutcomeViewModel outcomeViewModel;

    // we don't have a remove or update option, so for now, its always changed
    public boolean hasChanges() {
        return true;
    }

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected OutcomeCloneCommandViewModel() {
        super();
    }

    public OutcomeCloneCommandViewModel(@Nonnull OutcomeViewModel outcomeViewModel) {
        super(UriComponentsBuilder
                .fromUriString("config/outcome/clone/")
                .toUriString());
        this.outcomeViewModel = outcomeViewModel;
    }

    public OutcomeViewModel getOutcomeViewModel() {
        return outcomeViewModel;
    }

}
