package com.ecco.webApi.serviceConfig;

import com.ecco.serviceConfig.dom.OutcomeType;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.UUID;

/**
 * Base command for configuration operations on outcomes
 */
@Slf4j
public class OutcomeDefCommandViewModel extends BaseCommandViewModel {

    @Nonnull
    public String operation;
    @Nonnull
    public UUID outcomeDefUuid;
    @Nullable
    public ChangeViewModel<String> nameChange;

    // Distinguisher so its clear looking at the stored commands
    // what outcome it is - it could be this, the OutcomeThreatCommandViewModel,
    // or some OutcomeHr.
    // The url alone might not be as helpful to remember, or as reliable
    // when parsing audits to the screen.
    @Nullable
    public OutcomeType outcomeType;

    public boolean hasChanges() {
        return nameChange != null;
    }

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected OutcomeDefCommandViewModel() {
        super();
    }

    public OutcomeDefCommandViewModel(@Nonnull String operation, @Nonnull UUID outcomeDefUuid) {
        super(UriComponentsBuilder
                .fromUriString("config/outcomeDef/")
                .toUriString());
        this.operation = operation;
        this.outcomeDefUuid = outcomeDefUuid;
        this.outcomeType = OutcomeType.NEEDS; // default
    }

    public boolean valid() {

        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_ADD)) {
            if (this.outcomeType == null) {
                log.error("Required field: outcomeType");
                return false;
            }
        }

        return valid;
    }

}
