package com.ecco.webApi.rota;

import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.taskFlow.ServiceRecipientTaskCommandViewModel;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 'tasks' based commands for 'contract detail'
 */
@NoArgsConstructor(access = AccessLevel.PACKAGE)
public abstract class TaskContractDetailAbstractCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    @Nonnull
    public String operation;

    @Nullable
    public Integer contractId;

    @Nullable
    public ChangeViewModel<String> name;

    @Nullable
    public ChangeViewModel<LocalDateTime> startDateTime;

    @Nullable
    public ChangeViewModel<LocalDateTime> endDateTime;

    @Nullable
    public ChangeViewModel<String> PONumbers;

    @Nullable
    public ChangeViewModel<BigDecimal> agreedCharge;

    @Nullable
    public ChangeViewModel<Integer> contractTypeId;

    public TaskContractDetailAbstractCommandViewModel(@Nonnull String taskName, @Nullable String taskHandle) {
        super(taskName, taskHandle);
    }

}
