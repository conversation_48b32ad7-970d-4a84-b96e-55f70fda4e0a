package com.ecco.webApi.taskFlow;

import javax.annotation.Nonnull;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.Link;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.ecco.dao.AgencyRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Referral;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class ReferralTaskDeliveredByCommandHandler extends ServiceRecipientTaskCommandHandler<ReferralTaskEditDeliveredByCommandViewModel> {

    @PersistenceContext
    private EntityManager entityManager;

    @Nonnull
    private final ReferralRepository referralRepository;

    @Nonnull
    private final AgencyRepository agencyRepository;

    @Autowired
    public ReferralTaskDeliveredByCommandHandler(@Nonnull ObjectMapper objectMapper,
                                                 @Nonnull WorkflowTaskController workflowTaskController,
            @Nonnull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            @Nonnull AgencyRepository agencyRepository,
            @Nonnull ReferralRepository referralRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskEditDeliveredByCommandViewModel.class);

        this.agencyRepository = agencyRepository;
        this.referralRepository = referralRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, ServiceRecipientTaskParams params,
                                               ReferralTaskEditDeliveredByCommandViewModel vm) {
        Referral r = referralRepository.findByServiceRecipient_Id(params.serviceRecipientId);

        if (vm.deliveredBy != null) {
            r.setDeliveredBy(vm.deliveredBy.to == null ? null : agencyRepository.getOne(vm.deliveredBy.to.longValue()));
        }

        if (vm.deliveredByStartDate != null) {
            r.setDeliveredByStartDate(vm.deliveredByStartDate.to);
        }
        return null;
    }
}
