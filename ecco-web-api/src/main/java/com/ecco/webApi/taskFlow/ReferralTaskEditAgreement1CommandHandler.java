package com.ecco.webApi.taskFlow;

import com.ecco.dao.ClientRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dao.SignatureRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Referral;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.UUID;

@Component
public class ReferralTaskEditAgreement1CommandHandler extends ReferralTaskEditAgreementCommandHandler {

    @Autowired
    public ReferralTaskEditAgreement1CommandHandler(@Nonnull ObjectMapper objectMapper,
                                                    @Nonnull WorkflowTaskController workflowTaskController,
                                                    @Nonnull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                    @Nonnull ClientRepository clientRepository,
                                                    @Nonnull SignatureRepository signatureRepository,
                                                    @Nonnull ReferralRepository referralRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, clientRepository,
                signatureRepository, referralRepository, ReferralTaskEditAgreement1CommandViewModel.class);
    }

    protected void setAgreementDate(Referral r, DateTime utcDate) {
        r.setAgreementSigned(utcDate);
    }

    protected void setAgreementStatus(Referral r, Boolean status) {
        r.setAgreementStatus(status);
    }

    protected void setAgreementSignature(Referral r, UUID signatureId) {
        r.getServiceRecipient().setAgreementSignatureId(signatureId);
    }

}
