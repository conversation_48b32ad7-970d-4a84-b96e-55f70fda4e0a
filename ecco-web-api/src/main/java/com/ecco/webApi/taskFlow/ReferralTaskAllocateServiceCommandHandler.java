package com.ecco.webApi.taskFlow;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.*;
import com.ecco.infrastructure.util.FlagMap;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.serviceConfig.service.RepositoryBasedServiceTypeService;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.serviceConfig.viewModel.TaskDefinitionEntryViewModel;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Referral;
import com.ecco.dto.ServiceViewModel;
import com.ecco.security.SecurityUtil;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.webApi.evidence.ReferralFromViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.dom.contacts.Contact;

@Component
public class ReferralTaskAllocateServiceCommandHandler extends ServiceRecipientTaskCommandHandler<ReferralTaskAllocateServiceCommandViewModel> {

    @Nonnull
    private final ReferralRepository referralRepository;

    @Nonnull
    private final ReferralFromViewModel referralFromViewModel;
    private final ServiceCategorisationRepository serviceCategorisationRepository;

    @Nonnull
    private RepositoryBasedServiceTypeService serviceTypeService;
    @Nonnull
    private RepositoryBasedServiceCategorisationService serviceCategorisationService;

    @Nonnull
    private ServiceRepository serviceRepository;

    @Autowired
    public ReferralTaskAllocateServiceCommandHandler(
            ObjectMapper objectMapper,
            @Nonnull WorkflowTaskController workflowTaskController,
            RepositoryBasedServiceTypeService serviceTypeService,
            RepositoryBasedServiceCategorisationService serviceCategorisationService,
            ServiceRepository serviceRepository,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            FundingSourceRepository fundingSourceRepository,
            LocalAuthorityRepository localAuthorityRepository,
            ReferralRepository referralRepository,
            ProjectRepository projectRepository,
            IndividualRepository individualRepository,
            ListDefinitionRepository listDefinitionRepository,
            SignatureRepository signatureRepository,
            ClientRepository clientRepository,
            ServiceCategorisationRepository serviceCategorisationRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskAllocateServiceCommandViewModel.class);
        this.referralFromViewModel = new ReferralFromViewModel(fundingSourceRepository, localAuthorityRepository,
                serviceRepository, serviceCategorisationRepository, projectRepository, individualRepository,
                signatureRepository, clientRepository, listDefinitionRepository);
        this.referralRepository = referralRepository;
        this.serviceTypeService = serviceTypeService;
        this.serviceCategorisationService = serviceCategorisationService;
        this.serviceRepository = serviceRepository;
        this.serviceCategorisationRepository = serviceCategorisationRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, ServiceRecipientTaskParams params,
                                               ReferralTaskAllocateServiceCommandViewModel vm) {
        Referral r = referralRepository.findByServiceRecipient_Id(vm.serviceRecipientId);
        // we just trust the allocationIds are what is wanted
        for (Integer svcCatId : vm.allocationIds) {
            Referral allocated = createAllocatedReferral(r, svcCatId);
            referralRepository.save(allocated);
        }
        return null;
    }

    /**
     * Create Referral's according to the services/projects chosen
     * using the same client, and sensible referral defaults.
     * The intention here is NOT to copy details, but assume that the
     * subsequently configured services don't duplicate the tasks.
     * NB we don't copy other referral data - like contacts!
     */
    private Referral createAllocatedReferral(Referral r, int svcCatId) {
        ReferralViewModel rvm = new ReferralViewModel();
        rvm.clientId = r.getClient().getId();

        rvm.parentReferralId = r.getId();
        rvm.receivedDate = new LocalDate(DateTimeZone.UTC);

        // TODO we could do with using AgencyController.create using the ui-messages org name
        // so we find the company or create one, then add this user's contact to the list
        rvm.referrerAgencyId = null;
        rvm.referrerIndividualId = getUserContact().getId();

        rvm.serviceAllocationId = svcCatId;

        rvm.callAcceptOnService = true;

        // if no project has been specifically chosen from the view model then use a project from the referral's config
        var svcCat = serviceCategorisationService.getServiceCategorisation(svcCatId);
        var project = svcCat.getProjectId();
        if (project == null) {
            ServiceTypeViewModel refServiceType = serviceTypeService.findOneDto(r.loadConfigServiceTypeId());
            TaskDefinitionEntryViewModel taskDef = refServiceType.taskDefinitionEntries
                    .stream()
                    .filter(task -> task.name.equals("allocateToServices"))
                    .findFirst()
                    .orElse(null);
            if (taskDef != null) {
                // migrate the same project as the allocating referral
                boolean migrateProject = new FlagMap(taskDef.settings.get("migrateProperties")).containsKey("project");
                if (migrateProject && r.getServiceRecipient().getServiceAllocation().getProject() != null) {
                    var svcCatMatch = serviceCategorisationRepository.findOneByService_IdAndProject_Id(svcCat.getServiceId(), r.getServiceRecipient().getServiceAllocation().getProject().getId());
                    rvm.serviceAllocationId = svcCatMatch.getId();
                }
            }
        }

        var allocServiceTypeId = rvm.loadConfigServiceTypeId();
        var allocServiceType = serviceTypeService.findOneDto(allocServiceTypeId);
        rvm.currentTaskDefinitionIndex = Integer.toString(allocServiceType.getTaskCountToReferralView());

        return referralFromViewModel.apply(rvm);
    }

    private Contact getUserContact() {
        return SecurityUtil.getAuthenticatedUser().getContact();
    }

}
