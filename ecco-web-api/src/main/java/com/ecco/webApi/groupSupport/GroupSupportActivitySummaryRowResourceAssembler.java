package com.ecco.webApi.groupSupport;

import com.ecco.dao.GroupSupportActivitySummary;
import com.ecco.groupsupport.repositories.GroupSupportActivityRepository;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;

public class GroupSupportActivitySummaryRowResourceAssembler extends RepresentationModelAssemblerSupport<GroupSupportActivitySummary, GroupSupportActivitySummaryRowResource> {

    final GroupSupportActivityRepository repository;

    public GroupSupportActivitySummaryRowResourceAssembler(GroupSupportActivityRepository repository) {
        super(GroupSupportActivityController.class, GroupSupportActivitySummaryRowResource.class);
        this.repository = repository;
    }

    @Override
    public GroupSupportActivitySummaryRowResource toModel(GroupSupportActivitySummary a) {

        Long count = null;
        if (a.course) {
            count = this.repository.countByParentId(a.id);
        }
        GroupSupportActivitySummaryRowResource resource = new GroupSupportActivitySummaryRowResource(
            a.id,
            a.serviceRecipientId,
            a.uuid,
            a.description,
            a.startDateTime,
            a.capacity,
            a.duration,
            a.activityTypeId,
            a.course,
            a.parentId,
            count,
            a.venueId,
            a.venueName,
            a.serviceId,
            a.projectId,
            a.clientsInvited,
            a.clientsAttending,
            a.clientsAttended,
            a.endDate);

        return resource;
    }

}
