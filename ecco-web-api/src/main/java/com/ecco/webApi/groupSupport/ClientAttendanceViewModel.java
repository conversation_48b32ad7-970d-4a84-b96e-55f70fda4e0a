package com.ecco.webApi.groupSupport;

import com.ecco.dao.GroupSupportActivitySummary;

import java.util.List;
import java.util.UUID;
import javax.annotation.Nullable;

public class ClientAttendanceViewModel {

    @Nullable
    public GroupSupportActivitySummary parentActivity;

    public int serviceRecipientId;

    public boolean invited = true;
    public boolean attending;
    public boolean attended;

    /** comment that also gets recorded in evidence */
    public UUID supportWorkUuid;
    public String comment;
    public Integer typeId;

    /** optional **/
    public List<DailyAttendanceViewModel> dailyAttendances;
}
