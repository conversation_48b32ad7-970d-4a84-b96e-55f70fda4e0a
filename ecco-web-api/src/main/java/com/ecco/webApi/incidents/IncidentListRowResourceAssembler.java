package com.ecco.webApi.incidents;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.incidents.Incident;
import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.security.repositories.IndividualRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport;

// see also UserListRowResourceAssembler
public class IncidentListRowResourceAssembler extends RepresentationModelAssemblerSupport<Incident, IncidentViewModel> {

    public static final String REL_EDIT = "edit";

    private final IncidentToViewModel incidentToViewModel;
    protected final ObjectMapper objectMapper = ConvertersConfig.getObjectMapper();
    private final ListDefinitionRepository listDefinitionRepository;
    private final IndividualRepository individualRepository;

    public IncidentListRowResourceAssembler(ListDefinitionRepository listDefinitionRepository, IndividualRepository individualRepository) {
        super(IncidentListController.class, IncidentViewModel.class);
        this.listDefinitionRepository = listDefinitionRepository;
        this.individualRepository = individualRepository;
        this.incidentToViewModel = new IncidentToViewModel(listDefinitionRepository);
    }

    @Override
    public IncidentViewModel toModel(Incident input) {
        var result = incidentToViewModel.apply(input);

        result.serviceDescription = input.getServiceRecipient().getServiceAllocation().description();
        if (input.getSupportWorkerId() != null) {
            result.supportWorkerDisplayName = individualRepository.findOne(input.getSupportWorkerId()).getDisplayName();
        }

        result.significant = Incident.isSignificant(input, listDefinitionRepository, objectMapper);

        return result;

        //addEditLink(resource, vm);
    }

}
