package com.ecco.webApi.users;

import com.ecco.dom.ProjectAclId;
import com.ecco.dom.ServiceAclId;
import com.ecco.security.service.UserManagementService;
import com.ecco.users.commands.UserAclCommand;
import com.ecco.users.repository.UserCommandRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.acls.DummyPermission;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.security.dto.AclEntryDto;
import com.ecco.security.acl.AclHandler;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * Changes to acls of a user.
 */
@Component
public class UserAclCommandHandler extends BaseUserCommandHandler<UserAclCommandDto, UserAclCommand, UserParams> {

    @Nonnull
    UserManagementService userManagementService;

    @Nonnull
    AclHandler aclHandler;

    @Autowired
    public UserAclCommandHandler(
            @Nonnull ObjectMapper objectMapper,
            @Nonnull UserCommandRepository userCommandRepository,
            @Nonnull UserManagementService userManagementService,
            @Nonnull AclHandler aclHandler) {
        super(objectMapper, userCommandRepository, UserAclCommandDto.class);
        this.userManagementService = userManagementService;
        this.aclHandler = aclHandler;
    }

    @Override
    protected CommandResult handleInternal(@NotNull Authentication auth, UserParams params,
                                           @NotNull UserAclCommandDto dto) {

        String username = userManagementService.findUsernameFromId(params.userIdSubject);
        Set<AclEntryDto> aes = new HashSet<>();
        if (dto.services != null && dto.services.added != null) {
            for (var serviceId : dto.services.added) {
                AclEntryDto ae = new AclEntryDto(username, serviceId, ServiceAclId.class, new DummyPermission(1));
                aes.add(ae);
            }
        }
        if (dto.projects != null && dto.projects.added != null) {
            for (var projectId : dto.projects.added) {
                AclEntryDto ae = new AclEntryDto(username, projectId, ProjectAclId.class, new DummyPermission(1));
                aes.add(ae);
            }
        }
        aclHandler.addAclEntries(aes);

        aes = new HashSet<>();
        if (dto.services != null && dto.services.removed != null) {
            for (var serviceId : dto.services.removed) {
                AclEntryDto ae = new AclEntryDto(username, serviceId, ServiceAclId.class, new DummyPermission(1));
                aes.add(ae);
            }
        }
        if (dto.projects != null && dto.projects.removed != null) {
            for (var projectId : dto.projects.removed) {
                AclEntryDto ae = new AclEntryDto(username, projectId, ProjectAclId.class, new DummyPermission(1));
                aes.add(ae);
            }
        }
        aclHandler.removeAclEntries(aes);

        // TODO linkTo needs correcting
        return null; //LinkWithId.create(userIdSubject, linkToApi(methodOn(UserListController.class).self()).withRel(UserListRowResourceAssembler.REL_EDIT));
    }

    @Override
    protected UserAclCommand createCommand(Serializable targetId, UserParams params,
                                           String requestBody,
                                           UserAclCommandDto viewModel,
                                           long userId) {

        return new UserAclCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, params.userIdSubject);
    }

}
