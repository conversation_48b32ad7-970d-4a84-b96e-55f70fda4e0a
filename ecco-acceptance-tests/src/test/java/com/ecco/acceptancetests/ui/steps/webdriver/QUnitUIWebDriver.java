package com.ecco.acceptancetests.ui.steps.webdriver;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

import com.ecco.acceptancetests.steps.QUnitUI;
import com.ecco.acceptancetests.ui.pages.QUnitPage;

import org.openqa.selenium.WebDriver;

public class QUnitUIWebDriver extends WebDriverUI implements QUnitUI {

    public QUnitUIWebDriver(WebDriver webDriver) {
        super(webDriver);
    }

    @Override
    public void runQUnitTest(String relativePath) {
        QUnitPage page = new QUnitPage(webDriver, relativePath);
        page.verifyIsCurrentPage();

        assertThat(page.getTotalErrorCount(), is(0));
    }

}
