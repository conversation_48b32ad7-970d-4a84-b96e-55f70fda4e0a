package com.ecco.acceptancetests.api.userManagement;

import com.ecco.test.support.UniqueDataService;
import com.ecco.acceptancetests.steps.UserManagementSteps;
import com.ecco.acceptancetests.ui.pages.Role;
import com.ecco.data.client.actors.BaseActor;
import com.ecco.data.client.actors.UserActor;
import com.ecco.infrastructure.time.Clock;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Nonnull;
import java.util.Arrays;
import java.util.List;

import static java.util.stream.Collectors.toSet;

@SuppressWarnings("unused")
public class UserManagementStepsWebApi extends BaseActor implements UserManagementSteps {
    private final UserActor userActor;
    private Clock clock = Clock.DEFAULT;
    private DateTime now = clock.now();

    private final UniqueDataService unique = UniqueDataService.instance;

    public UserManagementStepsWebApi(RestTemplate restTemplate, UserActor userActor) {
        super(restTemplate);
        this.userActor = userActor;
    }

    @Nonnull
    @Override
    public String createUser(@Nonnull String username, @Nonnull Role... groups) {
        throw new UnsupportedOperationException("Not completed on Web API");
    }

    @Nonnull
    @Override
    public IndividualUserSummaryViewModel createIndividualWithUser(@Nonnull String username, @Nonnull String newPassword,
                                                                   @Nonnull String firstName, @Nonnull String lastName,
                                                                   @Nonnull Role ...groups) {
        return userActor.createIndividualWithUser(username, newPassword, firstName, lastName,
                Arrays.stream(groups).map(Role::name).collect(toSet()));
    }

    @Override
    public void createSharedUser(@NotNull String username, @NotNull Role... group) {
        throw new UnsupportedOperationException("Not completed on Web API");
    }

    @NotNull
    @Override
    public List<String> listUsers() {
        throw new UnsupportedOperationException("Not completed on Web API");
    }

    @NotNull
    @Override
    public List<String> listUsers(@NotNull String initial, @NotNull String groupName, boolean enabledOnly) {
        throw new UnsupportedOperationException("Not completed on Web API");
    }

    @NotNull
    @Override
    public List<String> listUserGroups() {
        throw new UnsupportedOperationException("Not completed on Web API");
    }

    @Override
    public void navigateBack() {
        throw new UnsupportedOperationException("Not appropriate on Web API");
    }

    @Override
    public void navigateToHome() {
        throw new UnsupportedOperationException("Not appropriate on Web API");
    }

    @Override
    public void navigateToWelcome() {
        throw new UnsupportedOperationException("Not appropriate on Web API");
    }

    @Override
    public void checkCanSeeText(@NotNull String text) {
        throw new UnsupportedOperationException("Not appropriate on Web API");
    }

    @Override
    public void checkCannotSeeText(@NotNull String text) {
        throw new UnsupportedOperationException("Not appropriate on Web API");
    }
}
