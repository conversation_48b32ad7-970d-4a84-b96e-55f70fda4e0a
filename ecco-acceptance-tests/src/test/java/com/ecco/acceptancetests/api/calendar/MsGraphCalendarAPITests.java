package com.ecco.acceptancetests.api.calendar;

import com.ecco.acceptancetests.api.BaseJsonTest;
import org.junit.Ignore;
import org.junit.Test;

/**
 * Just a placeholder for now, but allows us to note the config required
 */
public class MsGraphCalendarAPITests extends BaseJsonTest {

    /**
     * AZURE CALENDAR CONFIG
     * See ECCO-SNIPPETS.md "AZURE - config"
     */
    @Test
    @Ignore("only for local setup for now")
    public void setupAzureConfig() {
        // ensure localhost database has email address
        // -Dazure.calendar.enabled=true
        // -Decco.authn.loginProvidersOnly=false
        // NB this client-id is the ecco-app: https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/Overview/appId/b6176369-a85e-4a9d-af1e-5b790b3db245/isMSAApp~/false
        //      check the 'Redirect URI's include localhost
        // -Dazure.activedirectory.client-id=b6176369-a85e-4a9d-af1e-5b790b3db245
        // -Dazure.activedirectory.client-secret=<secret VALUE>
        // -Dcookie.insecure=true
        // -Dcookie.samesite=strict
    }

}
