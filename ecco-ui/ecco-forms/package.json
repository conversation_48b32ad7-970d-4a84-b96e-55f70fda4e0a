{"private": true, "name": "ecco-forms", "version": "0.0.0", "main": "./index.js", "typings": "./types/index.d.ts", "scripts": {"analyse": "webpack --json | webpack-bundle-size-analyzer", "clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts,.tsx .", "build": "eslint --ext .ts,.tsx . && webpack", "lint": "eslint --ext .ts,.tsx .", "test": "echo no tests"}, "dependencies": {"@eccosolutions/rjsf-core": "2.5.0-alpha.4", "@eccosolutions/rjsf-material-ui": "2.5.0-alpha.4", "markdown-to-jsx": "^6.11.4", "react": "16.13.1", "react-dom": "16.13.1"}, "devDependencies": {"@types/jest": "^26.0.24", "@types/markdown-to-jsx": "^6.11.0", "@types/react": "^16.9.19", "@types/react-dom": "^16.9.5", "@typescript-eslint/eslint-plugin": "^4.9.0", "@typescript-eslint/parser": "^4.9.0", "babel-loader": "^8.0.6", "esbuild": "^0.17.19", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "shx": "0.3.2", "terser-webpack-plugin": "^2.2.1", "ts-loader": "^5.3.3", "typescript": "5.1.5", "url-loader": "2.3.0", "webpack": "^4.42.1", "webpack-bundle-size-analyzer": "^3.1.0", "webpack-cli": "^3.2.3"}}