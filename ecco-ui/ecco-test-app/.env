SKIP_PREFLIGHT_CHECK=true
# For yarn start
#PUBLIC_URL=http://localhost:3000


# For everything else, there's Mastercard
PUBLIC_URL=_CONTEXT_PATH_/r/test

# To override locally put e.g. BROWSER=brave-browser and other overrides in .env.local (and don't commit it)
# for snap-based linux, override .env.local with BROWSER=chromium
BROWSER=chromium-browser

# Override these in .env.local - they must be provided else we try to login as "%REACT_APP_USERNAME%"
REACT_APP_USERNAME=
REACT_APP_PASSWORD=

# In .env.local you can set a username and password for the API client to automatically log in with using
# REACT_APP_USERNAME & REACT_APP_PASSWORD

# For CRA url-loader inline limit
IMAGE_INLINE_SIZE_LIMIT=81920