import * as React from "react"
import {CommandQueue, PendingStatusCommand} from "ecco-commands";

import {
    CommandSubform,
    dropdownList,
    possiblyModalForm,
    useCurrentServiceRecipientWithEntities,
    withCommandForm
} from "ecco-components";
import {ReferralSummaryWithEntities} from "ecco-dto";

export const PendingStatusDialog = (props: {serviceRecipientId: number, taskHandle: string, readOnly: boolean}) => {
    const {resolved, reload} = useCurrentServiceRecipientWithEntities()
    return withCommandForm(commandForm =>
            possiblyModalForm(
                    "pending status",
                    true, true,
                    () => commandForm.cancelForm(),
                    () => commandForm.submitForm().then(reload),
                    false, // TODO could emitChangesTo and see if there are any commands
                    props.readOnly,
                    <PendingStatus
                            referral={resolved.referral}
                            taskHandle={props.taskHandle}
                            commandForm={commandForm}
                            readOnly={props.readOnly}
                    />
            )
    );
};

interface Props {
    referral: ReferralSummaryWithEntities;
    taskHandle: string;
}

interface State {
    pendingStatus: number;
}

/** Pending status subform in modal */
export class PendingStatus extends CommandSubform<Props, State> {

    constructor(props) {
        super(props);
        let referral = this.props.referral;
        this.state = {
            pendingStatus: referral.pendingStatusId,
        };
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const cmd = new PendingStatusCommand(this.props.referral.serviceRecipientId, this.props.taskHandle)
            .changePendingStatusId(this.props.referral.pendingStatusId, this.state.pendingStatus);

        if(cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    render() {
        return dropdownList("pending", state => this.setState(state), this.state, "pendingStatus",
            this.props.referral.features.getPendingStatusList(), {});
    }
}