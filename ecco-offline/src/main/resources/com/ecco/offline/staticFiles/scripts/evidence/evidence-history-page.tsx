import $ = require("jquery");
import URI = require("URI");
import MvcUtils = require("../mvc/MvcUtils");
import {
    AsyncServiceRecipientWithEntities, DomElementContainer,
    ServiceRecipientWithEntitiesContext, useServiceRecipientWithEntities,
    useServicesContext
} from "ecco-components";
import * as React from "react";
import {mountWithServices} from "../offline/ServicesContextProvider";
import {FC, ReactElement} from "react";
import {Grid} from "@eccosolutions/ecco-mui";
import {BaseHistoryListControl} from "./BaseHistoryListControl";
import SupportHistoryListControl from "./support/SupportHistoryListControl";
import RiskHistoryListControl from "./risk/RiskHistoryListControl";


// expect /evidence-history/{entityId}
let uri = URI(window.location.href);
const pathParts = MvcUtils.getAppPathComponents(uri);
const entityId = parseInt(pathParts[pathParts.length - 1]);
const type = pathParts[pathParts.length - 2];


const PadInCenter: FC = (props) => {
    return <Grid container justify="center">
        <Grid item xs={12} md={8}>
            {props.children}
        </Grid>
    </Grid>
}

const HistoryWrapper: FC<{element: $.JQuery, srId: number}> = props => {
    const {sessionData} = useServicesContext();
    const {context} = useServiceRecipientWithEntities(props.srId);

    let historyCtl: BaseHistoryListControl<any, any>;

    if (type == "support") {
        const taskName = context.serviceType.getFirstSupportTaskName(sessionData);
        historyCtl = new SupportHistoryListControl(props.srId, taskName, undefined, undefined, undefined, false);
    }
    if (type == "risk") {
        const taskName = context.serviceType.getFirstRiskTaskName(sessionData);
        historyCtl = new RiskHistoryListControl(props.srId, taskName, undefined, undefined, false);
    }

    let Elm: ReactElement = null;
    if (historyCtl) {
        Elm = <DomElementContainer content={historyCtl.domElement()} />;
        historyCtl.load();
    }

    return <PadInCenter>
        {Elm}
    </PadInCenter>
};

class HistoryLoader {

    public static enhanceForPrinting($element: $.JQuery, srId: number) {
        mountWithServices(
            <AsyncServiceRecipientWithEntities srId={srId}>
                <AsyncServiceRecipientWithEntities.Resolved>
                    {(context: ServiceRecipientWithEntitiesContext) =>
                        <HistoryWrapper element={$element} srId={srId}/>
                    }
                </AsyncServiceRecipientWithEntities.Resolved>
            </AsyncServiceRecipientWithEntities>
            , $element[0]
        );
    }
}

HistoryLoader.enhanceForPrinting($("#main-content"), entityId);

$("#main-content-wrapper").css({"padding-left": "0px", "padding-right": "0px"})
$("body").css({'width': '210mm'});
$(".container").css({'width': '210mm'});
