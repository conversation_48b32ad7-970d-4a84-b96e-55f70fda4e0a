import ServiceProjectSelectionControl = require("../../entity-restrictions/ServiceProjectSelectionControl");
import $ = require("jquery");
import Lazy = require("lazy");
import _ = require("lodash");
import BaseTableRowControl = require("../../controls/BaseTableRowControl");

import PagedAsyncTableControl = require("../../controls/PagedAsyncTableControl");
import SelectList = require("../../controls/SelectList");
import SessionDataService = require("../../feature-config/SessionDataService");
import ProjectDto = spDto.ProjectDto;
import ServiceDto = spDto.ServiceDto;
import {EccoDate, LinkDto, StringToObjectMap} from "@eccosolutions/ecco-common";
import {apiClient, getGlobalEccoAPI, taskDueClass, taskDueStatus} from "ecco-components";
import {
    ContactsAjaxRepository,
    ReferralAjaxRepository,
    ResourceList,
    SessionData
} from "ecco-dto";
import {ReferralsListRow, ReferralStatusFields} from "ecco-dto/referral-dto";
import * as spDto from "ecco-dto/service-config-dto";

import {EntityRestrictionsAjaxRepository} from "../../entity-restrictions/EntityRestrictionsAjaxRepository";
import {attach} from "../../entity-restrictions/ServiceCategorisationSelect";
import {fullAddress} from "../../reports/tables/predefined-table-representations";

const restrictedEntityRepository = new EntityRestrictionsAjaxRepository(apiClient);
const repository = new ReferralAjaxRepository(apiClient);
const contactsRepository = new ContactsAjaxRepository(apiClient);

function isSLAEnabled(sessionData) {
    return sessionData.isEnabled("referral.list.slaTasks");
}

//noinspection JSUnusedLocalSymbols
function getHeadings(sessionData: SessionData) {

    const headings = [
        "r-id", "name", "from",
        {text: "to", help: "assigned service/project"},
        "received", "worker", "start", "status", "status summary"
    ];

    /*if (isSLAEnabled(sessionData)) {
        //headings.push("interviewer1Id");
        //headings.push("workerId");
        headings.push({text: "action required", help: "shows any overdue SLA tasks upto 'start on service'"});
    }*/

    return headings;
}

const statuses = [
    {id: "allNoDates", name: "all"}, // see ReferralStatusName.java and ReferralListFilter for what these do
    //{id: "created", name: "created"},
//    {id: "received", name: "received"},
    /*{id: "dueSlaTask", name: "due date"},*/
    {id: "ongoing", name: "ongoing"},
//    {id: "acceptedService", name: "accepted"},
    {id: "liveAtEnd", name: "live"}, // from reporting, make live be 'live at the end of the period'
    //{id: "movein", name: "move in"},
    {id: "incompleteAtEnd", name: "new"},
    {id: "waitingAtEnd", name: "waiting"},
    {id: "signposted", name: "signposted"},
    {id: "exited", name: "exited"}
    ];

function findHref(links: LinkDto[], rel: string) {
    if (!links)
        return null;
    let matches = links.filter( link => link.rel == rel );
    return matches.length == 0 ? null : matches[0].href;
}

export class RowControl extends BaseTableRowControl<ReferralsListRow> {

    constructor(private referral: ReferralsListRow, private servicesList: ServiceDto[],
            private openParent: boolean, private showSecondRow = false) {
        super(referral);
    }

    protected getColumnMapping(): StringToObjectMap<(dto: ReferralsListRow) => string|$.JQuery> {
        const messages = getGlobalEccoAPI().sessionData.getMessages()
        return {
            "r-id":     item => item.referralCode || item.referralId.toString(),
            "name":     item => {
                const link = this.openParent && findHref(item.links, "parent") ? findHref(item.links, "parent") : findHref(item.links, "edit");
                return $("<a>")
                    .attr("href", link)
                    .attr("title", "r-id: " + item.referralCode)
                    .text(item.clientDisplayName);
            },
            "from":     item => item.selfReferral ? "self referral"
                : item.agency || "individual",
            "to":       item => {
                const $to = $("<div>");
                $to.append(this.renderServiceDescription(item.serviceAllocationId));
                if (item.address) {
                    $to.append("<br/>").append($("<span>").append($("<small>").text(item.address)));
                }
                return $to;
            },
            "received": item => EccoDate.iso8601ToFormatShort(item.receivedDate),
            "start":    item => EccoDate.iso8601ToFormatShort(item.receivingServiceDate),
            "status":   item => messages[item.statusMessageKey],
            "status summary":   item => RowControl.summariseStatus(item),
            "workerId":   item => item.supportWorkerId != null ? item.supportWorkerId.toString() : null,
            "worker":   item => item.supportWorkerDisplayName,
            "interviewer1Id":   item => item.interviewer1ContactId != null ? item.interviewer1ContactId.toString() : null,
            "action required": item => this.summariseSLA(item)
        };
    }

    public secondRow() {
        if (this.showSecondRow && this.referral.statusMessageKey.indexOf("status.started") == 0) {
            return $("<tr>")
                .append( $("<td>") )
                .append( $("<td>").attr("colspan", "7")
                    .append($("<button>").addClass("btn btn-link")
                        .text("support plan")
                        .click( () => {
                            import("../../evidence/EvidenceDelegatingForm").then(({EvidenceDelegatingForm}) => {
                                EvidenceDelegatingForm.showInModalByIds(this.referral.serviceRecipientId, "support plan", "needsReduction");
                            })
                        })
                    ));
        }
        return null;
    }

    private summariseSLA(row: ReferralsListRow): $.JQuery {
        if (row.nextDueSlaDate == null) {
            return null;
        }
        const date = EccoDate.parseIso8601(row.nextDueSlaDate);

        let task: string;
        switch (row.nextDueTaskId) {
            case 25: // TODO: Should we use task name at web API
                task = 'setup initial contact';
                break;
            case 9:
                task = 'accept on service';
                break;
            case 26:
                task = 'start';
                break;
            case 27:
                task = 'needs assessment';
                break;
            default:
                task = row.nextDueTaskId.toString();
        }
        const cssClass = taskDueClass(taskDueStatus(date?.toDateTimeMidnight().formatIso8601())) || "";
        return $("<span>").addClass(cssClass).html(`${date.formatRelative()}<br>${task}`);
    }

    public static summariseStatus(item: ReferralStatusFields) {
        const messages = getGlobalEccoAPI().sessionData.getMessages()
        switch (item.statusMessageKey) {
            case "status.toStart":
            case "status.started":
            case "status.exited":
                return messages[item.statusMessageKey] + (item.exitedDate
                        ? " " + EccoDate.iso8601ToFormatShort(item.exitedDate) : "");

            case "status.signposted":
                return messages[item.statusMessageKey] + (item.decisionMadeOn
                        ? " " + EccoDate.iso8601ToFormatShort(item.decisionMadeOn) : "");

            case "status.forAssessment":
            case "status.incomplete":
                return messages[item.statusMessageKey];
        }
    }

    private renderServiceDescription(serviceAllocationId: number): $.JQuery {

        let svcCat = getGlobalEccoAPI().sessionData.getServiceCategorisation(serviceAllocationId);

        let projectHtml = "";
        if (svcCat && svcCat.projectId) {
            let project = this.getProject(this.servicesList, svcCat.projectId);
            // TODO: Get real project name from config (session data?). we don't have it in this list if it has been
            // removed from the service
            projectHtml = "<br> - " + (project ? project.name : "old project:" + svcCat.projectId.toString());
        }
        return $("<span>").html(svcCat && (svcCat.serviceName + projectHtml));
    }

    // mimic servicesprojects-dto.Services which we are replacing
    private getProject(services: ServiceDto[], projectId: number): ProjectDto {
        let allProjects = Lazy(services)
                .map(service => {
                    const svcCat = getGlobalEccoAPI().sessionData.getServiceCategorisationByIds(service.id, projectId);
                    return getGlobalEccoAPI().sessionData.getServiceCategorisationProjects(service.id, svcCat?.id || true)
                })
                .flatten<ProjectDto>().toArray();
        return _.find(allProjects, (p) => p.id == projectId);
    }
}


/**
 * referrals by status
 * no-args constructor allowed
 */
export class ReferralsListControl extends PagedAsyncTableControl<ReferralsListRow> {

    private sessionData: SessionData; // NOTE: filled in asynchronously - available for render
    private servicesList: ServiceDto[];

    private companyIdFilter: number;
    private clientGroupIdFilter: number;
    private serviceIdFilter: number;
    private projectIdFilter: number;
    private serviceGroupIdFilter: number;
    private statusKeyFilter: string = "ongoing";
    private visibleStatuses: {id: string, name: string}[];

    constructor() {
        super("referrals", "referrals-list ecco-rounded");
    }

    protected fetchViewData(): Promise<ReferralsListRow[]> {

        return repository.findAllReferrals(this.serviceIdFilter, this.projectIdFilter, this.statusKeyFilter,
                this.page ? this.page - 1 : null, this.pageSize)
            .then( referrals => {
                return this.populateSessionData(referrals);
            })
            .then( referrals => {
                return this.populateSupportWorker(referrals);
            })
            .then( referrals => {
                return this.populateAddress(referrals);
            })
            .then(referrals => {
                return this.populateSLA(referrals);
            })
            .then( referrals => {
                if (!this.servicesList) {
                    return restrictedEntityRepository.findRestrictedServicesProjects().then(servicesList => {
                        this.servicesList = servicesList;
                        return referrals;
                    });
                }
                else { return referrals; }
            })
            .then ( resourceList => {
                this.hasNext = resourceList.links.some( link => link.rel == "next" );
                this.numPages = resourceList.numPages;
                return resourceList.data;
            }); // Not ideal, but sufficient for now
    }

    private populateSessionData(referrals: ResourceList<ReferralsListRow>) {
        if (!this.sessionData) {
            return SessionDataService.getFeatures().then(sessionData => {
                this.sessionData = sessionData;
                return referrals;
            });
        } else {
            return referrals;
        }
    }

    private populateSLA(referrals: ResourceList<ReferralsListRow>) {
        this.visibleStatuses = isSLAEnabled(this.sessionData)
            ? statuses
            : statuses.filter(entry => entry.id != "dueSlaTask");
        return referrals;
    }

    private populateSupportWorker(referrals: ResourceList<ReferralsListRow>): Promise<ResourceList<ReferralsListRow>> {
        let contactIds = referrals.data.filter(rlw => rlw.supportWorkerId != null).map(rlw => rlw.supportWorkerId);
        //let interview1Ids = referrals.data.filter(rlw => rlw.supportWorkerId != null).map(rlw => rlw.supportWorkerId);
        return contactIds.length == 0
            ? Promise.resolve(referrals)
            : contactsRepository.findAllIndividualsByIndividualId(contactIds)
                .then(contacts => {
                    referrals.data.forEach(ref => {
                        if (ref.supportWorkerId) {
                            const contact = contacts
                                .filter(c => c.contactId == ref.supportWorkerId).pop();
                            if (contact) {
                                if (contact.firstName) {
                                    ref.supportWorkerDisplayName = contact.firstName;
                                }
                                if (contact.lastName) {
                                    ref.supportWorkerDisplayName = ' ' + contact.lastName;
                                }
                            }
                        }
                    });
                    return referrals;
                })
    }

    private populateAddress(referrals): Promise<ResourceList<ReferralsListRow>> {
        const addressFeature = this.sessionData.isEnabled("referral.list.clientAddress");
        if (!addressFeature) {
            return Promise.resolve(referrals);
        }

        let contactIds = referrals.data.filter(rlw => rlw.contactId != null).map(rlw => rlw.contactId);
        //let interview1Ids = referrals.data.filter(rlw => rlw.supportWorkerId != null).map(rlw => rlw.supportWorkerId);
        return contactIds.length == 0
            ? Promise.resolve(referrals)
            : contactsRepository.findAllIndividualsByIndividualId(contactIds)
                .then(contacts => {
                    referrals.data.forEach(ref => {
                        if (ref.contactId) {
                            const contact = contacts
                                .filter(c => c.contactId == ref.contactId).pop();
                            if (contact) {
                                ref.address = fullAddress(contact.address);
                            }
                        }
                    });
                    return referrals;
                })
    }

    protected getHeaders() { // OVERRIDES default impl
        return getHeadings(this.sessionData);
    }

    protected createRowControl(referral: ReferralsListRow) {
        const parameters = this.sessionData.getService(this.sessionData.getServiceCategorisation(referral.serviceAllocationId).serviceId).parameters;
        const openParent = parameters && parameters.openParentReferralInstead;
        return new RowControl(referral, this.servicesList, openParent);
    }

    protected getMenu() {
        const $menu = $("<div>")
            .addClass("text-center")

        if (this.sessionData.isEnabled("referral.list.service-group")) {
            // current usage of sessionData.getDto().restrictedServiceCategorisation
            this.attachServiceCategorisationSelector($menu);
        }
        else {
            this.attachServiceProjectSelector($menu);
        }

        const statusControl = new SelectList("referralStatusList", false);
        statusControl.change( (value: string) => {
            const changed = this.statusKeyFilter != value;

            this.statusKeyFilter = value;
            if (changed) {
                this.pushState(1, this.pageSize, "search");
            }
        });

        statusControl.element().addClass("inline");
//        control.load();
        $menu.append( $("<div>").addClass("text-center")
            .append($("<label>").addClass("e-label").text("status"))
            .append($("<span>").append(statusControl.element())) );

        statusControl.populateFromList(this.visibleStatuses,
            (item) => ({key: item.id, value: item.name}),
            (item) => item.id == this.statusKeyFilter);

/*        var $createLink = $("<a>new referral</a>");
        $createLink.click( () => {
            window.alert("to do");
//            ReferralActivityEditWithChartControl.showInModal(null, this.load.bind(this)));
        });
        $menu.append( $("<div>").addClass("text-left")
            .append($createLink) );
*/
        return $menu;
    }

    // if referral.list.service-group
    private attachServiceCategorisationSelector($menu: $.JQuery) {
        const el = document.createElement('p');
        $menu.append(el);
        attach(el, serviceCat => this.onServiceSelected(serviceCat));
    }

    private attachServiceProjectSelector($menu: $.JQuery) {
        const serviceProjectSelector = new ServiceProjectSelectionControl(this.servicesList,
            {
                serviceId: this.serviceIdFilter,
                projectId: this.projectIdFilter
            },
            (selection) => this.onServiceSelected(selection));
        serviceProjectSelector.element().find(".e-row").removeClass("e-row").addClass("text-center"); // Hack for now
        $menu.append($("<p>")
            .append(serviceProjectSelector.element().addClass("inline")));
    }

    private onServiceSelected(selection: {serviceId?: number, projectId?: number, companyId?: number,
                                clientGroupId?: number, serviceGroupId?: number}) {
        let changed = this.clientGroupIdFilter != selection.clientGroupId
            || this.serviceIdFilter != selection.serviceId
            || this.projectIdFilter != selection.projectId;
        this.serviceIdFilter = selection.serviceId;
        this.projectIdFilter = selection.projectId;
        this.companyIdFilter = selection.companyId;
        this.clientGroupIdFilter = selection.clientGroupId;
        this.serviceGroupIdFilter = selection.serviceGroupId;

        if (changed) {
            this.pushState(1, this.pageSize, "search");
        }
    }
}

export default ReferralsListControl;