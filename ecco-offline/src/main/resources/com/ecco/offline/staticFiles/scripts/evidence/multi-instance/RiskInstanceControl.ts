import $ = require("jquery");
import {Uuid} from "@eccosolutions/ecco-crypto";

import * as commands from "ecco-commands";
import {EvidenceDef, EvidenceContext, ActionInstanceFeatures, ActionInstanceControlData} from "ecco-evidence";
import InputGroup = require("../../controls/InputGroup");
import ListDefSelectList = require("../../data-attr/ListDefSelectList");

import {SmartStepStatus} from "ecco-dto";
import TextAreaInput = require("../../controls/TextAreaInput");
import {ActionComponent, ServiceTypeElement} from "ecco-dto";
import {ActionInstanceControl} from "../../evidence/evidenceControls";
import {SessionData} from "ecco-dto";
import SupportInstanceControl = require("../../evidence/multi-instance/SupportInstanceControl");



/**
 * Individual representation of a smart step
 */
class RiskInstanceControl extends SupportInstanceControl implements ActionInstanceControl {

    private enableValidateRiskMatrix = false;
    private enableValidateHazardIntervention = false;

    private riskLikelihoodControl: ListDefSelectList;
    private riskLikelihoodGroup: InputGroup;
    private riskSeverityControl: ListDefSelectList;
    private riskSeverityGroup: InputGroup;
    private hazardControl: TextAreaInput;
    private hazardGroup: InputGroup;
    private interventionControl: TextAreaInput;
    private interventionGroup: InputGroup;

    /**
     * Create a control for the smart step.
     */
    constructor(context: EvidenceContext, sessionData: SessionData, serviceRecipientId: number,
                evidenceDef: EvidenceDef, actionDef: ActionComponent,
                initialData: ActionInstanceControlData, controlUuid: Uuid,
                controlFeatures: ActionInstanceFeatures) {
        super(context, sessionData, serviceRecipientId, evidenceDef, actionDef, initialData, controlUuid, controlFeatures);
    }

    protected initialise() {
        super.initialise();
        const outcomeId = this.actionDef.getOutcome().getId();
        const taskName = this.context.evidenceDef.getTaskName();
        this.enableValidateRiskMatrix = this.context.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(taskName,  "showActionComponents", "riskMatrix", outcomeId)
            && !this.context.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(taskName,  "validateRiskMatrix", "allowBlank", outcomeId);

        this.enableValidateHazardIntervention = this.context.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(taskName,  "showActionComponents", "triggerControl", outcomeId)
            && this.context.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(taskName,  "validateHazardIntervention", "validate", outcomeId);

        this.updateLikelihood(this.initialData.likelihood);
        this.updateSeverity(this.initialData.severity);
        this.updateHazard(this.initialData.trigger);
        this.updateIntervention(this.initialData.control);
    }

    protected lookupSmartStepDefinition(id: number): ServiceTypeElement {
        return this.context.features.getRiskActionById(id);
    }

    /** change the state of the likelihood */
    private updateLikelihood(likelihood: number) {
        this.latest.likelihood = likelihood;
    }
    /** change the state of the severity */
    private updateSeverity(severity: number) {
        this.latest.severity = severity;
    }
    /** change the state of the hazard */
    private updateHazard(hazard: string) {

        // ensure consistenct between input of 'empty' to our null/undefined internal expectations
        if (hazard == "") {
            hazard = null;
        }

        // hazard update
        this.latest.hazard = hazard;
    }
    /** change the state of the intervention */
    private updateIntervention(intervention: string) {

        // ensure consistenct between input of 'empty' to our null/undefined internal expectations
        if (intervention == "") {
            intervention = null;
        }

        // hazard update
        this.latest.intervention = intervention;
    }

    protected createControls() {
        super.createControls();

        const taskName = this.context.evidenceDef.getTaskName();
        const outcomeId = this.actionDef.getOutcome().getId();

        // RISK MATRIX
        if (this.context.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(taskName, "showActionComponents", "riskMatrix", outcomeId)) {

            let $listElemLikelihood = $("<span>").attr("list-name", "riskMatrixLikelihood");
            this.riskLikelihoodControl = new ListDefSelectList($listElemLikelihood, undefined, () => {
            }, "form-control");
            this.riskLikelihoodControl.init(
                this.context.features,
                'riskMatrixLikelihood',
                this.latest.likelihood && this.latest.likelihood.toString(),
                true);
            this.riskLikelihoodGroup = new InputGroup("", this.riskLikelihoodControl, undefined, false);

            let $listElemSeverity = $("<span>").attr("list-name", "riskMatrixSeverity");
            this.riskSeverityControl = new ListDefSelectList($listElemSeverity, undefined, () => {}, "form-control");
            this.riskSeverityControl.init(
                this.context.features,
                'riskMatrixSeverity',
                this.latest.severity && this.latest.severity.toString(),
                true);
            this.riskSeverityGroup = new InputGroup("", this.riskSeverityControl, undefined, false);

            this.setupValidateRiskMatrix(false);
        }

        // TRIGGER / CONTROL
        if (this.context.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(taskName, "showActionComponents", "triggerControl", outcomeId)) {

            const messages = this.context.features.getMessages()
            let tapToEdit = this.context.features.isEnabled("support.evidence.tapToEditTextAreas");
            this.hazardControl = new TextAreaInput(null, 2, tapToEdit, "form-control")
                .placeholderText(messages["threat.triggerControl.hazard.label"]);
            this.hazardControl.setVal(this.latest.hazard);
            this.hazardGroup = new InputGroup("", this.hazardControl, undefined, false);

            this.interventionControl = new TextAreaInput(null, 2, tapToEdit, "form-control")
                .placeholderText( messages["threat.triggerControl.intervention.label"]);
            this.interventionControl.setVal(this.latest.intervention);
            this.interventionGroup = new InputGroup("", this.interventionControl, undefined, false);

            this.setupValidateHazardIntervention(false);
        }

    }

    protected renderControls() {
        super.renderControls();

        let $containerInner = this.controlFeatures.isAllowFromBlankUi() ? this.getDrawContainer() : this.getContainer();

        // original ui hid the elements, then exposed through showComponents
        if (this.controlFeatures.isAllowFromBlankUi()) {
            this.riskLikelihoodGroup && this.riskLikelihoodGroup.element().hide();
            this.riskSeverityGroup && this.riskSeverityGroup.element().hide();
            this.hazardControl && this.hazardControl.element().hide();
            this.interventionControl && this.interventionControl.element().hide();
        }

        // RISK MATRIX
        if (this.riskLikelihoodControl) {
            $containerInner.append(this.riskLikelihoodGroup.element());
            $containerInner.append(this.riskSeverityGroup.element());
        }

        // TRIGGER / CONTROL
        if (this.hazardControl) {
            $containerInner.append(this.hazardGroup.element());
            $containerInner.append(this.interventionGroup.element());
        }

        // if no active status on this smart step, then hide the elements
        if (!this.latest.status) {
            this.hideComponents();
        }

        // original ui hid the elements, then exposed through showComponents
        if (this.controlFeatures.isAllowFromBlankUi()) {
            this.showComponents();
        }
    }

    private hideComponents() {
        this.riskLikelihoodControl && this.riskLikelihoodControl.element().hide();
        this.riskLikelihoodGroup && this.riskLikelihoodGroup.element().hide();
        this.riskSeverityControl && this.riskSeverityControl.element().hide();
        this.riskSeverityGroup && this.riskSeverityGroup.element().hide();
        this.hazardControl && this.hazardControl.element().hide();
        this.hazardGroup && this.hazardGroup.element().hide();
        this.interventionControl && this.interventionControl.element().hide();
        this.interventionGroup && this.interventionGroup.element().hide();
    }

    protected showComponents(fadeIn = 500) {
        super.showComponents(fadeIn);
        this.riskLikelihoodControl && this.riskLikelihoodControl.element().fadeIn(fadeIn);
        this.riskLikelihoodGroup && this.riskLikelihoodGroup.element().fadeIn(fadeIn);
        this.riskSeverityControl && this.riskSeverityControl.element().fadeIn(fadeIn);
        this.riskSeverityGroup && this.riskSeverityGroup.element().fadeIn(fadeIn);
        this.hazardControl && this.hazardControl.element().fadeIn(fadeIn);
        this.hazardGroup && this.hazardGroup.element().fadeIn(fadeIn);
        this.interventionControl && this.interventionControl.element().fadeIn(fadeIn);
        this.interventionGroup && this.interventionGroup.element().fadeIn(fadeIn);
    }

    protected refreshIntervention() {
        this.interventionControl.setVal(this.latest.intervention);
    }

    protected populateGoalUpdateCommand(cmd: commands.GoalUpdateCommand) {
        super.populateGoalUpdateCommand(cmd);
        // once likelihood and severity are set, they can't be undone (as per old ui) so -1 '-' isn't processed
        if (this.riskLikelihoodControl && this.riskLikelihoodControl.getSelectedId()
            && this.riskSeverityControl && this.riskSeverityControl.getSelectedId()) {
            cmd.changeLikelihood(this.initialData.likelihood, this.riskLikelihoodControl.getSelectedId());
            cmd.changeSeverity(this.initialData.severity, this.riskSeverityControl.getSelectedId());
        }
        if (this.hazardControl && this.interventionControl) {
            cmd.changeHazard(this.initialData.trigger, this.hazardControl.val());
            cmd.changeIntervention(this.initialData.control, this.interventionControl.val());
        }
    }

    /** True if required fields are set */
    public isValid(): boolean {
        let valid = super.isValid();

        if (this.enableValidateRiskMatrix) {
            if (!this.riskLikelihoodGroup.isValid() && this.riskSeverityGroup.isValid()) {
                valid = false;
            }
        }
        if (this.enableValidateHazardIntervention) {
            if (!this.hazardGroup.isValid() || !this.interventionGroup.isValid()) {
                valid = false;
            }
        }

        return valid;
    }

    public nextStatusUI(): void {
        super.nextStatusUI();
        this.setupValidateRiskMatrix(true);
        this.setupValidateHazardIntervention(true);
    }

    private setupValidateRiskMatrix(uiTriggered: boolean) {
        // if matrix is on, see if we have all validation (!allowBlank)
        // which enforces both values if a new action is wanted
        if (this.enableValidateRiskMatrix) {

            // if we already have values, and we need validation - then enforce
            if (!uiTriggered) {
                if (this.riskLikelihoodControl.getSelectedId() != null ||
                    this.riskSeverityControl.getSelectedId() != null) {
                    this.riskLikelihoodGroup.enableValidation();
                    this.riskSeverityGroup.enableValidation();
                }

            // otherwise we are triggered from selecting (or deselecting) a smart step
            } else {
                // reset to initial state - so disable validation
                // TODO we should reset the matrix values also, otherwise
                // its possible to save with just one of the matrix values set
                // without selecting the smart step. However, the logic above
                // ensures that the next time validation is triggered
                if (this.latest.status == this.initialData.status) {
                    this.riskLikelihoodGroup.disableValidation();
                    this.riskSeverityGroup.disableValidation();

                // enable validation if we include a new smart step
                } else if (this.latest.status == SmartStepStatus.WantToAchieve) {
                    this.riskLikelihoodGroup.enableValidation();
                    this.riskSeverityGroup.enableValidation();
                }
            }
        }
    }

    private setupValidateHazardIntervention(uiTriggered: boolean) {
        // if config is on, see if we have all validation (!allowBlank)
        // which enforces both values if a new action is wanted
        if (this.enableValidateHazardIntervention) {

            // if we already have values, and we need validation - then enforce
            if (!uiTriggered) {
                if ((this.hazardControl.getValueOrNull(this.initialData.trigger != null && this.initialData.trigger != "") != null) ||
                    (this.interventionControl.getValueOrNull(this.initialData.control != null && this.initialData.control != "")) != null) {
                    this.hazardGroup.enableValidation();
                    this.interventionGroup.enableValidation();
                }

                // otherwise we are triggered from selecting (or deselecting) a smart step
            } else {
                // reset to initial state - so disable validation
                if (this.latest.status == this.initialData.status) {
                    this.hazardGroup.disableValidation();
                    this.interventionGroup.disableValidation();

                    // enable validation if we include a new smart step
                } else if (this.latest.status == SmartStepStatus.WantToAchieve) {
                    this.hazardGroup.enableValidation();
                    this.interventionGroup.enableValidation();
                }
            }
        }
    }

}
export = RiskInstanceControl;