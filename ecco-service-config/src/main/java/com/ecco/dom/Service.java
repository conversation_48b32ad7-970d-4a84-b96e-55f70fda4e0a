package com.ecco.dom; // NOTE: If you change the package, a database script to update ACLs will be needed

import static java.util.stream.Collectors.toList;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dto.ServiceViewModel;
import com.ecco.infrastructure.entity.ConfigurableLongKeyedEntity;

import org.hibernate.annotations.Type;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.serviceConfig.dom.ServiceTypeMinimalView;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.serviceConfig.viewModel.ProjectViewModel;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationToProjectViewModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.*;

import javax.annotation.PostConstruct;
import javax.persistence.*;

/**
 * Service provided by an organization, such as "Probation", "Mental Health", etc.
 *
 * <p>Things that are per-Service vs per-ServiceType are:<ul>
 * <li>The service name</li>
 * <li>Projects (incl for access control)</li>
 * <li>GroupSupportActivityType</li>
 * <li>Parameters (stored in database as JSON)</li>
 * </ul>
 */
@Entity
@Table(name = "services")
@Getter
@Setter
@NoArgsConstructor
public class Service extends ConfigurableLongKeyedEntity implements MutableIdName<Long> {

    public static final String PARAM_EMAIL = "email.notification";
    public static final String PARAM_INTEGRATION_NOTETYPE = "externalNoteType";

    public static ServiceCategorisationToProjectViewModel scToViewModel;

    static {
        // If you change the package, a database script to update ACLs will be needed.
        Assert.state(Service.class.getName().equals("com.ecco"+".dom.Service")); // + avoids refactor tools changing this
    }

    @Autowired
    @Transient
    private ListDefinitionRepository listDefinitionRepository;


    @PostConstruct
    public void init() {
        scToViewModel = new ServiceCategorisationToProjectViewModel();
    }

    private static final long serialVersionUID = 1L;

    @Autowired
    @Transient
    private transient ServiceTypeService serviceTypeService;

    @Column
    private String name;

    @Column
    private boolean disabled;

    @Column
    @Type(type = "com.ecco.infrastructure.hibernate.JSONUserTypeStringToObjectMap")
    private HashMap<String, Object> parameters = new HashMap<>();

    @Column(name="servicetypeId", updatable = false, insertable = false)
    private Long serviceTypeId;

    // needed by ReferralPredicate .serviceType.hideOnList
    // which could be changed or moved to the service
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "servicetypeId")
    private ServiceTypeMinimalView serviceType;

    /**
     * We should look to cache this, but we'd have to check the concurrency map problems
     * we had last time of Hibernate proxy's - see ECCO-1408, though we could try
     * caching the result of Hibernate.initialize(serviceType).
     *
     * Alternatively we could return a cached view model using an interface to share
     * the JPA definition (with targetEntity=ServiceTypeMinimalView) and here to
     * return the view model. We'd then need to match the method names from the
     * view model to each entity (eg OutcomeThreat.weighting in RiskAreaViewModel)
     * so that jsp pages were happy - code changes would be a smaller task
     */
    /*public ServiceTypeViewModel loadServiceType() {
        return serviceTypeId == null ? null : serviceTypeService.findOneDto(serviceTypeId.intValue());
    }*/

    public void setServiceType(ServiceTypeMinimalView serviceType) {
        Assert.notNull(serviceType.getId());
        this.serviceType = serviceType;
        // TODO: Sort out how we go about this - can we assign persisted ServiceType to ServiceTypeMinimalView if it is subclass of it...
//        Assert.state(false, "This function is not yet supported.");
    }

    // NB ironically, categorisations can return null Project, unlike getProjects below
    @OneToMany(fetch = FetchType.EAGER, mappedBy = "service", cascade = CascadeType.PERSIST)
    private List<ServiceCategorisation> categorisations = new ArrayList<>();


    // this is needed as-is for ServiceConfigConfig.serviceAclExtractor
    public Service(Long id) {
        setId(id);
    }

    public Service(String name) { setName(name); }

    public String getParameterAsString(String key) {
        return Service.getParameterAsString(parameters, key);
    }
    public static String getParameterAsString(Map<String, Object> parameters, String key) {
        return parameters == null ? null
                : parameters.get(key) == null ? null
                : parameters.get(key).toString();
    }

    /** We need to ensure that we can use this service even with no projects associated with it, so we
     * create a categorisation that allows us to assign a ServiceRecipient to this service without a project */
    @PrePersist
    void ensureAtLeastOneServiceCatorisationExists() {
        if (categorisations.isEmpty()) {
            categorisations.add(new ServiceCategorisation(this, null));
        }
    }

    @Transient
    public List<Project> getProjects() {
        return categorisations.stream()
                .filter(sc -> sc.getProject() != null)
                .map(ServiceCategorisation::getProject)
                .sorted(Comparator.comparing(Project::getName))
                .collect(toList());
    }

    @Transient
    private List<ProjectViewModel> getProjectsViewModel() {
        return categorisations.stream()
                .filter(sc -> sc.getProject() != null)
                .map(scToViewModel)
                .collect(toList());
    }

    // useful for debugging
    @Override
    public String toString() {
        return "service: " + super.toString();
    }

    /**
     * service-config version of ServiceViewModel
     */
    public ServiceViewModel toViewModel() {
        ServiceViewModel s = ServiceViewModel.builder()
                .id(this.getId().intValue())
                .name(this.getName())
                .serviceTypeId(this.getServiceTypeId())
//                .serviceType(this.serviceTypeService.findOneDto(this.getServiceTypeId().intValue()))
                .projects(getProjectsViewModel())
                .parameters(this.parameters)
                .disabled(this.disabled)
                .build();

        // NOT IMPLEMENTED
        //s.staffWithAccess

        return s;
    }

}
