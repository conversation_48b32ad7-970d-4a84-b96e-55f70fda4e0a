package com.ecco.serviceConfig.repositories;

import com.ecco.dom.Service;
import com.ecco.dom.ServiceAclId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ServiceRepository extends JpaRepository<Service, Long> {

    Service findOneByName(String name);

    @Query("SELECT s.id from Service s")
    List<Long> findAllServiceIds();

    @Query("SELECT s.serviceTypeId from Service s where s.id = :id")
    long findServiceTypeId(long id);

    // perf - use cache instead?
    @Query("SELECT sc.service from ServiceCategorisation sc where sc.id = :serviceCategorisationId")
    Service findOneByServiceCategorisationId(int serviceCategorisationId);

    @Query("SELECT new com.ecco.dom.ServiceAclId(s.id, s.name) from Service s")
    List<ServiceAclId> findAllAclIds();
}
