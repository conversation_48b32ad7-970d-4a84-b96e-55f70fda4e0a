package com.ecco.serviceConfig.repositories;

import org.springframework.data.jpa.repository.JpaRepository;

import com.ecco.serviceConfig.dom.ServiceCategorisation;
import org.springframework.data.jpa.repository.Query;

public interface ServiceCategorisationRepository extends JpaRepository<ServiceCategorisation, Integer> {

    ServiceCategorisation findOneByService_IdAndProject_Id(long serviceId, Long projectId);

    //@Query("SELECT sc.service.serviceTypeId from ServiceCategorisation sc where sc.id = :serviceCategorisationId")
    @Query(nativeQuery = true, value="SELECT s.servicetypeid FROM services_projects sp inner join services s on sp.serviceId=s.id where sp.id=?1")
    int findServiceTypeId(long id);

}
