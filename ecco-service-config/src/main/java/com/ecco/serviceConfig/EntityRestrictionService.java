package com.ecco.serviceConfig;

import java.util.List;

import javax.annotation.Nonnull;

import com.ecco.dom.ProjectAclId;
import com.ecco.dom.ServiceAclId;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import org.springframework.security.access.AccessDeniedException;

@ReadOnlyTransaction
public interface EntityRestrictionService {

    /**
     * Instance method to get restricted services and projects DTO.
     * The loading of services does include ServiceTypeMinimalView but this is minimal load since the heavy stuff
     * for jsp is lazy.
     */
    @Nonnull
    default ServicesProjectsDto getRestrictedServicesProjectsDto(RepositoryBasedServiceCategorisationService svcCatsService) {
        // Now we can access the Hibernate session since this is an instance method
        // called on a Spring-managed bean with proper transactional context
        var restrictedProjects = getRestrictedProjectIds();
        var restrictedServices = getRestrictedServiceIds();
        var svcCatsAll = svcCatsService.getServiceCategorisationViewModels();
        return new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCatsAll);
    }

    /**
     * Instance method to verify access for a service recipient.
     */
    default void verifyAccess(int srId, long serviceId, Long projectId, RepositoryBasedServiceCategorisationService svcCatService) {
        ServicesProjectsDto restrictions = getRestrictedServicesProjectsDto(svcCatService);
        // var access = projectId == null ? restrictions.canAccess(serviceId) : restrictions.canAccess(serviceId, projectId);
        if (restrictions.canAccess(serviceId, projectId)) {
            return; // full access to this referral
        }
        // DON'T check the parentReferral anymore, by now the user will need permission for both anyway
        throw new AccessDeniedException("access denied for entity id: " + srId);
    }

    /**
     * Return the services the user is allowed access to - restricted to those which form part of the referral process
     * NB this only applies when -DenableAcls is on because this method is a configured for filtering through spring security
     * @return services the user is allowed access to
     */
    List<ServiceAclId> getRestrictedServiceIds();

    /**
     * Return the projects the user is allowed access to.
     * A project with id -1 is used to indicate all projects (for convenience) and no project for when a referral isn't yet assigned - see impl.
     *
     * NB this only applies when -DenableAcls is on because this method is a configured for filtering through spring security
     */
    List<ProjectAclId> getRestrictedProjectIds();

    /**
     * Ensures that the classes we use (Service/Project) all have entries in the ACL tables
     * otherwise significant errors can occur for users
     */
    void ensureAcls();

}
