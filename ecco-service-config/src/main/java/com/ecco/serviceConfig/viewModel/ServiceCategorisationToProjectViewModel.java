package com.ecco.serviceConfig.viewModel;

import com.ecco.dom.Project;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import lombok.RequiredArgsConstructor;

import javax.annotation.Nonnull;
import java.util.function.Function;

@RequiredArgsConstructor
public class ServiceCategorisationToProjectViewModel implements Function<ServiceCategorisation, ProjectViewModel> {

    @Override
    public ProjectViewModel apply(@Nonnull ServiceCategorisation input) {

        if (input.getProject() == null) {
            return null;
        }

        ProjectViewModel result = new ProjectViewModel();
        Project project = input.getProject();
        result.id = project.getId() == null ? null : project.getId().intValue();
        result.name = project.getName();

        if (project.getRegion() != null) {
            result.regionName = project.getRegion().getName();
            result.regionId = project.getRegion().getId().intValue();
        }

        return result;
    }
}
