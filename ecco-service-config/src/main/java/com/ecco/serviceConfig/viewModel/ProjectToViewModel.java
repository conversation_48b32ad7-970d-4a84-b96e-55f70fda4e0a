package com.ecco.serviceConfig.viewModel;

import com.ecco.dom.Project;

import javax.annotation.Nullable;
import java.util.function.Function;

public class ProjectToViewModel implements Function<Project, ProjectViewModel> {

    @Nullable
    @Override
    public ProjectViewModel apply(@Nullable Project input) {
        if (input == null) {
            throw new NullPointerException("input Project must not be null");
        }

        ProjectViewModel result = new ProjectViewModel();
        result.id = input.getId() == null ? null : input.getId().intValue();
        result.name = input.getName();

        if (input.getRegion() != null) {
            result.regionName = input.getRegion().getName();
            result.regionId = input.getRegion().getId().intValue();
        }

        return result;
    }
}
