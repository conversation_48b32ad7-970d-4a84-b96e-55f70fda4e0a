package com.ecco.serviceConfig.service;

import com.ecco.dom.Project;
import com.ecco.dom.Service;
import com.ecco.dto.ServiceViewModel;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.config.root.CacheConfig;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.serviceConfig.viewModel.ProjectToViewModel;
import com.ecco.serviceConfig.viewModel.ProjectViewModel;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationToViewModel;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import java.util.List;

@org.springframework.stereotype.Service("serviceCategorisationService")
@ReadOnlyTransaction
public class RepositoryBasedServiceCategorisationService {

    private final ServiceRepository serviceRepository;
    private final ProjectRepository projectRepository;
    private final ServiceCategorisationRepository serviceCategorisationRepository;
    private final ServiceCategorisationToViewModel serviceCategorisationToViewModel;
    private final ProjectToViewModel projectToViewModel = new ProjectToViewModel();

    @Autowired
    public RepositoryBasedServiceCategorisationService(ServiceCategorisationRepository serviceCategorisationRepository,
                                                       ServiceRepository serviceRepository,
                                                       ProjectRepository projectRepository,
                                                       ServiceCategorisationToViewModel serviceCategorisationToViewModel) {
        super();
        this.serviceCategorisationRepository = serviceCategorisationRepository;
        this.serviceRepository = serviceRepository;
        this.projectRepository = projectRepository;
        this.serviceCategorisationToViewModel = serviceCategorisationToViewModel;
    }

    @Cacheable(value=CacheConfig.CACHE_SVCCAT, key="'_all_'")
    public List<ServiceCategorisationViewModel> getServiceCategorisationViewModels() {
        return serviceCategorisationRepository.findAll().stream()
            .map(serviceCategorisationToViewModel)
            .toList(); // fails with proxy objects
    }

    @Cacheable(value= CacheConfig.CACHE_SVCCAT)
    public ServiceCategorisationViewModel getServiceCategorisation(int svcCatId) {
        return serviceCategorisationRepository.findById(svcCatId)
            .map(serviceCategorisationToViewModel)
            .orElseThrow();
    }

    @Cacheable(CacheConfig.CACHE_SERVICE)
    public ServiceViewModel getService(Long id) {
        return serviceRepository.findById(id)
            .map(Service::toViewModel)
            .orElseThrow();
    }

    @Cacheable(CacheConfig.CACHE_PROJECT)
    public ProjectViewModel getProject(Long id) {
        return projectRepository.findById(id)
            .map(projectToViewModel)
            .orElseThrow();
    }
}
