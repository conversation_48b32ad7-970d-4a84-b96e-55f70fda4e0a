package com.ecco.test.entities.hibernate;

import java.io.InputStream;
import java.util.Date;
import org.apache.commons.lang.time.DateUtils;
import org.dbunit.dataset.IDataSet;
import org.dbunit.dataset.ReplacementDataSet;
import org.dbunit.dataset.xml.FlatXmlDataSet;
import org.dbunit.dataset.xml.FlatXmlDataSetBuilder;
import org.springframework.core.io.Resource;

import com.github.springtestdbunit.dataset.AbstractDataSetLoader;

/**
 * As per FlatXMLDataSetLoader except with autoSensing which uses all the columns in the file,
 * plus replaces [date:now] and [date:yesterday] with {@link Date} for now and 1 day ago respectively.
 */
public class AllColumnsDateSubstitutingDataSetLoader extends AbstractDataSetLoader {

    @Override
    protected IDataSet createDataSet(Resource resource) throws Exception {

        FlatXmlDataSetBuilder builder = new FlatXmlDataSetBuilder();
        builder.setColumnSensing(true);
        InputStream inputStream = resource.getInputStream();
        try {
            FlatXmlDataSet dataSet = builder.build(inputStream);
            ReplacementDataSet rDataSet = new ReplacementDataSet(dataSet);
            rDataSet.addReplacementObject("[date:now]", new Date());
            rDataSet.addReplacementObject("[date:yesterday]", DateUtils.addDays(new Date(), -1));
            return rDataSet;
        } finally {
            inputStream.close();
        }
    }

}
