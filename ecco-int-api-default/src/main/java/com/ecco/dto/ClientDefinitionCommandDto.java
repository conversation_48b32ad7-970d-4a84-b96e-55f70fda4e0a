package com.ecco.dto;

import org.joda.time.LocalDate;

/**
 * Used to update an external system.
 * Based on ReferralTaskClientDetailCommandViewModel.
 * Also matches fields in ClientDefinition (except 'code')
 */
public class ClientDefinitionCommandDto {
    public String externalClientRef;
    public ChangeViewModel<String> firstName;
    public ChangeViewModel<String> lastName;
    public ChangeViewModel<String> genderKey;
    public ChangeViewModel<String> genderAtBirthKey;
    public ChangeViewModel<LocalDate> birthDate;
    // TODO if wanted to update external system
    //  code
    public ChangeViewModel<String> ni;
    public ChangeViewModel<String> firstLanguageKey;
    public ChangeViewModel<String> ethnicOriginKey;
    public ChangeViewModel<String> nationalityKey;
    public ChangeViewModel<String> maritalStatusKey;
    public ChangeViewModel<String> religionKey;
    public ChangeViewModel<String> disabilityKey;
    public ChangeViewModel<String> sexualOrientationKey;
    public ChangeViewModel<String[]> address;
    public ChangeViewModel<String> postCode;
    public ChangeViewModel<String> town;
    public ChangeViewModel<String> county;
    public ChangeViewModel<String> phoneNumber;
    public ChangeViewModel<String> mobileNumber;
    public ChangeViewModel<String> email;
}
