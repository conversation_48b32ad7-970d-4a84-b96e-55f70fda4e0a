package com.ecco.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

/**
 * Provides information about a client support event (i.e. completion of a referral task) to use as a notification.
 *
 * @since 19/12/14
 */
@JsonDeserialize(as = ClientSupportEvent.Impl.class)
public interface ClientSupportEvent extends BuildableDto<ClientSupportEvent> {
    /** The external system name. */
    String getExternalClientSource();

    /** The reference to the client in the external system. */
    String getExternalClientRef();

    /** The local referral code for this notification. */
    String getLocalReferralCode();

    /** The support event type that occurred. */
    String getEventType();

    /** The client's support worker. */
    String getLocalSupportWorkerCode();

    /** The client's support worker. */
    String getLocalSupportWorkerName();

    interface Builder extends DtoBuilder<ClientSupportEvent> {
        Builder externalClientSource(String externalClientSource);
        Builder externalClientRef(String externalClientRef);
        Builder localReferralCode(String localReferralCode);
        Builder eventType(String eventType);
        Builder localSupportWorkerCode(String localSupportWorkerCode);
        Builder localSupportWorkerName(String localSupportWorkerName);
    }

    final class BuilderFactory {
        public static Builder create() {
            return ProxyDtoBuilderProxy.newInstance(Builder.class, ClientSupportEvent.class);
        }

        public static Builder create(ClientSupportEvent template) {
            return DtoBuilderProxy.newInstance(Builder.class, template);
        }
    }

    @SuppressWarnings("UnusedDeclaration")
    final class Impl implements ClientSupportEvent {
        private String externalClientSource;
        private String externalClientRef;
        private String localReferralCode;
        private String eventType;
        private String localSupportWorkerCode;
        private String localSupportWorkerName;

        @Override
        public String getExternalClientSource() {
            return externalClientSource;
        }

        @Override
        public String getExternalClientRef() {
            return externalClientRef;
        }

        @Override
        public String getLocalReferralCode() {
            return localReferralCode;
        }

        @Override
        public String getEventType() {
            return eventType;
        }

        @Override
        public String getLocalSupportWorkerCode() {
            return localSupportWorkerCode;
        }

        @Override
        public String getLocalSupportWorkerName() {
            return localSupportWorkerName;
        }
    }

}
