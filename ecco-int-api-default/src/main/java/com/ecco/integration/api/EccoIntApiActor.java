package com.ecco.integration.api;

import java.lang.reflect.Proxy;
import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.ecco.dto.ClientDefinitionCommandDto;
import com.ecco.dto.ClientEvent;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import com.ecco.dto.ClientDefinition;
import com.ecco.infrastructure.config.web.ConvertersConfig;


/**
 * TODO: This shares some (if not all behaviour with {@code com.ecco.service.DefaultClientImportStrategy} in
 * the web app.  We may be able to share it in the ecco-int-api-default package.
 */
public class EccoIntApiActor {

    protected final RestTemplate restTemplate;

    public EccoIntApiActor(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
        List<HttpMessageConverter<?>> messageConverterList = new ArrayList<>();
        // We probably don't need these as many are added by default, but somehow we get different exceptions mapped
        ConvertersConfig.addWebApiConvertersTo(messageConverterList);
        restTemplate.setMessageConverters(messageConverterList);
    }


    public Iterable<ClientDefinition> queryClients(String externalSourceName, URI uri, ClientDefinition exemplar) {
        final ClientDefinition[] result = restTemplate.postForObject(uri.resolve("clients/query"),
                externalClientSourceWrapper(exemplar, externalSourceName), ClientDefinition[].class);
        return Arrays.asList(result);
    }

    public ClientDefinition createClient(URI uri, ClientDefinition clientWithSource) {
        return restTemplate.postForObject(uri.resolve("clients/"),
                clientWithSource, ClientDefinition.class);
    }

    public String updateClient(URI uri, ClientDefinitionCommandDto cmd) {
        return restTemplate.postForObject(uri.resolve("client/update"),
                cmd, String.class);
    }

    /** Create event and return unique id for that event */
    public String createEvent(URI uri, ClientEvent event) {
        return restTemplate.postForObject(uri.resolve("events/"),
                event, String.class);
    }

    /** Provides a proxy instance of ClientDefinition so we can create the immutable.. I think */
    private ClientDefinition externalClientSourceWrapper(final ClientDefinition exemplar, final String externalClientSource) {
        return (ClientDefinition) Proxy.newProxyInstance(EccoIntApiActor.class.getClassLoader(),
                new Class[]{ClientDefinition.class}, (proxy, method, args) -> {
            if (method.getName().equals("getExternalClientSource")) {
                return externalClientSource;
            } else {
                return method.invoke(exemplar, args);
            }
        });
    }
}
