package com.ecco.evidence;

import com.ecco.dom.BaseServiceRecipientEvidence;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import javax.annotation.Nonnull;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

/**
 * Default implementation that is a ONE LEVEL, non-hierarchical
 * resolver for parentId and childId
 */
public class ParentChildResolverDefault implements ParentChildResolver {

    @PersistenceContext
    protected EntityManager entityManager;

    @Nonnull protected final ServiceRepository serviceRepository;

    public ParentChildResolverDefault(ServiceRepository serviceRepository) {
        this.serviceRepository = serviceRepository;
    }

    /**
     * Determine the parent/child of a given file.
     * Used to save evidence against the correct file in central processing feature, but the feature is now unused.
     * The parent/child logic can be repurposed, or removed.
     */
    @Override
    public ParentChildId getParentChildId(BaseServiceRecipientEvidence serviceRecipient) {
        // NB servicetypes.childService = true was the switch to recording evidence against the parent
        return new ParentChildId(serviceRecipient.getId(), null);
    }

    /**
     * Assume a one level hierarchy to get the parent's recipientId
     */
    private int getParentServiceRecipientId(BaseServiceRecipientEvidence serviceRecipient) {

        int parentReferralId = serviceRecipient.getTargetEntity().getParentEvidenceCapable() == null
                ? serviceRecipient.getId()
                : serviceRecipient.getTargetEntity().getParentEvidenceCapable().getServiceRecipient().getId();
        return parentReferralId;
    }

    private Integer getChildServiceRecipientId(BaseServiceRecipientEvidence serviceRecipient) {

        Integer childId = serviceRecipient.getTargetEntity().getParentEvidenceCapable() == null
                ? null
                : serviceRecipient.getId();
        return childId;
    }

}
