package com.ecco.evidence;

import com.ecco.dom.TaskDefinitionNameIdMappings;
import com.ecco.evidence.repositories.SupportWorkActionAssociationRepository;
import com.ecco.infrastructure.spring.data.QueryDslJpaEnhancedRepositoryImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;


@Configuration(proxyBeanMethods = false)
@EnableJpaRepositories(
        basePackageClasses=SupportWorkActionAssociationRepository.class,
        repositoryBaseClass=QueryDslJpaEnhancedRepositoryImpl.class)
public class EvidenceConfig {

    @Bean
    public TaskDefinitionNameIdMappings taskDefNameIdMappings(TaskDefinitionNameIdResolver resolver) {
        return new TaskDefinitionNameIdMappings(resolver);
    }
}
