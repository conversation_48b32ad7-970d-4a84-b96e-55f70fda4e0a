package com.ecco.evidence.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.UUID;

@Getter
public class DeleteRequestEvidenceEvent extends ApplicationEvent {

    private final int serviceRecipientId;
    private final int serviceAllocationId;
    private final UUID cmdUuid;

    public DeleteRequestEvidenceEvent(Object source, int serviceRecipientId, int serviceAllocationId, UUID cmdUuid) {
        super(source);
        this.serviceRecipientId = serviceRecipientId;
        this.serviceAllocationId = serviceAllocationId;
        this.cmdUuid = cmdUuid;
    }

}
