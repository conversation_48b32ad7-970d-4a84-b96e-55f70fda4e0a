package com.ecco.evidence.event;

import com.ecco.dom.EvidenceComment;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class EvidenceCommentSavedEvent extends ApplicationEvent {

    private final Integer serviceRecipientId;


    public EvidenceCommentSavedEvent(EvidenceComment source, Integer serviceRecipientId) {
        super(source);
        this.serviceRecipientId = serviceRecipientId;
    }

    public EvidenceComment getCommentDetails() {
        return (EvidenceComment) super.getSource();
    }
}
