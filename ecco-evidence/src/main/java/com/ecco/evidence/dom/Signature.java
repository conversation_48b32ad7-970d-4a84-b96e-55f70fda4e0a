package com.ecco.evidence.dom;

import com.ecco.dom.Individual;
import com.ecco.infrastructure.entity.AbstractUUIDKeyedEntity;

import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.UUID;

@Entity
@Table(name = "signature")
@NamedQueries(@NamedQuery(name = Signature.BULK_UPDATE_INDIVIDUAL_QUERY, query = "update Signature set individual = :newContact where individual = :oldContact"))
public class Signature extends AbstractUUIDKeyedEntity {

    public static final String BULK_UPDATE_INDIVIDUAL_QUERY = "signature.bulkUpdateIndividual";

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "individualId")
    @NotNull
    private Individual individual;
    @Lob
    @NotNull
    private String svgXml;
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @Column(name="signeddate")
    @NotNull
    @DateTimeFormat
    private DateTime date;

    public Signature() {}

    public Signature(UUID uuid, Individual individual, String svgXml, DateTime date) {
        super(uuid);
        this.individual = individual;
        this.svgXml = svgXml;
        this.date = date;
    }

    public Signature(Individual individual, String svgXml, DateTime date) {
        this.individual = individual;
        this.svgXml = svgXml;
        this.date = date;
    }

    public Individual getIndividual() {
        return individual;
    }

    public void setIndividual(Individual individual) {
        this.individual = individual;
    }

    public String getSvgXml() {
        return svgXml;
    }

    public void setSvgXml(String svgXml) {
        this.svgXml = svgXml;
    }

    public DateTime getDate() {
        return date;
    }

    public Date getTime() {
        return getDate().toDate();
    }

    public void setDate(DateTime date) {
        this.date = date;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }

        Signature signature = (Signature) o;

        if (!date.equals(signature.date)) {
            return false;
        }
        if (!individual.equals(signature.individual)) {
            return false;
        }
        return svgXml.equals(signature.svgXml);
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + individual.hashCode();
        result = 31 * result + svgXml.hashCode();
        result = 31 * result + date.hashCode();
        return result;
    }
}
