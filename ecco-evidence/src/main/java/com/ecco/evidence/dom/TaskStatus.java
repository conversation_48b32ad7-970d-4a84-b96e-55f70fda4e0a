package com.ecco.evidence.dom;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.entity.AbstractUUIDKeyedEntity;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.security.dom.Group;
import com.ecco.security.dom.User;
import com.ecco.serviceConfig.dom.TaskDefinition;
import com.querydsl.core.annotations.QueryInit;
import lombok.*;
import org.apache.commons.lang.StringUtils;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.joda.time.Period;
import org.springframework.format.annotation.DateTimeFormat;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
@Entity
@Table(name = "svcrec_taskstatus")
public class TaskStatus extends AbstractUUIDKeyedEntity implements Created {

    private static Pattern PERIOD = Pattern.compile("[dwmy]", Pattern.CASE_INSENSITIVE);

    /**
     * Calculate a next date based on a start and a schedule string (non-standard).
     *
     * @param schedule consisting of csv periods - eg "2w, 2m" is 2 weeks followed by 2 months, where the last (2m) repeats until "end"
     * @param earliestDate first date to find the schedules from (null means today)
     * @param lastCompleted highest completed date from which we reset the schedule from (if provided)
     * @param extendBeyond last date which we need to extend past (null means today)
     * @return List of dates from earliestDate in the schedule after today until extendUntil
     */
    public static List<DateTime> calculateAllScheduleDates(@Nonnull String schedule, @Nullable DateTime earliestDate, @Nullable DateTime lastCompleted, @Nullable DateTime extendBeyond) {
        var scheduleDates = new ArrayList<DateTime>();
        String[] values = StringUtils.split(org.springframework.util.StringUtils.trimAllWhitespace(schedule), ',');

        // TODO we may want to consider updating extendBeyond if lastCompleted is after it
        //if (lastCompleted != null && extendBeyond != null && lastCompleted.isAfter(extendBeyond)) {
        //    extendBeyond = lastCompleted;
        //}

        DateTime lastScheduleDate = earliestDate != null ? earliestDate : DateTime.now();
        var resetDate = lastCompleted;
        String lastInterval = null;
        for (String value : values) {
            lastInterval = value;
            if ("end".equals(lastInterval)) {
                return scheduleDates;
            }
            lastScheduleDate = calculateScheduleDate(lastInterval, lastScheduleDate);
            // if the next proposed date is after our completed date, then re-calculate from the lastCompleted as the baseline
            if (resetDate != null && (lastScheduleDate.isAfter(resetDate))) {
                lastScheduleDate = calculateScheduleDate(lastInterval, resetDate);
                resetDate = null;
            }
            scheduleDates.add(lastScheduleDate);
        }

        while (lastScheduleDate.isBefore(extendBeyond) || lastScheduleDate.isEqual(extendBeyond)) {
            lastScheduleDate = calculateScheduleDate(lastInterval, lastScheduleDate);
            // if the next proposed date is after our completed date, then re-calculate from the lastCompleted as the baseline
            if (resetDate != null && (lastScheduleDate.isAfter(resetDate))) {
                lastScheduleDate = calculateScheduleDate(lastInterval, resetDate);
                resetDate = null;
            }
            scheduleDates.add(lastScheduleDate);
        }

        return scheduleDates;
    }

    /**
     * For an individual interval, 2m or 10w etc, find the next date
     */
    protected static DateTime calculateScheduleDate(String interval, DateTime from) {
        int d = 0;
        int m = 0;
        int w = 0;
        int y = 0;

        String period = StringUtils.right(interval, 1);
        var matcher = PERIOD.matcher(period);
        var match = matcher.find();
        assert match : "task dueDateSchedule is invalid: " + interval;

        String digitsStr = StringUtils.substring(interval, 0, interval.length()-1);
        assert StringUtils.isNotEmpty(digitsStr) : "task dueDateSchedule is invalue: " + interval;
        var digits = Integer.parseInt(digitsStr);

        // construct
        if (period.equals("d")) {
            d = digits;
        }
        if (period.equals("w")) {
            w = digits;
        }
        if (period.equals("m")) {
            m = digits;
        }
        if (period.equals("y")) {
            y = digits;
        }

        Period p = new Period(y, m, w, d, 0, 0, 0, 0);
        return from.plus(p);
    }

    /**
     * Calculate the due date based on startDate and the schedule, occurring after now
     * @param dueDateSchedule number of days to add to task created date
     * @return startDate from which the schedule begins working from
     */
    public static java.time.LocalDateTime calculateNextDueDate(String dueDateSchedule, java.time.LocalDate startDate) {
        return TaskStatus.calculateNextDueDate(dueDateSchedule, startDate, null, startDate);
    }

    public static java.time.LocalDateTime calculateNextDueDate(String dueDateSchedule, java.time.LocalDate earliestDate,
                                                               java.time.LocalDate lastCompletedDate,
                                                               java.time.LocalDate afterDate) {
        if (dueDateSchedule == null) {
            return null;
        }
        // instant is required for calculateNextScheduleDates, and although UTC is temporary, the user could trigger in the midnight->1am gap causing a day difference
        var earliestDateJoda = JodaToJDKAdapters.dateTimeToJoda(earliestDate.atStartOfDay(ZoneId.of("UTC")));
        var afterDateJoda = JodaToJDKAdapters.dateTimeToJoda(afterDate.atStartOfDay(ZoneId.of("UTC")));
        var lastCompletedDateJoda = lastCompletedDate != null ? JodaToJDKAdapters.dateTimeToJoda(lastCompletedDate.atStartOfDay(ZoneId.of("UTC"))) : null;
        var nextSchedules = TaskStatus.calculateAllScheduleDates(dueDateSchedule, earliestDateJoda, lastCompletedDateJoda, afterDateJoda);
        // get the first afterDate, converted back to java time
        return nextSchedules.stream()
                .filter(date -> date.isAfter(afterDateJoda))
                .findFirst()
                .map(s -> JodaToJDKAdapters.localDateTimeToJDk(s.toLocalDateTime()))
                .orElse(null);
    }

    public TaskStatus(String taskInstanceId, int serviceRecipientId) {
        super(UUID.fromString(taskInstanceId));
        this.serviceRecipientId = serviceRecipientId;
    }

    @NotNull
    @Column(nullable = false)
    private int serviceRecipientId;

    /**
     * This mapping allows us to map to service recipient serviceId and projectId for reporting in querydsl.
     * However, getService returns null and using EAGER we get 'MultipleBagFetchException' on
     * 'cannot simultaneously fetch multiple bags: [com.ecco.dom.Service.projects, com.ecco.security.dom.User.passwordHistory]'
     * Therefore we use serviceRecipientRepository (currently on the view mode conversion) until BaseServiceRecipient is
     * cleaned up and EAGER can be used.
     */
    @QueryInit("*.*")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "serviceRecipientId", insertable=false, updatable=false)
    @NotFound(action = NotFoundAction.EXCEPTION)
    @Nullable // NOTE: This is null when building
    BaseServiceRecipient serviceRecipient;

    @Nullable
    @JoinColumn(name="taskDefinitionId", insertable = false, updatable = false)
    @ManyToOne
    private TaskDefinition taskDefinition;

    @Nullable
    private Long taskDefinitionId;

    /**
     * A system's point in time.
     */
    @NotNull
    @Column
    // TODO we should be using Instant here, but this clashes with CreatedInterceptor
    //@Type(type = "org.jadira.usertype.dateandtime.joda.PersistentInstantAsTimestamp")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @DateTimeFormat(style="S-")
    @Nullable // NOTE this is @NotNull in the database, as it's set by CreatedInteceptor
    private DateTime created;

    /**
     * A status of how the task was closed.
     * See completeWorkflowTask which sets 'completed', or ReferralCloseWorkflowSyncAgent which sets 'parentinactive'.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "completedStatusId")
    @Nullable
    private ListDefinitionEntry completedStatus;

    @Column(name = "completedStatusId", insertable = false, updatable = false)
    @Nullable
    private Integer completedStatusId;

    /**
     * A users representation of when the task was completed.
     * Since it is completed, it has a point in time.
     */
    @Nullable
    @Column
    private Instant completed;

    /** What the task is about */
    @Nullable
    private String description;

    /**
     * When the task is due.
     * UTC-based because we should store as UTC.
     */
    @Nullable
    @Column
    private LocalDateTime dueDate;

    /**
     * Null means any group is applicable
     */
    @Nullable
    @ManyToOne
    @JoinColumn(name="relevantGroupId")
    private Group relevantGroup;

    @Nullable
    @ManyToOne
    @JoinColumn(name="assignedUserId")
    @QueryInit("*.*")
    private User assignedUser;

}
