package com.ecco.evidence.dom;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.UUID;

@Entity
@Table(name = "evdnc_threatwork_actions")
public class EvidenceThreatWorkAction {

    @EmbeddedId
    private WorkActionPk workActionId;

    EvidenceThreatWorkAction() {
    }

    public EvidenceThreatWorkAction(long actionDefId, UUID workUuid) {
        workActionId = new WorkActionPk(actionDefId, workUuid);
    }

    public WorkActionPk getWorkActionId() {
        return workActionId;
    }
    public void setWorkActionId(WorkActionPk workActionId) {
        this.workActionId = workActionId;
    }
}
