package com.ecco.evidence.repositories;

import static com.ecco.dom.servicerecipients.QBaseServiceRecipient.baseServiceRecipient;
import static com.ecco.evidence.repositories.ServiceRecipientRepository.PROJECTION;

import org.springframework.beans.factory.annotation.Autowired;

import com.ecco.servicerecipient.ServiceRecipientSummary;

import java.util.List;
import java.util.Set;

public class ServiceRecipientRepositoryImpl implements ServiceRecipientRepositoryCustom {

    @Autowired
    private ServiceRecipientRepository serviceRecipientRepository;

    @Override
    public ServiceRecipientSummary findOneSummary(int serviceRecipientId) {
        return serviceRecipientRepository.findOneWithProjection(PROJECTION,
                baseServiceRecipient.id.eq(serviceRecipientId) );
    }

    @Override
    public List<ServiceRecipientSummary> findAllLimitedToServiceIds(Set<Integer> serviceRecipientIds, List<Long> serviceIds) {
        return serviceRecipientRepository.findAllWithProjection(PROJECTION,
                baseServiceRecipient.id.in(serviceRecipientIds)
                .and(baseServiceRecipient.serviceAllocation.service.id.in(serviceIds))
        );
    }

}
