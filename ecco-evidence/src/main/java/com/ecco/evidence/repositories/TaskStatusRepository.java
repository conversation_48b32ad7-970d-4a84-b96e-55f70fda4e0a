package com.ecco.evidence.repositories;

import com.ecco.evidence.dom.TaskStatus;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import com.ecco.security.dom.User;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

public interface TaskStatusRepository extends QueryDslPredicateAndProjectionExecutor<TaskStatus, UUID> {

    List<TaskStatus> findAllByServiceRecipientIdAndTaskDefinitionId(Integer srId, long taskDefId);

    Stream<TaskStatus> findAllByCompletedNullAndAssignedUserIsNotNullAndDueDateBeforeOrderByAssignedUserAscDueDateAsc(LocalDateTime dueDateBefore);
    Stream<TaskStatus> findAllByAssignedUserAndCompletedNullAndDueDateBeforeOrderByAssignedUserAscDueDateAsc(User user, LocalDateTime dueDateBefore);
    Stream<TaskStatus> findAllByServiceRecipientIdAndAssignedUserAndCompletedNullAndDueDateIsNotNull(Integer srId, User user);

    Stream<TaskStatus> findAllByCompletedNullAndAssignedUserIsNotNullAndDueDateAfterAndDueDateBeforeOrderByAssignedUserAscDueDateAsc(LocalDateTime dueDateAfter, LocalDateTime dueDateBefore);
    Stream<TaskStatus> findAllByAssignedUserAndCompletedNullAndDueDateAfterAndDueDateBeforeOrderByAssignedUserAscDueDateAsc(User user, LocalDateTime dueDateAfter, LocalDateTime dueDateBefore);

    Optional<TaskStatus> findFirstByServiceRecipientIdAndTaskDefinitionIdOrderByCreatedAsc(Integer srId, long taskDefId);
    Optional<TaskStatus> findFirstByServiceRecipientIdAndTaskDefinitionIdAndCompletedIsNotNullOrderByCompletedDesc(Integer srId, long taskDefId);
    Optional<TaskStatus> findFirstByServiceRecipientIdAndTaskDefinitionIdOrderByDueDateDesc(Integer srId, long taskDefId);

    List<TaskStatus> findAllByServiceRecipientIdAndCompletedIsNull(Integer serviceRecipientId);
    // created to be more efficient, but completed not null could still be finding a lot of records. Should try to search on completedStatusId, or taskDefinitionId.
    List<TaskStatus> findAllByServiceRecipientIdAndCompletedIsNotNull(Integer serviceRecipientId);

    // NB only usage of this is to get latest per taskDefId (and testing) - ideally change to more specific method rather than catch-all
    List<TaskStatus> findAllByServiceRecipientId(Integer serviceRecipientId);
}
