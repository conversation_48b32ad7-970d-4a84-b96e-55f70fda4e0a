package com.ecco.evidence.repositories;

import com.ecco.dom.CalendarEventSnapshot;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

public interface CalendarEventSnapshotRepository extends QueryDslPredicateAndProjectionExecutor<CalendarEventSnapshot, String> {

    Stream<CalendarEventSnapshot> findAllByPlannedStartInstantBefore(Instant end);

    Stream<CalendarEventSnapshot> findAllByPlannedStartInstantGreaterThanEqualAndPlannedStartInstantBefore(Instant start, Instant end);

    Stream<CalendarEventSnapshot> findAllByPlannedStartInstantGreaterThanEqualAndPlannedStartInstantBeforeAndServiceRecipientId(Instant start, Instant end, int serviceRecipientId);

    Stream<CalendarEventSnapshot> findAllByPlannedStartInstantBeforeAndResourceContactIdAndEndInstantIsNull(Instant end, Long resourceContactId);

    Iterable<CalendarEventSnapshot> findAllByEventUidStartingWithAndPlannedStartInstantGreaterThanEqual(String eventUidPrefix, Instant from);

    Optional<CalendarEventSnapshot> findOneByEventUid(String eventUuid);

    Optional<CalendarEventSnapshot> findOneByWorkUuid(UUID workUuid);

    void deleteByPlannedStartInstantBefore(Instant minusDays);

    void deleteByEventUid(String uid);
}
