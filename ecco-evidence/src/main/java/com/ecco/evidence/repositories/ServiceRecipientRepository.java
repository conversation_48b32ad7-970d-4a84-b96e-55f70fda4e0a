package com.ecco.evidence.repositories;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.dom.BaseServiceRecipientEvidence;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import com.ecco.servicerecipient.QServiceRecipientSummary;
import org.joda.time.DateTime;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;

import javax.persistence.QueryHint;
import java.util.Optional;

import static com.ecco.dom.servicerecipients.QBaseServiceRecipient.baseServiceRecipient;
import static org.hibernate.jpa.QueryHints.HINT_READONLY;


public interface ServiceRecipientRepository extends QueryDslPredicateAndProjectionExecutor<BaseServiceRecipient, Integer>,
        ServiceRecipientRepositoryCustom, CrudRepositoryWithFindOne<BaseServiceRecipient, Integer> {

    QServiceRecipientSummary PROJECTION = new QServiceRecipientSummary(
            baseServiceRecipient.discriminator,
            baseServiceRecipient.id,
//            null, // TODO: parentId
            baseServiceRecipient.serviceAllocationId,
            baseServiceRecipient.serviceTypeId,
            baseServiceRecipient.serviceAllocation.serviceId, // for permissions server-side only
            baseServiceRecipient.serviceAllocation.projectId, // for permissions server-side only
            baseServiceRecipient.currentTaskIndex,
            baseServiceRecipient.currentTaskId);

    // we'd need to move this up to web-api module to have access to referral specific stuff which we want to avoid!
//    QReferral referral = baseServiceRecipient.as(QReferralServiceRecipient.class).referral;

    @Modifying
    @Query("UPDATE BaseServiceRecipient"
            + " SET version = version + 1, latestClientStatusId = :clientStatusId, latestClientStatusDateTime = :clientStatusDateTime"
            + " WHERE id = :serviceRecipientId")
    void updateLatestClientStatus(
            @Param("serviceRecipientId") int serviceRecipientId,
            @Param("clientStatusId") Integer clientStatusId,
            @Param("clientStatusDateTime") DateTime clientStatusDateTime);

    @Modifying
    @Query("UPDATE BaseServiceRecipient"
            + " SET version = version + 1, currentTaskId = :taskDefId"
            + " WHERE id = :serviceRecipientId")
    void updateTaskDefId(
            @Param("serviceRecipientId") int serviceRecipientId,
            @Param("taskDefId") long taskDefId);

    @Modifying
    @Query("UPDATE BaseServiceRecipient"
            + " SET version = version + 1, currentTaskIndex = :taskIndex"
            + " WHERE id = :serviceRecipientId")
    void updateTaskDefIndex(
            @Param("serviceRecipientId") int serviceRecipientId,
            @Param("taskIndex") Integer taskIndex);

    @Query("select serviceRecipient.id from CustomEventWithServiceRecipient ev inner join ev.serviceRecipient serviceRecipient where ev.id = ?1")
    Integer findServiceRecipientIdForCustomEvent(Long customEventId);

    @Query("select serviceRecipient.id from CustomEventRecurringImpl ev inner join ev.serviceRecipient serviceRecipient where ev.id = ?1")
    Optional<Integer> findServiceRecipientIdForCustomRecurringEvent(Long customEventId);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("select agreement.serviceRecipientId from DemandSchedule ds inner join ds.agreement agreement where ds.id = ?1")
    Integer findServiceRecipientIdForDemandSchedule(Long demandScheduleId);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    Optional<BaseServiceRecipientEvidence> findEvidenceCapableById(Integer serviceRecipientId); // TODO: Remove when this is on CrudRepository (Spring Data >= 2)

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("select sc.service.serviceTypeId from ServiceCategorisation sc where sc.id = ?1")
    Integer findConfigServiceTypeId(int serviceAllocationId);

}
