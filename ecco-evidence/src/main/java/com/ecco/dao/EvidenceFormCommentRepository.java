package com.ecco.dao;

import com.ecco.dom.EvidenceFormComment;
import org.springframework.data.repository.Repository;

import java.util.UUID;

public interface EvidenceFormCommentRepository extends Repository<EvidenceFormComment, UUID> {

    EvidenceFormComment save(EvidenceFormComment entity);

    EvidenceFormComment findOneByWork_Id(UUID workUuid);

    void delete(EvidenceFormComment comment);

}
