package com.ecco.dao;

import com.ecco.dom.EvidenceSupportWork;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;

import javax.persistence.QueryHint;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface EvidenceSupportWorkRepository extends EvidenceSupportWorkRepositoryCustom,
        QueryDslPredicateAndProjectionExecutor<EvidenceSupportWork, UUID>,
        CrudRepositoryWithFindOne<EvidenceSupportWork, UUID> {

    @Modifying
    @Query("UPDATE EvidenceSupportWork w " +
            "SET w.version = w.version + 1, w.signature.id = :signatureId " +
            "WHERE w.id IN :workIds")
    void attachSignature(@Param("workIds") List<UUID> workIds, @Param("signatureId") UUID signatureId);

    void delete(EvidenceSupportWork work);

    @Modifying
    @Query("UPDATE EvidenceSupportComment spc " +
            "SET spc.version = spc.version + 1, spc.threatWork=null " +
            "WHERE spc.threatWork.id = ?1")
    void clearThreatWorkLink(UUID uuid);

    @Modifying
    @Query("UPDATE EvidenceSupportWork w " +
            "SET w.version = w.version + 1, " +
            "w.invoiceId = :invoiceId " +
            "WHERE w.id = :workUuid")
    void setInvoiceId(@Param("workUuid") UUID workUuid,
                      @Param("invoiceId") Integer invoiceId);


    Optional<EvidenceSupportWork> findFirst1ByServiceRecipientIdOrderByCreatedDesc(int serviceRecipientId);

    Optional<EvidenceSupportWork> findById(UUID workUuid);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT e.id FROM EvidenceSupportWork e WHERE e.eventId = ?1")
    UUID findUuidByEventId(String eventId);

}
