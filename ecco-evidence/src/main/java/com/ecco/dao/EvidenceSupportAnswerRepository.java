package com.ecco.dao;

import com.ecco.dom.EvidenceSupportAnswer;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;

import java.util.UUID;

public interface EvidenceSupportAnswerRepository extends
        QueryDslPredicateAndProjectionExecutor<EvidenceSupportAnswer, UUID>,
        CrudRepositoryWithFindOne<EvidenceSupportAnswer, UUID> {

}
