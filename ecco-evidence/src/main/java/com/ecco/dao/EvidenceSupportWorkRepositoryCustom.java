package com.ecco.dao;

import com.ecco.dom.EvidenceGroup;
import com.ecco.dom.EvidenceSupportWork;
import org.joda.time.DateTime;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.querydsl.QPageRequest;

import javax.persistence.QueryHint;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

/**
 * Adds custom methods to {@link EvidenceSupportWorkRepository}
 * @since 23/09/2014
 */
public interface EvidenceSupportWorkRepositoryCustom {
    /**
     * Returns work summaries with the actions set populated, by querying the summaries and actions separately and then
     * munging them together.
     *
     * @param siblingServiceRecipientIds the service recipient id's (siblings of the clientId) from which to return work summaries
     * @param findAttachmentsOnly whether to filter just attachments
     * @return the list of fully-populated summary objects, ordered by newest first: workDate DESC, created DESC
     *
     * @see EvidenceSupportWorkRepositoryCustom#findAllSupportWorkSummaryByServiceRecipientIdAndRiskManagementOutstanding(Integer, EvidenceGroup)
     */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    Slice<EvidenceSupportWorkSummary> findAllSupportWorkSummaryWithActions(Set<Integer> siblingServiceRecipientIds, EvidenceGroup evidenceGroup, QPageRequest pr,
                                                                           boolean findAttachmentsOnly, boolean hactOnly, boolean statusChangeOnly);

    /**
     * Just one of {@link #findAllSupportWorkSummaryWithActions(List, EvidenceGroup, QPageRequest, boolean, boolean, boolean)}
     */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    Optional<EvidenceSupportWorkSummary> findOneSupportWorkSummaryWithActions(Integer serviceRecipientId, EvidenceGroup evidenceGroup, UUID workUuid);

    /**
     * @return list of support work UUIDs from the service recipient which has risk management handled
     */
    List<EvidenceSupportWorkRiskManagementHandledSummary> findAllSupportWorkWithRiskManagementHandled(Integer serviceRecipientId);
    List<EvidenceSupportWork> findAllSupportWorkWithRiskManagementNotHandledBefore(Integer serviceRecipientId, DateTime before);
    List<EvidenceSupportWorkRiskManagementHandledSummary> findAllSupportWorkWithRiskManagementHandled(Set<Integer> serviceRecipientIds);

    /**
     * @return list of support work where risk management required is true, but no threat has dealt with it yet
     */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<EvidenceSupportWorkSummary> findAllSupportWorkSummaryByServiceRecipientIdAndRiskManagementOutstanding(Integer serviceRecipientId, EvidenceGroup evidenceGroup);

}
