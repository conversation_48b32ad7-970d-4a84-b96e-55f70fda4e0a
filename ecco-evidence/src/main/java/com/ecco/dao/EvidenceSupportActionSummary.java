package com.ecco.dao;

import lombok.Getter;
import org.joda.time.DateTime;

import java.util.UUID;

@Getter
public class EvidenceSupportActionSummary extends BaseActionSummary {

    private final Integer statusChangeReasonId;

    private final String targetSchedule;

    @SuppressWarnings("unused")
    EvidenceSupportActionSummary() {
        this(null, null, null, null, null, null, null, null, null, null, null, 0, false, null, null, null, null, null, null, null);
    }

    @SuppressWarnings("WeakerAccess")
    public EvidenceSupportActionSummary(Long id, String actionName, String goalName, String goalPlan, UUID actionInstanceUuid,
                                        UUID parentActionInstanceUuid, Short hierarchy, String position, Long actionId, Long actionRiskId,
                                        Long actionRiskOutcomeId, int status, boolean statusChange, Integer score, Integer statusChangeReasonId,
                                        DateTime target, String targetSchedule, DateTime expiry, UUID workId, DateTime workDate) {
        super(id, actionName, goalName, goalPlan, actionId, actionRiskId, actionRiskOutcomeId, status, statusChange, target, expiry,
                workId, workDate, actionInstanceUuid, parentActionInstanceUuid, hierarchy, position, score);
        this.statusChangeReasonId = statusChangeReasonId;
        this.targetSchedule = targetSchedule;
    }

}
