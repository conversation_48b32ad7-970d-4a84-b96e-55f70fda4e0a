package com.ecco.dao;

import java.util.List;
import java.util.UUID;

import com.ecco.dom.BaseOutcomeBasedWorkSummary;
import com.ecco.dom.ContactImpl;
import com.querydsl.core.annotations.QueryProjection;

import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.joda.time.Instant;

/** Represents just a summary of support work without pulling in all the eager relationships. Used by SupportWorkController for the web-api. */
@Getter
@Setter
public class EvidenceSupportWorkSummary extends BaseOutcomeBasedWorkSummary<EvidenceSupportActionSummary> {

    private Boolean riskManagementRequired;
    private UUID riskManagementHandled;
    private Integer clientStatusId;
    private Integer meetingStatusId;
    private Integer locationId;
    private String eventId;
    private List<EvidenceFlagSummary> flags;

    // for cglib
    EvidenceSupportWorkSummary() {
        super(null, null, null, null, null,null, null, null, null, null, null, null);
    }

    @QueryProjection
    public EvidenceSupportWorkSummary(UUID id, Instant requestedDelete, Long taskDefId, Integer serviceRecipientId, Integer serviceAllocationId,
                                      ContactImpl author, String comment,
                                      Integer commentTypeId, Integer clientStatusId, Integer meetingStatusId, Integer locationId,
                                      Integer commentMinutesSpent, Boolean riskManagementRequired, UUID riskManagementHandled,
                                      UUID signatureId, DateTime workDate, DateTime createdDate, String eventId) {
        super(id, requestedDelete, taskDefId, serviceRecipientId, serviceAllocationId, author, comment, commentTypeId, commentMinutesSpent, signatureId,
                workDate, createdDate);
        this.riskManagementRequired = riskManagementRequired;
        this.riskManagementHandled = riskManagementHandled;
        this.clientStatusId = clientStatusId;
        this.meetingStatusId = meetingStatusId;
        this.locationId = locationId;
        this.eventId = eventId;
    }

}
