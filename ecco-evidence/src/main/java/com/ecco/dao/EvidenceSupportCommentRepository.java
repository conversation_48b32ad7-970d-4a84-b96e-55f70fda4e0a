package com.ecco.dao;

import com.ecco.dom.EvidenceSupportComment;
import org.springframework.data.repository.Repository;

import java.util.Optional;
import java.util.UUID;

public interface EvidenceSupportCommentRepository extends Repository<EvidenceSupportComment, UUID> {

    EvidenceSupportComment save(EvidenceSupportComment entity);

    Optional<EvidenceSupportComment> findOneByWork_Id(UUID workUuid);

    void delete(EvidenceSupportComment comment);

}
