package com.ecco.dao;

import java.util.List;
import java.util.Set;
import java.util.UUID;
import javax.annotation.Nonnull;

import org.joda.time.DateTime;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;

import com.ecco.dom.EvidenceSupportAnswer;

public interface EvidenceQuestionAnswerRepository extends Repository<EvidenceSupportAnswer, Long> {

    @Query("SELECT NEW com.ecco.dao.EvidenceQuestionAnswerSummary(" +
            " a.id, a.question.id, a.answer, a.work.id" +
            ")" +
            " from EvidenceSupportAnswer a" +
            // left join so we fail in constructor rather than miss an answer
            // but then, do we pull in all the eager joins...
            //" left join a.question q" +
            " where a.work.serviceRecipient.id in (?1)")
    List<EvidenceQuestionAnswerSummary> findAllQuestionAnswerSummaryByWork_serviceRecipientIds(Set<Integer> serviceRecipientIds);

    @Query("SELECT NEW com.ecco.dao.EvidenceQuestionAnswerSummary(" +
            " a.id, a.question.id, a.answer, a.work.id" +
            ")" +
            " from EvidenceSupportAnswer a" +
            " where a.work.id = ?1")
    List<EvidenceQuestionAnswerSummary> findOneQuestionAnswerSummary(UUID workUuid);

    /**
     * DEPRECATED - delete it, in favour of finding the whole snapshot at a point in time using existing code (then filter).
     * This is because this query finds the latest 'created', just as the below deprecated method finds the latest id', but these only works
     * because at the moment we only ever allow users to see the latest snapshot - but when it comes to editing, this won't work.
     */
    @Query("SELECT a1 FROM EvidenceSupportAnswer a1 WHERE " +
            "a1.serviceRecipientId = ?1 AND " +
            "a1.question.id = ?2 AND " +
            "a1.work.evidenceGroupId = ?3 AND " +
            "a1.created <= ?4 " +
            "ORDER BY a1.created DESC")
    List<EvidenceSupportAnswer> findLatestByServiceRecipientIdAndEvidenceGroupIdAndQuestionIdAndCreatedLessOrEqualTo(
            int serviceRecipientId, long questionId,
            long evidenceGroupId, @Nonnull DateTime timestamp,
            @Nonnull Pageable pageable);

    @Nonnull
    EvidenceSupportAnswer save(@Nonnull EvidenceSupportAnswer answer);

}
