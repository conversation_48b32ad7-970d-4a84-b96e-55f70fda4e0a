package com.ecco.dao;

import java.util.UUID;

import lombok.Getter;
import org.joda.time.DateTime;

@Getter
public abstract class BaseActionSummary {
    protected final UUID actionInstanceUuid;
    protected final UUID parentActionInstanceUuid;
    protected final Short hierarchy;
    protected final String position;
    private final Long id;
    private final String actionName;
    private final String goalName;
    private final String goalPlan;
    private final Integer score;
    private final Long actionId;
    private final Long actionRiskId;
    private final Long actionRiskOutcomeId;
    private final int status;
    private final boolean statusChange;
    private final DateTime target;
    private final DateTime expiry;
    private final UUID workId;
    private final DateTime workDate;

    public BaseActionSummary(Long id, String actionName, String goalName, String goalPlan,
                             Long actionId, Long actionRiskId,
                             Long actionRiskOutcomeId, int status, boolean statusChange, DateTime target, DateTime expiry,
                             UUID workId, DateTime workDate,
                             UUID actionInstanceUuid, UUID parentActionInstanceUuid, Short hierarchy, String position,
                             Integer score) {
        this.id = id;
        this.actionName = actionName;
        this.goalName = goalName;
        this.goalPlan = goalPlan;
        this.score = score;
        this.actionId = actionId;
        this.actionRiskId = actionRiskId;
        this.actionRiskOutcomeId = actionRiskOutcomeId;
        this.status = status;
        this.statusChange = statusChange;
        this.target = target;
        this.expiry = expiry;
        this.workId = workId;
        this.workDate = workDate;
        this.actionInstanceUuid = actionInstanceUuid;
        this.parentActionInstanceUuid = parentActionInstanceUuid;
        this.hierarchy = hierarchy;
        this.position = position;
    }

}
