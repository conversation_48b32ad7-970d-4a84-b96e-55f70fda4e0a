package com.ecco.dao;

import java.util.UUID;

import javax.annotation.Nonnull;
import com.querydsl.core.annotations.QueryProjection;

/** Represents UUIDs of support work which are managed by the UUID threat work */
public class EvidenceSupportWorkRiskManagementHandledSummary {

    @Nonnull
    private final UUID supportWorkUuid;

    @Nonnull
    private final UUID threatWorkUuid;

    // for cglib
    EvidenceSupportWorkRiskManagementHandledSummary() {
        this.supportWorkUuid = null;
        this.threatWorkUuid = null;
    }

    @QueryProjection
    public EvidenceSupportWorkRiskManagementHandledSummary(@Nonnull UUID supportWorkUuid, @Nonnull UUID threatWorkUuid) {
        this.supportWorkUuid = supportWorkUuid;
        this.threatWorkUuid = threatWorkUuid;
    }

    public UUID getSupportWorkUuid() {
        return supportWorkUuid;
    }

    public UUID getThreatWorkUuid() {
        return threatWorkUuid;
    }

}
