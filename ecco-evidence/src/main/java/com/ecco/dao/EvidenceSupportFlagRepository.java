package com.ecco.dao;

import com.ecco.dom.EvidenceSupportFlag;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;

import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface EvidenceSupportFlagRepository extends Repository<EvidenceSupportFlag, Long> {

    @Query("select new com.ecco.dao.EvidenceFlagSummary(" +
            " f.id, f.flagDefId, f.value, f.work.id)" +
            " from EvidenceSupportFlag f" +
            " where f.work.id = ?1"
    )
    List<EvidenceFlagSummary> findOneSupportFlagSummary(UUID workUid);

    @Query("select new com.ecco.dao.EvidenceFlagSummary(" +
            " f.id, f.flagDefId, f.value, f.work.id)" +
            " from EvidenceSupportFlag f" +
            " where f.serviceRecipient.id in ?1" +
            " order by f.work.workDate DESC"
    )
    List<EvidenceFlagSummary> findAllSupportFlagSummaryByServiceRecipientIds(Set<Integer> serviceRecipientId);

    @Query("SELECT f1 FROM EvidenceSupportFlag f1 WHERE " +
            "f1.serviceRecipient.id in ?1 AND " +
            "f1.id >= all ( " +
                "SELECT f2.id FROM EvidenceSupportFlag f2 WHERE "+
                "f2.serviceRecipient.id in ?1 AND " +
                "f2.flagDefId = f1.flagDefId)")
    List<EvidenceSupportFlag> findLatestFlagsByServiceRecipientIds(List<Integer> serviceRecipientIds);

}
