package com.ecco.dao;

import com.ecco.calendar.dom.EventEntry;
import com.ecco.dom.CustomEventImpl;
import com.ecco.calendar.dom.EventType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * CRUD operations on our events.
 */
public interface CustomEventRepository extends CrudRepository<CustomEventImpl, Long> {
    Optional<CustomEventImpl> findOneByUid(String uid);

    /**
     * Remove local event information if no longer relevant (eg reinstated apt, so remove dropped reason)
     * NB This is only for rota code, since we maintain events in both otherwise.
     */
    void deleteByUid(String uid);

    List<EventEntry> findAllByUidIn(List<String> uuids);

    @Query("SELECT e FROM CustomEventImpl e WHERE " +
            "e.serviceRecipient.id = :id AND " +
            "e.eventType = :type AND " +
                "((e.eventDate.year >= :year) OR " +
                "(e.eventDate.year = :year AND e.eventDate.month > :month) OR " +
                "(e.eventDate.year = :year AND e.eventDate.month = :month AND e.eventDate.day >= :day))")
    List<CustomEventImpl> findAllByTypeGreaterThan(@Param("id") int srId, @Param("type") EventType type,
                                                   @Param("year") int year, @Param("month") short month, @Param("day") short day);
}
