package com.ecco.dao;

import com.ecco.dom.EvidenceAttachment;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface EvidenceAttachmentRepository extends CrudRepository<EvidenceAttachment, Long> {

    @Query("SELECT NEW com.ecco.dao.EvidenceAttachmentSummary(a.work.taskDefId, a.file, a.work.id, a.work.serviceRecipient.id, a.work.workDate, a.work.created)"
            + " FROM EvidenceSupportAttachment a WHERE a.work.id = ?1")
    List<EvidenceAttachmentSummary> findSupportAttachmentsByWorkUuid(final UUID workUuid);

    /**
     * Get by serviceRecipientId - but don't want eager join on work as we've got that elsewhere
     * Restrict to evidenceGroupId=19 to avoid client rendering 'null' when it can't get supportStaffNotes.
     * This may exclude CHECKLIST etc - see findGroupFromGroupName.
     */
    @Query("SELECT NEW com.ecco.dao.EvidenceAttachmentSummary(a.work.taskDefId, a.file, a.work.id, a.work.serviceRecipient.id, a.work.workDate, a.work.created)"
            + " FROM EvidenceSupportAttachment a WHERE a.work.serviceRecipient.id = ?1 and a.work.evidenceGroupId = ?2"
            + " ORDER BY a.work.workDate desc")
    List<EvidenceAttachmentSummary> findSupportAttachmentsByServiceRecipientId(final int serviceRecipientId, long evidenceGroupId);

    /**
     * Get by serviceRecipientId - but don't want eager join on work as we've got that elsewhere
     */
    @Query("SELECT NEW com.ecco.dao.EvidenceAttachmentSummary(a.work.taskDefId, a.file, a.work.id, a.work.serviceRecipient.id, a.work.workDate, a.work.created)"
            + " FROM EvidenceSupportAttachment a WHERE a.work.serviceRecipient.id in ?1"
            + " ORDER BY a.work.workDate desc")
    List<EvidenceAttachmentSummary> findSupportAttachmentsByServiceRecipientIds(final Set<Integer> serviceRecipientIds);

    /**
     * Get by serviceRecipientId - but don't want eager join on work as we've got that elsewhere
     */
    @Query("SELECT NEW com.ecco.dao.EvidenceAttachmentSummary(a.work.taskDefId, a.file, a.work.id, a.work.serviceRecipient.id, a.work.workDate, a.work.created)"
            + " FROM EvidenceThreatAttachment a WHERE a.work.serviceRecipient.id = ?1"
            + " ORDER BY a.work.workDate desc")
    List<EvidenceAttachmentSummary> findThreatAttachmentsByServiceRecipientId(final int serviceRecipientId);

}
