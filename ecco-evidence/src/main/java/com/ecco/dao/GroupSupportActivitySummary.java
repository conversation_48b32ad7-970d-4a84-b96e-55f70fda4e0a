package com.ecco.dao;

import java.util.UUID;

import javax.annotation.Nullable;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.joda.time.LocalDate;

import org.joda.time.LocalDateTime;

@NoArgsConstructor
@Data
public class GroupSupportActivitySummary implements GroupSupportActivitySummaryStats {

    public Long id;
    public Integer serviceRecipientId;
    public UUID uuid;
    public String description;
    public LocalDateTime startDateTime;
    public Integer capacity;
    public Integer duration;
    public Integer activityTypeId;
    public Boolean course;
    public Long parentId;

    /** optional **/
    @Nullable
    public Integer venueId;

    /** optional **/
    @Nullable
    public String venueName;

    /** optional **/
    @Nullable
    public Long serviceId;

    /** optional **/
    @Nullable
    public Long projectId;

    /** optional **/
    @Nullable
    public Integer clientsInvited;

    /** optional **/
    @Nullable
    public Integer clientsAttending;

    /** optional **/
    @Nullable
    public Integer clientsAttended;

    /** optional **/
    @Nullable
    public LocalDate endDate;

}
