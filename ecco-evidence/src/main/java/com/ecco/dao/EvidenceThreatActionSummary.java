package com.ecco.dao;

import java.util.UUID;

import lombok.Getter;
import org.joda.time.DateTime;

@Getter
public class EvidenceThreatActionSummary extends BaseActionSummary {

    private final Integer likelihood;
    private final Integer severity;
    private final String hazard;
    private final String intervention;

    EvidenceThreatActionSummary() {
        this(null, null, null, null, null, null, null, null, null, null, null, 0, false, null, null, null, null, null, null, null, null, null);
    }

    public EvidenceThreatActionSummary(Long id, String actionName, String goalName, String goalPlan, UUID actionInstanceUuid,
                                       UUID parentActionInstanceUuid, Short hierarchy, String position, Long actionId, Long actionRiskId,
                                       Long actionRiskOutcomeId, int status, boolean statusChange, DateTime target, DateTime expiry, UUID workId, DateTime workDate,
                                       Integer likelihood, Integer severity, String hazard, String intervention, Integer score) {
        super(id, actionName, goalName, goalPlan, actionId, actionRiskId, actionRiskOutcomeId, status, statusChange, target, expiry,
                workId, workDate, actionInstanceUuid, parentActionInstanceUuid, hierarchy, position, score);
        this.likelihood = likelihood;
        this.severity = severity;
        this.hazard = hazard;
        this.intervention = intervention;
    }

}
