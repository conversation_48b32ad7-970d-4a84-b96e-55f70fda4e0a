package com.ecco.dao;

import java.util.UUID;

import javax.annotation.Nonnull;

/**
 * Intermediate class used in the loading of full history in an efficient
 * and simple manner
 */
public class EvidenceQuestionAnswerSummary {

    private final Long id;
    private final Long questionId;
    private final String answer;
    private final UUID workId;

    public EvidenceQuestionAnswerSummary(@Nonnull Long id, @Nonnull Long questionId,
                                         String answer, @Nonnull UUID workId) {
        this.id = id;
        this.questionId = questionId;
        this.answer = answer;
        this.workId = workId;
    }

    public Long getId() {
        return id;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public String getAnswer() {
        return answer;
    }

    public UUID getWorkId() {
        return workId;
    }

}
