package com.ecco.dao;

import com.ecco.dom.BaseWorkSummary;
import com.ecco.dom.ContactImpl;
import com.querydsl.core.annotations.QueryProjection;
import lombok.*;
import org.joda.time.DateTime;
import org.joda.time.Instant;

import java.util.UUID;

/** Represents just a summary of support work without pulling in all the eager relationships. Used by EvidenceFormController for the web-api. */
@Getter
@Setter
public class EvidenceFormWorkSummary extends BaseWorkSummary {

    private String form;
    private UUID formDefinitionUuid;

    // for cglib
    EvidenceFormWorkSummary() {
        super(null, null, null, null, null, null, null, null, null, null, null, null);
    }

    @QueryProjection
    public EvidenceFormWorkSummary(UUID id, Instant requestedDelete, Long taskDefId, Integer serviceRecipientId,
                                   Integer serviceAllocationId,
                                   ContactImpl author,
                                   String form, UUID formDefinitionUuid,
                                   String comment, Integer commentTypeId, Integer commentMinutesSpent,
                                   UUID signatureId, DateTime workDate, DateTime createdDate) {
        super(id, requestedDelete, taskDefId, serviceRecipientId, serviceAllocationId, author, comment, commentTypeId,
                commentMinutesSpent, signatureId, workDate, createdDate);
        this.form = form;
        this.formDefinitionUuid = formDefinitionUuid;
    }

}
