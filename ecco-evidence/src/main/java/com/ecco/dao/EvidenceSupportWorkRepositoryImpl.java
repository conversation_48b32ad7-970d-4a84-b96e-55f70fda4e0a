package com.ecco.dao;

import com.ecco.dom.*;
import com.ecco.evidence.dom.AssociatedAction;
import com.ecco.infrastructure.spring.data.QueryDslJpaEnhancedRepositoryImpl;
import com.ecco.serviceConfig.hact.dom.QHactOutcomeMapping;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Multimaps;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Slice;
import org.springframework.data.querydsl.QPageRequest;

import javax.annotation.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Custom implementation to take advantage of using ..Summary classes which avoid eager pull in of data we
 * don't need.
 */
public class EvidenceSupportWorkRepositoryImpl implements EvidenceSupportWorkRepositoryCustom {

    @Autowired
    private EvidenceSupportActionRepository actionEvidenceRepostory;

    @Autowired
    private EvidenceAttachmentRepository evidenceAttachmentRepository;

    @Autowired
    private EvidenceSupportFlagRepository flagRepository;

    @PersistenceContext
    private EntityManager em;


    @Override
    public Slice<EvidenceSupportWorkSummary> findAllSupportWorkSummaryWithActions(Set<Integer> siblingServiceRecipientIds, EvidenceGroup evidenceGroup, QPageRequest pr,
                                                                                  boolean findAttachmentsOnly, boolean hactOnly, boolean statusChangeOnly) {
        final Slice<EvidenceSupportWorkSummary> workSummaries = findAllWorkSummaryByServiceRecipientId(siblingServiceRecipientIds, evidenceGroup, pr,
                findAttachmentsOnly, hactOnly, statusChangeOnly);

        populateWorkSummaries(workSummaries, evidenceGroup);

        return workSummaries;
    }

    private void populateWorkSummaries(Slice<EvidenceSupportWorkSummary> workSummaries, EvidenceGroup evidenceGroup) {
        if (workSummaries.hasContent()) {
            Map<Integer, List<EvidenceSupportWorkSummary>> workSummariesBySrId = workSummaries.getContent().stream()
                    .collect(Collectors.groupingBy(EvidenceSupportWorkSummary::getServiceRecipientId));
            workSummariesBySrId.keySet().forEach(srId -> {
                populateWorkSummary(srId, workSummariesBySrId.get(srId).stream(), evidenceGroup);
            });
        }
    }

    @Override
    public Optional<EvidenceSupportWorkSummary> findOneSupportWorkSummaryWithActions(Integer serviceRecipientId, EvidenceGroup evidenceGroup, UUID uuid) {
        final Slice<EvidenceSupportWorkSummary> workSummaries = findAllWorkSummaryByServiceRecipientId(Collections.singleton(serviceRecipientId), evidenceGroup,
                false, false, false, false, null, uuid);

        populateWorkSummary(serviceRecipientId, workSummaries.stream(), evidenceGroup);

        return workSummaries.hasContent()
                ? Optional.of(workSummaries.getContent().get(0))
                : Optional.empty();
    }

    @Override
    public List<EvidenceSupportWorkSummary> findAllSupportWorkSummaryByServiceRecipientIdAndRiskManagementOutstanding(Integer serviceRecipientId, EvidenceGroup evidenceGroup) {
        Slice<EvidenceSupportWorkSummary> workSummaries = findAllWorkSummaryByServiceRecipientId(Collections.singleton(serviceRecipientId), evidenceGroup,
                true, false, false, false, null, null);

        populateWorkSummaries(workSummaries, evidenceGroup);

        return workSummaries.getContent();
    }

    @Override
    public List<EvidenceSupportWorkRiskManagementHandledSummary> findAllSupportWorkWithRiskManagementHandled(Integer serviceRecipientId) {
        return findAllSupportWorkWithRiskManagementHandled(Collections.singleton(serviceRecipientId));
    }
    @Override
    public List<EvidenceSupportWork> findAllSupportWorkWithRiskManagementNotHandledBefore(Integer serviceRecipientId, DateTime before) {
        var srIds = Collections.singleton(serviceRecipientId);

        QEvidenceSupportWork workQ = QEvidenceSupportWork.evidenceSupportWork;

        BooleanExpression beforeExp = before != null ? workQ.workDate.before(before) : null;
        JPQLQuery<EvidenceSupportWork> uuidsNotManaged =
                new JPAQuery<>(em).from(workQ)
                        .select(workQ)
                        .where(workQ.serviceRecipient.id.in(srIds),
                            // NB this is FIXED with evidenceGroupId id of 19 since all operations are expecting support work
                            workQ.evidenceGroupId.eq(EvidenceGroup.NEEDS.getId()), // TODO: use evidenceGroup parameter and work through code
                            workQ.comment.requiresThreatManagement.isTrue(),
                            workQ.comment.threatWork.isNull(),
                            beforeExp);
        return uuidsNotManaged.fetch();
    }

    @Override
    public List<EvidenceSupportWorkRiskManagementHandledSummary> findAllSupportWorkWithRiskManagementHandled(Set<Integer> serviceRecipientIds) {
        QEvidenceSupportWork workQ = QEvidenceSupportWork.evidenceSupportWork;

        QEvidenceSupportWorkRiskManagementHandledSummary resultObj = new QEvidenceSupportWorkRiskManagementHandledSummary(
                workQ.id, workQ.comment.threatWork.id);
        JPQLQuery<EvidenceSupportWorkRiskManagementHandledSummary> query = new JPAQuery<>(em);
        JPQLQuery<EvidenceSupportWorkRiskManagementHandledSummary> uuidsManaged =
                query.from(workQ)
                .select(resultObj)
                .where(workQ.serviceRecipient.id.in(serviceRecipientIds),
                        // NB this is FIXED with evidenceGroupId id of 19 since all operations are expecting support work
                        workQ.evidenceGroupId.eq(EvidenceGroup.NEEDS.getId()), // TODO: use evidenceGroup parameter and work through code
                        workQ.comment.threatWork.isNotNull());
        return uuidsManaged.fetch();
    }

    public void populateWorkSummary(Integer serviceRecipientId, final Stream<EvidenceSupportWorkSummary> workSummaries, EvidenceGroup evidenceGroup) {

        workSummaries.forEach(workSummary -> {
            final List<EvidenceSupportActionSummary> actions =
                    actionEvidenceRepostory.findAllSupportActionSummaryByWork_serviceRecipientId(serviceRecipientId);
            final List<EvidenceFlagSummary> flags =
                    flagRepository.findAllSupportFlagSummaryByServiceRecipientIds(Collections.singleton(serviceRecipientId));
            // Map it
            ImmutableListMultimap<UUID, EvidenceSupportActionSummary> actionMap = Multimaps.index(actions, EvidenceSupportActionSummary::getWorkId);
            final ImmutableListMultimap<UUID, EvidenceFlagSummary> flagsByWorkId = Multimaps.index(flags, EvidenceFlagSummary::getWorkId);

            // and again for associated actions
            List<AssociatedAction> associatedActions =
                    actionEvidenceRepostory.findAllAssociatedActionsByWork_serviceRecipientId(serviceRecipientId);
            ImmutableListMultimap<UUID,AssociatedAction> associatedMap = Multimaps.index(associatedActions, input -> input.workId);

            // and again for attachments
            List<EvidenceAttachmentSummary> attachments = evidenceAttachmentRepository.findSupportAttachmentsByServiceRecipientId(serviceRecipientId, evidenceGroup.getId());

            Map<UUID, List<EvidenceAttachmentSummary>> attachmentsMap = attachments.stream()
                    .collect(Collectors.groupingBy(EvidenceAttachmentSummary::getWorkId));

            // Munge them together
            UUID key = workSummary.getId();
            workSummary.setActions(ImmutableSet.copyOf(actionMap.get(key)));
            workSummary.setAssociatedActions(ImmutableSet.copyOf(associatedMap.get(key)));
            workSummary.setAttachments(attachmentsMap.get(key));
            workSummary.setFlags(ImmutableList.copyOf(flagsByWorkId.get(key)));
        });
    }

    /**
     * This method does not populate {@link EvidenceSupportWorkSummary actions}.
     * @see EvidenceSupportWorkRepositoryCustom#findAllSupportWorkSummaryWithActions(List, EvidenceGroup, QPageRequest, boolean, boolean, boolean)
     * was:
     * "select new com.ecco.dao.SupportWorkSummary(" +
            " w.id, w.referral.id, a, c.comment, t," +
            " c.minutesSpent, w.workDate, w.created" +
            ")" +
            " from evidenceSupportWork w left join w.author a left join w.comment c left join w.comment.type t" +
            " where w.referral.id = ?1" +
            " order by workDate DESC"
     */
    private Slice<EvidenceSupportWorkSummary> findAllWorkSummaryByServiceRecipientId(Set<Integer> siblingServiceRecipientIds, EvidenceGroup evidenceGroup, QPageRequest pr,
                                                                                     boolean findAttachmentsOnly, boolean hactOnly, boolean statusChangeOnly) {
        return findAllWorkSummaryByServiceRecipientId(siblingServiceRecipientIds, evidenceGroup, false,
                findAttachmentsOnly, hactOnly, statusChangeOnly, pr, null);
    }

    private Slice<EvidenceSupportWorkSummary> findAllWorkSummaryByServiceRecipientId(Set<Integer> siblingServiceRecipientIds,
                                                                                     EvidenceGroup evidenceGroup,
                                                                                     boolean findRiskManagementOutstanding,
                                                                                     boolean findAttachmentsOnly,
                                                                                     boolean hactOnly,
                                                                                     boolean statusChangeOnly,
                                                                                     @Nullable QPageRequest pr,
                                                                                     @Nullable UUID workUuid) {
        QEvidenceSupportWork workQ = QEvidenceSupportWork.evidenceSupportWork;
        QEvidenceSupportComment commentQ = QEvidenceSupportComment.evidenceSupportComment;
        QContactImpl authorQ = QContactImpl.contactImpl;
        QEvidenceThreatWork threatWorkQ = QEvidenceThreatWork.evidenceThreatWork;
        QEvidenceSupportAction workActionQ = QEvidenceSupportAction.evidenceSupportAction;
        QHactOutcomeMapping hactOutcomeMappingQ = QHactOutcomeMapping.hactOutcomeMapping;

        BooleanExpression riskManagementRequiredExp = findRiskManagementOutstanding ?
                workQ.comment.requiresThreatManagement.isTrue()
                .and(workQ.comment.threatWork.isNull())
                : null;

        BooleanExpression attachmentsOnlyExp = findAttachmentsOnly ?
                workQ.attachments.isNotEmpty()
                : null;

        BooleanExpression workUuidExp = workUuid != null ?
                workQ.id.eq(workUuid)
                : null;

        // NB its important to use alias in 'leftJoin(source, alias)' since the alias needs to be used
        // in the list(resultObj) to use the left joins, and not use inner joins from the original entity
        // see the test GenericTypeWorkRepositoryTest
        QEvidenceSupportWorkSummary resultObj = new QEvidenceSupportWorkSummary(
                workQ.id, workQ.requestedDelete, workQ.taskDefId, workQ.serviceRecipient.id,
                workQ.serviceRecipient.serviceAllocationId, authorQ, commentQ.comment, commentQ.typeDefId,
                commentQ.clientStatusId, commentQ.meetingStatusId, commentQ.locationId,
                commentQ.minutesSpent, commentQ.requiresThreatManagement, threatWorkQ.id, workQ.signature.id, workQ.workDate,
                workQ.created, workQ.eventId);

        // NB this is FIXED with evidenceGroupId id of 19 since all operations are expecting support work
        // and not the 'manager notes' etc which might also live under supportplanwork with a different id
        // TODO: allow evidenceGroup to be passed in as a parameter to allow "manager notes" etc to
        // be used offline.
        BooleanExpression where = workQ.serviceRecipient.id.in(siblingServiceRecipientIds)
                .and(riskManagementRequiredExp) // null gets ignored
                .and(attachmentsOnlyExp) // null gets ignored
                .and(workUuidExp)
                .and(workQ.evidenceGroupId.eq(evidenceGroup.getId()));

        var actionBased = statusChangeOnly || hactOnly;
        var queryTables = new JPAQuery<EvidenceSupportWork>(em)
                .from(workQ)
                .leftJoin(workQ.author, authorQ)
                .leftJoin(workQ.comment, commentQ)
                .leftJoin(commentQ.threatWork, threatWorkQ);

        if (actionBased) {
            BooleanExpression whereSub = workActionQ.serviceRecipient.id.eq(workQ.serviceRecipientId)
                    .and(workActionQ.work.id.eq(workQ.id));

            if (statusChangeOnly) {
                whereSub = whereSub
                        .and(workActionQ.statusChange.eq(Boolean.TRUE));
            }

            if (hactOnly) {
                // NB we can inner join our way to hactOutcomeMappingQ but the rhs must be a root
                // whereas hactOutcomeMappingQ is a new entity, so ideally we need to mimic inner join
                // although 'in' is perfectable acceptable for the size of the hact mappings expected.
                //.innerJoin(workActionQ.action, actionQ);
                //.innerJoin(actionQ, hactOutcomeMappingQ.action);
                whereSub = whereSub
                        .and(workActionQ.action.id.in(JPAExpressions.selectFrom(hactOutcomeMappingQ).select(hactOutcomeMappingQ.actionDefId)))
                        .and(workActionQ.statusChange.eq(Boolean.TRUE))
                        .and(workActionQ.status.eq(EvidenceAction.isRelevant));
            }

            /*
                Resulting query from the 'exists' approach, against mysql:
                    select * from supportplanwork generictyp0_
                    left outer join contacts contactimp1_ on generictyp0_.contactId=contactimp1_.id
                    left outer join supportplancomments generictyp2_ on generictyp0_.uuid=generictyp2_.workUuid
                    left outer join commenttypes commenttyp3_ on generictyp2_.typeId=commenttyp3_.id
                    left outer join supportthreatwork generictyp4_ on generictyp2_.threatWorkUuid=generictyp4_.uuid
                    cross join servicerecipients baseservic5_
                    where generictyp0_.serviceRecipientId=baseservic5_.id
                      and generictyp0_.serviceRecipientId=200307
                      and generictyp0_.evidenceGroupId='19'
                      and (exists
                            (select 1 from supportplanactions generictyp6_
                             where generictyp6_.serviceRecipientId=generictyp0_.serviceRecipientId
                             and generictyp6_.workUuid=generictyp0_.uuid
                             and (generictyp6_.actionId in (select hactoutcom7_.actionDefId from hactoutcomemappings hactoutcom7_))
                             and generictyp6_.statusChange=1
                             and generictyp6_.status=1
                            )
                          )
                    order by generictyp0_.workDate desc, generictyp0_.created desc;
             */
            where = where
                    .and(JPAExpressions.selectOne().from(workActionQ).where(whereSub).exists());
        }

        var query = queryTables
                .select(resultObj)
                .where(where)
                .orderBy(workQ.workDate.desc())
                .orderBy(workQ.created.desc());
        return QueryDslJpaEnhancedRepositoryImpl.queryAsSlice(query, pr, resultObj);
    }

}
