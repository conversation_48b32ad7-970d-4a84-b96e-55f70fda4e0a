package com.ecco.dom;


public enum RedAmberGreen implements IdName<Integer> {

    Unknown(-1), <PERSON>(0), <PERSON>(1), <PERSON>(2), None(3);

    int value;

    RedAmberGreen(int value) {
        this.value = value;
    }

    public int toInt() {return value;}

    public static RedAmberGreen fromInt(int value) {
        switch (value) {
            case 0: return Red;
            case 1: return Amber;
            case 2: return Green;
            case 3: return None;
            default: return Unknown;
        }
    }

    @Override
    public Integer getId() {
        return value;
    }
    @Override
    public String getName() {
        switch (value) {
            case 0: return "red";
            case 1: return "amber";
            case 2: return "green";
            case 3: return "none";
            default: return "unknown";
        }
    }
}
