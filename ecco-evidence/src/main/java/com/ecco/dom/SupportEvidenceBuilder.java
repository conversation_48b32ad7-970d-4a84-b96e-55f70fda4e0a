package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;

import java.math.BigDecimal;

public class SupportEvidenceBuilder extends EvidenceWithOutcomesBuilder<EvidenceSupportWork, EvidenceSupportOutcome, EvidenceSupportAction, EvidenceSupportComment> {

    public SupportEvidenceBuilder(EvidenceSupportWork existingWork) {
        super(existingWork);
    }

    public SupportEvidenceBuilder(int parentServiceRecipientId) {
        super(new EvidenceSupportWork(parentServiceRecipientId));
    }

    @Override
    protected EvidenceSupportWork newWork() {
        return new EvidenceSupportWork();
    }

    @Override
    protected EvidenceSupportOutcome newOutcome(int serviceRecipientId) {
        return new EvidenceSupportOutcome(serviceRecipientId);
    }

    @Override
    protected EvidenceSupportAction newAction(int serviceRecipientId) {
        return new EvidenceSupportAction(serviceRecipientId);
    }

    @Override
    protected EvidenceSupportComment newComment(int serviceRecipientId) {
        return new EvidenceSupportComment(serviceRecipientId);
    }

    @Override
    public SupportEvidenceBuilder setChild(BaseServiceRecipient child) {
        if (child != null) {
            getWork().setChildServiceRecipientId(child.getId());
        }
        return this;
    }

    public SupportEvidenceBuilder setChild(Integer childServiceRecipientId) {
        getWork().setChildServiceRecipientId(childServiceRecipientId);
        return this;
    }

    /**
     * When a piece of evidence requires risk management.
     * Not implemented by default, since not all evidence has the property
     */
    public SupportEvidenceBuilder riskManagementRequired(boolean riskManagementRequired) {
        getWorkComment().setRequiresThreatManagement(riskManagementRequired);
        return this;
    }

    /**
     * When a piece of evidence records the client status.
     * Not implemented by default, since not all evidence has the property
     */
    public SupportEvidenceBuilder withClientStatus(Integer clientStatusId) {
        if (clientStatusId != null && clientStatusId > 0) { // TODO: Explain why this blocks setting to null
            getWorkComment().setClientStatusId(clientStatusId);
        }
        return this;
    }

    /**
     * When a piece of evidence records the meeting status.
     * Not implemented by default, since not all evidence has the property
     */
    public SupportEvidenceBuilder withMeetingStatus(Integer meetingStatusId) {
        if (meetingStatusId != null && meetingStatusId > 0) { // TODO: Explain why this blocks setting to null
            getWorkComment().setMeetingStatusId(meetingStatusId);
        }
        return this;
    }

    /**
     * When a piece of evidence records the meeting status.
     * Not implemented by default, since not all evidence has the property
     */
    public SupportEvidenceBuilder withLocation(Integer locationId) {
        if (locationId != null && locationId > 0) { // TODO: Explain why this blocks setting to null
            getWorkComment().setLocationId(locationId);
        }
        return this;
    }

    public SupportEvidenceBuilder withEventId(String eventId) {
        getWork().setEventId(eventId);
        return this;
    }

    /**
     * When a piece of evidence can have mileage to the serviceRecipient.
     * Not implemented by default, since not all evidence has the property
     */
    public SupportEvidenceBuilder withMileageTo(BigDecimal mileageTo) {
        getWorkComment().setMileageTo(mileageTo);
        return this;
    }

    /**
     * When a piece of evidence can have mileage with the serviceRecipient.
     * Not implemented by default, since not all evidence has the property
     */
    public SupportEvidenceBuilder withMileageDuring(BigDecimal mileageDuring) {
        getWorkComment().setMileageDuring(mileageDuring);
        return this;
    }

    /**
     * When a piece of evidence can have travel time to the serviceRecipient.
     * Not implemented by default, since not all evidence has the property
     */
    public SupportEvidenceBuilder withMinutesTravel(Integer minsTravel) {
        getWorkComment().setMinsTravel(minsTravel);
        return this;
    }

    @Override
    public SupportEvidenceBuilder review(Review review) {
        // mimicing SupportEvidenceBusinessLogic.saveWork, the review is progressed on each save
        // so we simply do the same here

        // we set the page first - at least thats what we see from GenericTypeDtoSupport.toWork()
        // which occurs before SupportEvidenceBusinessLogic.saveWork
        getWork().setReview(review);
        getWork().setOutcomePage(review.getReviewPage());

        // now increase the percentage
        // the loading deals with where we may be over 100% complete - just plug away here
        review.setReviewPage(review.getReviewPage() + 1);

        return this;
    }

}
