package com.ecco.dom;

import com.ecco.infrastructure.Created;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

@MappedSuperclass
@Getter
@Setter
public abstract class EvidenceBaseFlag extends BaseEvidence implements Created, ServiceRecipientId, EvidenceData, EvidenceFlag {

    @Column(name = "flagDefId")
    protected Integer flagDefId;

    @Column
    private boolean value;

    @Override
    public int compareTo(EvidenceDataAcceptor o) {
        return getWorkId().compareTo(o.getWorkId());
    }

    @Override
    public Integer getServiceRecipientId() {
        return identifier(serviceRecipient);
    }

}
