package com.ecco.dom;

import com.ecco.infrastructure.Created;
import com.ecco.serviceConfig.dom.Question;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.joda.time.DateTime;

import javax.persistence.*;

@Setter
@Getter
@NoArgsConstructor
@MappedSuperclass
public abstract class EvidenceAnswer extends BaseEvidence implements EvidenceData, Created {

    // many to one since we can have many riskActionComments to one contact
    @ManyToOne(fetch=FetchType.LAZY)
    // we could be in the situation where a contact is not found - so don't make it a deal-breaker
    @NotFound(action=NotFoundAction.IGNORE)
    @JoinColumn(name="contactId")
    ContactImpl author;

    @ManyToOne(fetch=FetchType.LAZY)
    @NotFound(action=NotFoundAction.IGNORE) // FIXME Causes EAGER
    @JoinColumn(name="questionId")
    Question question;

    @Lob
    String answer; // we can work out the type from the question


    public EvidenceAnswer(int serviceRecipientId) {
        super(serviceRecipientId);
    }

    public void accept(EvidenceAnswerVisitor visitor) {
        visitor.visit(this);
    }

    public DateTime getDate() {
        return getCreated();
    }

    @Override
    public int compareTo(EvidenceDataAcceptor o) {
        return getWorkId().compareTo(o.getWorkId());
    }

    /** e.g. the serviceRecipientId */
    public abstract Integer getParentId();
}
