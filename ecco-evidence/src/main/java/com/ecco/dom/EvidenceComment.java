package com.ecco.dom;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import com.querydsl.core.annotations.QueryInit;

@MappedSuperclass
@Getter
public abstract class EvidenceComment extends BaseComment implements EvidenceData {

    private static final long serialVersionUID = 1L;

    @QueryInit("*.*")
    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="serviceRecipientId", insertable=false, updatable=false)
    @NotFound(action=NotFoundAction.EXCEPTION)
    private BaseServiceRecipient serviceRecipient;

    @Column
    private Integer serviceRecipientId;

    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="typeDefId", insertable=false, updatable=false)
    @NotFound(action= NotFoundAction.EXCEPTION)
    private ListDefinitionEntry type;

    @Setter
    @Column(name="typeDefId")
    private Integer typeDefId;

    public EvidenceComment() {
        // For Hibernate etc
    }

    public EvidenceComment(Integer serviceRecipientId) {
        super();
        this.serviceRecipientId = serviceRecipientId;
    }


    public abstract void setWork(BaseWorkEvidence work);


    @Override
    public int compareTo(EvidenceDataAcceptor o) {
        return getWorkId().compareTo(o.getWorkId());
    }

}
