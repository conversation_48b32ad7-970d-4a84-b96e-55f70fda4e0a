package com.ecco.dom;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

/**
 * Attachments for support work evidence.
 *
 * @since 08/08/2014
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@DiscriminatorValue(EvidenceSupportAttachment.DISCRIMINATOR_VALUE)
public class EvidenceSupportAttachment extends EvidenceAttachment {
    public static final String DISCRIMINATOR_VALUE = "support";

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "fileid", nullable = false)
    private ServiceRecipientAttachment file;

    @ManyToOne(fetch = FetchType.LAZY, optional = false, targetEntity = EvidenceSupportWork.class)
    @JoinColumn(name = "workUuid", columnDefinition="CHAR(36)", nullable=false)
    private EvidenceWork work;

    public EvidenceSupportAttachment() {
    }

    public EvidenceSupportAttachment(ServiceRecipientAttachment file) {
        this.file = file;
    }
}
