package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.evidence.dom.Signature;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.*;

import java.util.UUID;

import static javax.persistence.CascadeType.PERSIST;
import static javax.persistence.CascadeType.REMOVE;

/**
 * A work item for recording custom form data
 */
@Entity
@Table(name="evdnc_form_work")
@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED) // for hibernate, protected in superclass
@EqualsAndHashCode(callSuper = true)
public class EvidenceFormWork extends AbstractBaseWorkEvidence {

    /**
     * The source page where the evidence is saved against.
     * This was typically a string, as sent from the command and then
     * converted in the handler to the id for referential integrity
     * but we should be sending the exact value and relying on ref-integrity
     */
    @Column
    private long taskDefId;

    // We'd like this but ... alas @SecondaryTable didn't work when we tried it for this in AppointmentSchedule
//    transient String evidenceTaskName;

    @Column(length = 20)
    String evidenceGroupKey;

    @OneToOne(mappedBy = "work", cascade = {PERSIST, REMOVE})
    EvidenceFormComment comment;

    @OneToOne(mappedBy="work", cascade={PERSIST, REMOVE}, fetch=FetchType.EAGER)
    private EvidenceFormSnapshot snapshot;

    // as per BaseWorkWithChild - but not appropriate yet for AbstractBaseWorkEvidence
    @OneToOne(cascade = REMOVE)
    @JoinColumn(name = "signatureUuid", columnDefinition="CHAR(36)")
    Signature signature;

    public EvidenceFormWork(int serviceRecipientId) {
        super(serviceRecipientId);
    }

    public static Builder builder(int serviceRecipientId) {
        EvidenceFormWork e = new EvidenceFormWork(serviceRecipientId);
        return new Builder(e);
    }

    @Override
    public void setTaskDefId(long taskDefId) {
        this.taskDefId = taskDefId;
    }

    @Override
    public void setEvidenceGroupId(long evidenceGroupId) {
        super.setEvidenceGroupId(evidenceGroupId);
        // custom forms don't share history, so the group id is the task id
        this.evidenceGroupKey = TaskDefinitionNameIdMappings.fromTaskDefIdToTaskName(evidenceGroupId);
    }

    @Override
    public void addComment(EvidenceComment comment) {
        EvidenceFormComment com = (EvidenceFormComment) comment;
        com.setWork(this);
        this.comment = com;
    }


    public static class Builder extends EvidenceWithCommentBuilder<EvidenceFormWork, EvidenceFormComment> {

        public Builder(EvidenceFormWork e) {
            super(e);
        }

        public EvidenceFormWork build() {
            return work;
        }

        @Override
        protected EvidenceFormWork newWork() {
            return new EvidenceFormWork();
        }

        @Override
        protected EvidenceFormComment newComment(int serviceRecipientId) {
            return new EvidenceFormComment(serviceRecipientId);
        }

        public EvidenceFormSnapshot newSnapshot(String json, UUID formDefinitionUuid) {
            EvidenceFormSnapshot s = new EvidenceFormSnapshot(this.work.getServiceRecipientId());
            s.setWork(this.work);
            s.setJson(json);
            s.setFormDefinitionUuid(formDefinitionUuid);
            return s;
        }

        @Override
        public EvidenceWithCommentBuilder<EvidenceFormWork, EvidenceFormComment> setChild(BaseServiceRecipient child) {
            return this;
        }

    }

}
