package com.ecco.dom;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.Entity;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.Table;

/**
 * @since 08/08/2014
 */
@Entity
@Table(name = EvidenceAttachment.TABLE_NAME)
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = EvidenceAttachment.DISCRIMINATOR_COLUMN, discriminatorType = DiscriminatorType.STRING)
@Immutable
public abstract class EvidenceAttachment extends AbstractLongKeyedEntity {
    private static final long serialVersionUID = 1L;
    public static final String DISCRIMINATOR_COLUMN = "work_type";
    public static final String TABLE_NAME = "evdnc_attachments";

    @Column(nullable = false)
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @DateTimeFormat(style="S-")
    private DateTime created = new DateTime();

    public DateTime getCreated() {
        return created;
    }

    public void setCreated(DateTime created) {
        this.created = created;
    }

    public abstract ServiceRecipientAttachment getFile();

    public abstract BaseWorkEvidence getWork();
}
