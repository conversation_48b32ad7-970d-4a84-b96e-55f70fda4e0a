package com.ecco.dom;

import java.io.Serializable;

import javax.persistence.*;

import com.ecco.calendar.dom.EventEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@javax.persistence.Entity
@Table(name="contacts_events")

// override the default column names
@AssociationOverrides({
    @AssociationOverride(name="multiId.event", joinColumns = @JoinColumn(name="eventId")),
    @AssociationOverride(name="multiId.contact", joinColumns = @JoinColumn(name="contactId"))
    //@AssociationOverride(name="multiId.referral", joinColumns = @JoinColumn(name="referralId"))
})
@NamedQueries(@NamedQuery(name = Contact_Event.BULK_UPDATE_CONTACT_QUERY, query = "update Contact_Event set multiId.contact = :newContact where multiId.contact = :oldContact"))
public class Contact_Event implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final String BULK_UPDATE_CONTACT_QUERY = "contact_event.bulkUpdateContact";

    protected final Logger log = LoggerFactory.getLogger(getClass());

    // status
    Integer eventContactStatusId;

    Integer contactTypeId;

    public Integer getEventContactStatusId() {
        return eventContactStatusId;
    }

    public void setEventContactStatusId(Integer eventStatusId) {
        this.eventContactStatusId = eventStatusId;
    }

    // the id works much easier in a separate file (at least the hql had less problems)
    private Contact_Event_MultiId multiId = new Contact_Event_MultiId();
    @EmbeddedId
    public Contact_Event_MultiId getMultiId() {
        return multiId;
    }

    @Transient
    public ContactImpl getContact() {
        return getMultiId().getContact();
    }

    @Transient
    public EventEntry getEvent() {
        return getMultiId().getEvent();
    }

    private Integer version = null;
    @Version
    @Column(name="version")
    public Integer getVersion() {
        return version;
    }

    @Override
    public String toString() {
        return  "contact_event " + getMultiId().getContact().getId() + ", " + getMultiId().getEvent().getId();
    }

    public void setContact(ContactImpl contact) {
        getMultiId().setContact(contact);
    }
    public void setEvent(CustomEventImpl event) {
        getMultiId().setEvent(event);
    }
    public void setMultiId(Contact_Event_MultiId multiId) {
        this.multiId = multiId;
    }
    public void setVersion(Integer version) {
        this.version = version;
    }
    public Integer getContactTypeId() {
        return contactTypeId;
    }
    public void setContactTypeId(Integer contactTypeId) {
        this.contactTypeId = contactTypeId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Contact_Event that = (Contact_Event) o;

        return getMultiId() != null ? getMultiId().equals(that.getMultiId()) : that.getMultiId() == null;
    }

    @Override
    public int hashCode() {
        return (getMultiId() != null ? getMultiId().hashCode() : 0);
    }

    /* when we wanted to extend the event table - though it should really be the event_contact table
    // don't delete events if this gets deleted - its only a status
    @Id
    @OneToOne(cascade={}, fetch=FetchType.LAZY)
    @JoinColumn(name="eventId")
    @NotFound(action=NotFoundAction.EXCEPTION)
    public Event getEvent() {
        return event;
    }
    */

}
