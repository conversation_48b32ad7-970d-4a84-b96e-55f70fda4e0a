package com.ecco.dom;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

import com.ecco.serviceConfig.dom.Action;
import java.util.UUID;
import javax.persistence.*;

@javax.persistence.Entity
@javax.persistence.Table(name="evdnc_threatactions")
// abstractelemententity allows us to store many new objects in a set (equals can have an identifier)
@NamedQueries(@NamedQuery(name = EvidenceThreatAction.BULK_UPDATE_AUTHOR_QUERY, query = "update EvidenceThreatAction set author = :newContact where author = :oldContact"))
public class EvidenceThreatAction extends EvidenceAction<EvidenceThreatWork> {

    private static final long serialVersionUID = 1L;

    public static final String BULK_UPDATE_AUTHOR_QUERY = "evidenceThreatAction.bulkUpdateAuthor";

    public static class Builder {

        private final EvidenceThreatAction a;

        private Builder(EvidenceThreatAction a) {
            this.a = a;
        }

        public Builder withActionInstanceUuid(UUID actionInstanceUuid) {
            a.actionInstanceUuid = actionInstanceUuid;
            return this;
        }

        public Builder withParentActionInstanceUuid(UUID parentActionInstanceUuid) {
            a.setParentActionInstanceUuid(parentActionInstanceUuid);
            return this;
        }

        public Builder withActionId(long id) {
            a.action = new Action(id);
            return this;
        }

        public EvidenceThreatAction build() {
            return a;
        }

        public Builder withStatus(int status) {
            a.status = status;
            return this;
        }

        public Builder withStatusChange(boolean isChanged) {
            a.statusChange = isChanged;
            return this;
        }
    }

    public static Builder builder(int serviceRecipientId, UUID actionInstanceUuid, UUID parentActionInstanceUuid, long actionDefId) {
        return builder(serviceRecipientId)
                .withActionInstanceUuid(actionInstanceUuid)
                .withParentActionInstanceUuid(parentActionInstanceUuid)
                .withActionId(actionDefId).withStatus(1);
    }

    public static Builder builder(int serviceRecipientId) {
        EvidenceThreatAction a = new EvidenceThreatAction(serviceRecipientId);
        return new Builder(a);
    }

    /** Create a new action with defaults from the previous action */
    public static EvidenceThreatAction fromPrevious(EvidenceThreatAction previousSnapshot) {
        EvidenceThreatAction newSnapshot = new EvidenceThreatAction(previousSnapshot.getServiceRecipientId());
        newSnapshot.action = previousSnapshot.action;
        newSnapshot.actionInstanceUuid = previousSnapshot.actionInstanceUuid;
        newSnapshot.setParentActionInstanceUuid(previousSnapshot.parentActionInstanceUuid);
        newSnapshot.hierarchy = previousSnapshot.getHierarchy();
        newSnapshot.position = previousSnapshot.position;
        newSnapshot.activity = previousSnapshot.activity;
        newSnapshot.goalName = previousSnapshot.goalName;
        newSnapshot.goalPlan= previousSnapshot.goalPlan;
        newSnapshot.expiryDate = previousSnapshot.expiryDate;
        newSnapshot.score = previousSnapshot.score;
        newSnapshot.target = previousSnapshot.target;
        newSnapshot.statusChange = false;
        newSnapshot.status = previousSnapshot.status;
        newSnapshot.hazard = previousSnapshot.hazard;
        newSnapshot.intervention = previousSnapshot.intervention;
        newSnapshot.likelihood = previousSnapshot.likelihood;
        newSnapshot.severity = previousSnapshot.severity;
        return newSnapshot;
    }


    @ManyToOne(fetch=FetchType.LAZY, targetEntity= EvidenceThreatWork.class, optional=false)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    protected EvidenceThreatWork work;

    protected Integer likelihood;

    protected Integer severity;

    @Lob
    @Column(name="hazard")
    protected String hazard;

    @Lob
    @Column(name="intervention")
    protected String intervention;


    public EvidenceThreatAction() {
        // for Hibernate etc
    }

    public EvidenceThreatAction(int serviceRecipientId) {
        super(serviceRecipientId);
    }


    @Override
    public EvidenceThreatWork getWork() {
        return work;
    }
    @Override
    public UUID getWorkId() {
        return identifier(work);
    }


    public Integer getLikelihood() {
        return likelihood;
    }
    public Integer getSeverity() {
        return severity;
    }

    public void setWork(EvidenceThreatWork work) {
        this.work = work;
    }
    public void setLikelihood(Integer likelihood) {
        this.likelihood = likelihood;
    }
    public void setSeverity(Integer severity) {
        this.severity = severity;
    }

    public String getHazard() {
        return hazard;
    }

    public void setHazard(String hazard) {
        this.hazard = hazard;
    }

    public String getIntervention() {
        return intervention;
    }

    public void setIntervention(String intervention) {
        this.intervention = intervention;
    }
}
