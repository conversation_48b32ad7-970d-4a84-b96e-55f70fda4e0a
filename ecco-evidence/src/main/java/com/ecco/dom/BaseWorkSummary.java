package com.ecco.dom;

import com.ecco.dao.EvidenceAttachmentSummary;

import lombok.Getter;
import org.joda.time.DateTime;
import org.joda.time.Instant;

import java.util.List;
import java.util.UUID;

@Getter
public abstract class BaseWorkSummary {
    private final UUID id;
    private Instant requestedDelete;

    private final Integer serviceRecipientId;
    private final Integer serviceAllocationId;
    private final ContactImpl author;

    private List<EvidenceAttachmentSummary> attachments;
    private final String comment;
    private final Integer commentTypeId;
    private final Integer commentMinutesSpent;
    private final DateTime workDate;
    private final DateTime createdDate;
    private UUID signatureId;
    private String taskName;

    public BaseWorkSummary(UUID id, Instant requestedDelete, Long taskDefId, Integer serviceRecipientId,
                           Integer serviceAllocationId, ContactImpl author, String comment,
                           Integer commentTypeId, Integer commentMinutesSpent, UUID signatureId,
                           DateTime workDate, DateTime createdDate) {
        this.id = id;
        this.requestedDelete = requestedDelete;
        this.taskName = TaskDefinitionNameIdMappings.fromTaskDefIdToTaskName(taskDefId);
        this.serviceRecipientId = serviceRecipientId;
        this.author = author;
        this.comment = comment;
        this.commentTypeId = commentTypeId;
        this.commentMinutesSpent = commentMinutesSpent;
        this.signatureId = signatureId;
        this.workDate = workDate;
        this.createdDate = createdDate;
        this.serviceAllocationId = serviceAllocationId;
    }

    public void setAttachments(List<EvidenceAttachmentSummary> attachments) {
        this.attachments = attachments;
    }
}
