package com.ecco.dom;

public interface EvidenceData extends EvidenceDataAcceptor {

    //public DateTime getCreated();
    // any data item has the potential to be associated with a 'piece of work' so this is the ideal location
    // also, being an interface (because of reasons indicated in the interface) means we need to specify the generic classes as abstract so the mappings can exist on real entities
    // unfortunately, common mappings can't exist in interfaces yet - https://hibernate.onjira.com/browse/HHH-4413
    //public GenericTypeWork getWork();

}
