package com.ecco.dom;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;
import java.time.Instant;
import java.util.UUID;

/**
 * Originally, this snapshot was for lone working to determine uncompleted visits, then it was pre-populated (DEV-2443)
 * to allow us to determine missed/late visits easily for alerting. Now, its being used as a way to cache data which
 * could be used for ScheduleView (and EvidenceView) replacing the findAllUninvoiced, but could even be used for the rota itself.
 */
@Entity
@Table(name="cal_eventstatus")
@Getter
@Setter
@NoArgsConstructor(force = true)
public class CalendarEventSnapshot {

    private static final long serialVersionUID = 1L;

    /**
     * The eventUid (UUID or UUID:YYYYMMDDThhmmss[Z]) that the work is for (eg 'rota visit')
     */
    @Id
    private String eventUid;

    /**
     * The appointment schedule id
     * Useful as persisted data.
     */
    @Column
    private Long demandScheduleId;

    /**
     * The service recipient receiving the 'visit' - the demand
     */
    @Column
    private Integer serviceRecipientId;

    /**
     * The contact receiving the 'visit' - the demand
     * Useful as persisted data - not easily query-able from a service recipient.
     * NB Can be null - eg BuildingServiceRecipient.
     */
    @Column
    private Long demandContactId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "demandContactId", updatable = false, insertable = false)
    @Nullable
    private Individual demandContact;

    /**
     * The one planning to doing the 'visit' - the resource
     * Useful as persisted data.
     */
    @Column
    private Long resourceContactId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "resourceContactId", updatable = false, insertable = false)
    @Nullable
    private Individual resourceContact;

    /**
     * The start time expected for this event
     */
    @Column
    @Nonnull
    private Instant plannedStartInstant;

    /**
     * The start time expected for this event
     * Useful as persisted data.
     */
    @Column
    @Nonnull
    private Instant plannedEndInstant;

    /**
     * Actual location.
     * Contains JSON of LocationViewModel (lat, lon, time, errorCode) - see careVisitReducer getGpsIfEnabled
     * The location of the contact at this (created) moment in time - according to the last CommentCommandViewModel received
     */
    @Column
    @Nullable
    private String location;

    /**
     * Actual start time of the event
     */
    @Column
    @Nullable
    private Instant startInstant;

    /**
     * Actual end time of the event
     */
    @Column
    @Nullable
    private Instant endInstant;

    /**
     * The one who recorded the 'visit'.
     * Actual user's contact who saved the data (also the contactId associated with the work)
     * ie resource/worker doing the 'visit' who submitted the EvidenceAssociatedContactCommand
     * see EvidenceAssociatedContactCommandHandler.orElseGet
     */
    @Column
    @Nullable
    private Long contactId;

    /**
     * The work of the 'visit'.
     * Actual work item holding the result of the visit, will exist on STARTing a visit.
     * Optional work item that the contact was associated with.
     * careSingleVisitDataLoader uses evidenceWorkUuid or generates a new one, so is mandatory in EvidenceAssociatedContactCommandViewModel,
     * but is null when we create entries for expected visits in recreateAppointmentsForServiceRecipient (for easier alerts).
     * Currently, if this is non-null then trying to delete the work will fail - due to a FK */
    @Column(columnDefinition="CHAR(36)")
    @Type(type="org.hibernate.type.UUIDCharType")
    @Nullable
    private UUID workUuid;

    public boolean canReset() {
        // if all null then can reset, if any data saved then don't reset
        return workUuid == null && contactId == null && startInstant == null && endInstant == null && location == null;
    }
}
