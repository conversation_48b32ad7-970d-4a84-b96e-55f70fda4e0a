package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.dom.upload.UploadedFile;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Entity
@Data
@Table(name="svcrec_attachments")
@NoArgsConstructor
public class ServiceRecipientAttachment extends UploadedFile {

    @Column
    private Integer serviceRecipientId;
    @Column
    private
    String evidencePage;
    @Column
    private
    String evidencePageGroup;

    // we don't need bidirectional to the serviceRecipient
    // however, we own the relationship and need to specify some aspects
    @ManyToOne(optional=false, fetch= FetchType.LAZY)
    @JoinColumn(name="serviceRecipientId", insertable=false, updatable=false)
    private BaseServiceRecipient serviceRecipient;


    public ServiceRecipientAttachment(Integer serviceRecipientId) {
        super();
        this.setServiceRecipientId(serviceRecipientId);
    }

    public void setEvidencePage(String taskName) {
        if (taskName != null) {
            long evidencePageNum = TaskDefinitionNameIdMappings.fromTaskNameToTaskDefId(taskName);
            evidencePage = String.valueOf(evidencePageNum);
        }
    }

    @Override
    public Source getSource() {
        return Source.SERVICE_RECIPIENT;
    }

}
