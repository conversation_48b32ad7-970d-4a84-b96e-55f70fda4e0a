package com.ecco.dom;

import static javax.persistence.CascadeType.PERSIST;
import static javax.persistence.CascadeType.REMOVE;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.*;

import org.hibernate.annotations.BatchSize;

import com.ecco.serviceConfig.dom.Action;

@Entity
@Table(name="evdnc_threatwork")
@NamedQueries(@NamedQuery(name = EvidenceThreatWork.BULK_UPDATE_AUTHOR_QUERY,
        query = "update EvidenceThreatWork set author = :newContact where author = :oldContact"))
public class EvidenceThreatWork extends BaseWorkWithChild {

    public static final String BULK_UPDATE_AUTHOR_QUERY = "evidenceThreatWork.bulkUpdateAuthor";

    @OneToOne(mappedBy="work", cascade={PERSIST, REMOVE})
    EvidenceThreatComment comment;

    @OneToMany(mappedBy="work", cascade={PERSIST, REMOVE}, fetch=FetchType.LAZY)
    @BatchSize(size = 20)
    Set<EvidenceThreatOutcome> outcomes = new HashSet<>();

    @OneToMany(mappedBy="work", cascade={PERSIST, REMOVE}, fetch=FetchType.LAZY)
    @BatchSize(size = 20)
    Set<EvidenceThreatAction> actions = new HashSet<>();

    // associated actions = linked actions
    // don't have any cascade, since we don't want to save the other side (the action) - simply the join table
    // 'persist' is reflected in the jointable syntax, such as insertable and updatable...
    @ManyToMany(fetch=FetchType.LAZY)
    @JoinTable(name = "evdnc_threatwork_actions",
            joinColumns = @JoinColumn(name = "workUuid", columnDefinition="CHAR(36)"),
            inverseJoinColumns = @JoinColumn(name = "actionId"))
    @BatchSize(size = 20)
    Set<Action> associatedActions = new HashSet<>();

    // support plan comments that are managed by this threat piece of work
    // we don't persist since the supportPlanComment is already saved
    @OneToMany(mappedBy = "threatWork", cascade = CascadeType.MERGE, fetch = FetchType.LAZY)
    @BatchSize(size = 20)
    Set<EvidenceSupportComment> supportPlanCommentsManaged = new HashSet<>();

    @OneToMany(mappedBy="work", cascade={PERSIST, REMOVE}, fetch=FetchType.LAZY)
    @BatchSize(size = 20)
    Set<EvidenceThreatFlag> flags = new HashSet<>();

    @OneToMany(mappedBy="work", cascade={PERSIST, REMOVE}, fetch=FetchType.LAZY)
    @BatchSize(size = 20)
    Set<EvidenceThreatAttachment> attachments = new HashSet<>();


    public EvidenceThreatWork() {
        super();
    }

    public EvidenceThreatWork(int serviceRecipientId) {
        super(serviceRecipientId);
    }


    // get the set of actions which haven't been acted upon
    public Set<Action> associatedActionsRemaining() {
        Set<Action> remainingActions = new HashSet<>(associatedActions);
        for (EvidenceThreatAction da : actions) {
            remainingActions.remove(da.getAction());
        }
        return remainingActions;
    }

    @Override
    public Set<EvidenceThreatOutcome> getOutcomes() {
        return outcomes;
    }
    @Override
    public Set<EvidenceThreatAction> getActions() {
        return actions;
    }
    @Override
    public EvidenceThreatComment getComment() {
        return comment;
    }

    public Set<EvidenceSupportComment> getSupportPlanCommentsManaged() {
        return supportPlanCommentsManaged;
    }

    public Set<EvidenceThreatFlag> getFlags() {
        return flags;
    }

    public Set<EvidenceThreatAttachment> getAttachments() {
        return attachments;
    }

    @Override
    public Set<Action> getAssociatedActions() {
        return associatedActions;
    }

    @Override
    public boolean addAssociatedAction(Action action) {
        return associatedActions.add(action);
    }
    public boolean removeAssociatedAction(Action action) {
        return actions.remove(action);
    }

    @Override
    public Set<? extends EvidenceActivity> getActivities() {
        return null;
    }
    public void addSupportPlanCommentsManaged(Set<EvidenceSupportComment> supportPlanCommentsManaged) {
        this.supportPlanCommentsManaged.addAll(supportPlanCommentsManaged);
    }
    public void addSupportPlanCommentsManaged(EvidenceSupportComment comment) {
        comment.setThreatWork(this);
        comment.setRequiresThreatManagement(false);
        supportPlanCommentsManaged.add(comment);
    }

    @Override
    public void addOutcome(EvidenceOutcome outcome) {
        EvidenceThreatOutcome spo = (EvidenceThreatOutcome) outcome;
        spo.setWork(this);
        outcomes.add(spo);
    }
    @Override
    public void addAction(EvidenceAction action) {
        EvidenceThreatAction spa = (EvidenceThreatAction) action;
        spa.setWork(this);
        actions.add(spa);
    }
    @Override
    public void addComment(EvidenceComment comment) {
        EvidenceThreatComment spc = (EvidenceThreatComment) comment;
        spc.setWork(this);
        this.comment = spc;
    }

    @Override
    public void addAttachment(EvidenceAttachment attachment) {
        EvidenceThreatAttachment spa = (EvidenceThreatAttachment) attachment;
        spa.setWork(this);
        attachments.add(spa);
    }

    @Override
    public void addAnswer(EvidenceAnswer answer) {
        return;
    }

    @Override
    public void addActivity(EvidenceActivity activity) {
    }
    public void addFlag(EvidenceThreatFlag flag) {
        flag.setWork(this);
        flags.add(flag);
    }

    public void setComment(EvidenceThreatComment comment) {
        this.comment = comment;
    }
    public void setFlags(Set<EvidenceThreatFlag> flags) {
        this.flags = flags;
    }

    public String getTaskName() {
        return TaskDefinitionNameIdMappings.fromTaskDefIdToTaskName(getTaskDefId());
    }
}
