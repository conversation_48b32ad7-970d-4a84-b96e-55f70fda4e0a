package com.ecco.dom;


public enum RecencyImpact implements IdName<Integer> {

    Unknown(-1), NoProblem(0), PastSignificant(1), PresentOccasionalMinor(2), PresentPersistentMinor(3), PresentOccasionalSerious(4), PresentPersistentSerious(5);

    int value;

    RecencyImpact(int value) {
        this.value = value;
    }

    public int toInt() {return value;}

    public static RecencyImpact fromInt(int value) {
        switch (value) {
            case 0: return NoProblem;
            case 1: return PastSignificant;
            case 2: return PresentOccasionalMinor;
            case 3: return PresentPersistentMinor;
            case 4: return PresentOccasionalSerious;
            case 5: return PresentPersistentSerious;
            default: return Unknown;
        }
    }

    @Override
    public Integer getId() {
        return value;
    }
    @Override
    public String getName() {
        if (value == 0) {
            return "no problem";
        }
        if (value == 1) {
            return "past but significant";
        }
        if (value == 2) {
            return "present occasional minor";
        }
        if (value == 3) {
            return "present persistent minor";
        }
        if (value == 4) {
            return "present occasional serious";
        }
        if (value == 5) {
            return "present persistent serious";
        }
        return "unknown";
    }

}
