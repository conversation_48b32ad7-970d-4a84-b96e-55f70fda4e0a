package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.entity.RequestDelete;
import org.joda.time.DateTime;

import java.util.UUID;

public interface BaseWorkEvidence extends EvidenceData, Created, ServiceRecipientId, RequestDelete {

    void setId(UUID workId);
    void setTaskDefId(long taskDefId);
    void setEvidenceGroupId(long evidenceGroupId);

    // one comment (to be changed to multiple 'evidence's)
    // links to many changes in circumstance
    // we keep the author and date etc in the classes themselves
    // for convenience, but it also allows for a bit of work to span a period of time

    EvidenceComment getComment();

    void addComment(EvidenceComment comment);

    DateTime getWorkDate(); // FIXME: LocalDateTime or adjust database by 1 hour for periods within DST

    /** Sets millis to zero to avoid inconsistency between diff databases rounding millis or not */
    void setWorkDate(DateTime workDate);

    ContactImpl getAuthor();

    void setAuthor(ContactImpl author);

    // we may have a client involved in the work (eg a calendar)
    void setIndividual(ContactImpl individual);

    ContactImpl getIndividual();

    BaseServiceRecipient getServiceRecipient();
}
