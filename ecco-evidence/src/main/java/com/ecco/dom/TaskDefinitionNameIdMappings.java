package com.ecco.dom;

import com.ecco.evidence.TaskDefinitionNameIdResolver;
import org.springframework.stereotype.Component;

import static com.ecco.dom.TaskDefinitionNameIdMappings.Task.NEEDS_ASSESSMENT;
import static com.ecco.dom.TaskDefinitionNameIdMappings.Task.NEEDS_ASS_RED_REVIEW;
import static com.ecco.dom.TaskDefinitionNameIdMappings.Task.SUPPORT_PLAN;

/**
 * Responsible for collecting into one place the confusing taskDefId (i.e. taskName) evidenceGroupId (i.e. evidenceGroupKey)
 * magic values that come out as various numbers which are still used in the database.
 *
 * Called by command handlers where the url provides the taskName and evidenceGroup name.
 * This class then works out what the evidence will be saved as - back to numbers for backwards-compatibility.
 * It also populates the taskName when loadino BaseWorkSummary.
 *
 * We could convert these to a short string, e.g. VARCHAR(31).  The task name will either match a referral aspect
 * (do we have a constraint at the moment for the taskDefId id? - pretty sure we don't) or an Activiti task name.
 *
 */
@Component
public class TaskDefinitionNameIdMappings {

    private static TaskDefinitionNameIdResolver taskDefIdNameResolver;


    /** Deliberately set the static so static methods can call resolver as provided via dependency injection wiring */
    public TaskDefinitionNameIdMappings(TaskDefinitionNameIdResolver taskDefIdNameResolver) {
        TaskDefinitionNameIdMappings.taskDefIdNameResolver = taskDefIdNameResolver;
    }

    public enum Task {
        ASSESSMENT_DATE    (25, Names.ASSESSMENT_DATE),
        ACCEPT_ON_SERVICE  ( 9, Names.ACCEPT_ON_SERVICE),    // friendlyName = assessmentAccepted
        START_ON_SERVICE_ACCOMM   (10, Names.START_ON_SERVICE_ACCOMM),
        START_ON_SERVICE   (26, Names.START_ON_SERVICE),
        NEEDS_ASSESSMENT   (27, Names.NEEDS_ASSESSMENT),
        NEEDS_ASS_RED      (31, Names.NEEDS_ASS_RED),
        SUPPORT_PLAN       (19, Names.SUPPORT_PLAN),
        NEEDS_ASS_RED_REVIEW(54, Names.NEEDS_ASS_RED_REVIEW),
        CLOSE              (50, Names.CLOSE)
        // TODO: Enter all where there is only one friendlyName for a given taskDefinition.name as we know those are effectively constant
        ;

        public interface Names {
            String ASSESSMENT_DATE = "assessmentDate";
            String ACCEPT_ON_SERVICE = "decideFinal";
            String START_ON_SERVICE = "start";
            String START_ON_SERVICE_ACCOMM = "startAccommodation";
            String NEEDS_ASSESSMENT = "needsAssessment";
            String NEEDS_ASS_RED = "needsAssessmentReduction";
            String SUPPORT_PLAN = "needsReduction";
            String NEEDS_ASS_RED_REVIEW = "needsAssessmentReductionReview";
            String CLOSE = "close";
        }

        public final int taskDefinitionId;
        public final String taskName;

        Task(int taskDefinitionId, String taskName) {
            this.taskDefinitionId = taskDefinitionId;
            this.taskName = taskName;
        }

        public long getTaskId() {
            return taskDefinitionId;
        }
    }

    /**
     * Take the taskName and convert into the database taskDefId id
     */
    public static long fromTaskNameToTaskDefId(String taskName) {

        switch (taskName) {
            case "groupActivities": // TODO: This should actually be 'client' we should sql change all these to something other than 1 so that the taskDefId/sourceTask column matches referralIds
                return 1;
            case "groupSupport":
                return 1;
            case "needsReduction":
                return SUPPORT_PLAN.taskDefinitionId;
            case "needsAssessment":
                return NEEDS_ASSESSMENT.taskDefinitionId;
            case "threatAssessmentReduction":
                return 28;
            case "needsAssessmentReduction":
                return 31;
            case "needsAssessmentReductionReview":
                return NEEDS_ASS_RED_REVIEW.taskDefinitionId;
            case "needsReductionSP":
                return 107;
            case "threatAssessment":
                return 59;
            case "threatReduction":
                return 60;
            case "engagementComments":
                return 97;
            case "rotaVisit":
                return 101;
            case "managerNotes":
                return 103;
            case "supportStaffNotes":
                return 94;
            case "needsChecklist":
                return 108;
            case "verifyWork":
                return 142;
            // QUESTIONNAIRES below
            case "iaptInitialAssessment": // TODO: Remove the rarely used ones initially, such as iapt*
                return 68;
            case "iaptAttendance":
                return 69;
            case "iaptCurrentView":
                return 70;
            case "iaptSessions":
                return 71;
            case "iaptGoals":
                return 87;
            case "iaptImpact":
                return 88;
            case "iaptFeedback":
                return 89;
            case "profile":
                return 77;
            case "generalQuestionnaire":
                return 79;
            case "feedbackQuestionnaire":
                return 86;
            case "ecorys":
                return 90;
            case "CAF10risk":
                return 75;
            case "hactQuestionnaire":
                return 109;
            case "dailyRoutines":
                return 111;
            case "dietaryIntake":
                return 117;

            default:
                try {
                    return taskDefIdNameResolver.resolveNameToId(taskName)
                            .orElseGet(() -> Long.parseLong(taskName)); // support legacy
                } catch (Exception e) {
                    throw new IllegalArgumentException(
                            "Failed to resolve task " + taskName + " to a referral aspect id (aka taskDefIdId)");
                }
        }
    }

    /**
     * Converts a database taskDefId id to a taskName string which is used throughout the client code
     */
    public static String fromTaskDefIdToTaskName(long taskDefIdId) {
        switch((int)taskDefIdId) {
            case 1:
                return "groupActivities"; // TODO: NOTE: this is "client" in taskdefinitions, but we record group activity support work as "1" historically.
            case 19:
                return "needsReduction";
            case 27:
                return "needsAssessment";
            case 28:
                return "threatAssessmentReduction";
            case 31:
                return "needsAssessmentReduction";
            case 54:
                return "needsAssessmentReductionReview";
            case 59:
                return "threatAssessment";
            case 60:
                return "threatReduction";
            case 79:
                return "generalQuestionnaire";
            case 94:
                return "supportStaffNotes";
            case 97:
                return "engagementComments";
            case 101:
                return "rotaVisit";
            case 103:
                return "managerNotes";
            case 108:
                return "needsChecklist";
            case 109:
                return "hactQuestionnaire";
            case 111:
                return "dailyRoutines";
            case 117:
                return "dietaryIntake";
            case 120:
                return "customForm1";
            case 121:
                return "customForm2";
            case 122:
                return "customForm3";
            case 126:
                return "customForm4";
            case 128:
                return "customForm5";
            case 129:
                return "customForm6";
            case 130:
                return "customForm7";
            case 131:
                return "customForm8";
            case 132:
                return "customForm9";
            case 133:
                return "customForm10";
            case 134:
                return "customForm11";
            case 135:
                return "customForm12";
            case 136:
                return "customForm13";
            case 137:
                return "customForm14";
            case 138:
                return "customForm15";
            case 139:
                return "customForm16";
            case 140:
                return "customForm17";
            case 149:
                return "customFormMgr";
            //case 150:
                //return "jobDetails";

            default:
                return taskDefIdNameResolver.resolveIdToName(taskDefIdId)
                        .orElseGet(() -> String.valueOf(taskDefIdId)); // support legacy

        }
    }

}
