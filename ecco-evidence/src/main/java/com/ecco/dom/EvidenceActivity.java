package com.ecco.dom;

import java.math.BigDecimal;

import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;

import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import com.ecco.serviceConfig.dom.Action;

@MappedSuperclass
public class EvidenceActivity extends AbstractLongKeyedEntity {

    @ManyToOne
    @JoinColumn(name="actionId")
    Action action; // so we can associate with an action
    // GenericTypeSupportAction can be determined from the work and action if desired

    @ManyToOne
    @JoinColumn(name="agencyId")
    Agency agency;

    @DateTimeFormat(style="S-")
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    DateTime fromDate;

    boolean agreementInPlace;
    BigDecimal value;
    String PONumber;
    int fundingBand = 0; // not currently in the UI as changed requirements

    /** toDate can be projected or actual */
    @DateTimeFormat(style="S-")
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    DateTime toDate;

    // plannedEnd represents the toDate being a successful exit - a planned end
    // but if not, we can't know if the toDate is ongoing or unsuccessful (unless we compare to 'today' - but can't rely on that boundary as being definitive, we need manual verification)
    // therefore we require also a complete flag
    boolean complete;
    boolean plannedEnd;

    // referencing - optional
    public Action getAction() {
        return action;
    }
    public void setAction(Action action) {
        this.action = action;
    }

    // agency delivering the service
    public Agency getAgency() {
        return agency;
    }
    public void setAgency(Agency agency) {
        this.agency = agency;
    }

    public DateTime getFromDate() {
        return fromDate;
    }
    public void setFromDate(DateTime fromDate) {
        this.fromDate = fromDate;
    }

    public DateTime getToDate() {
        return toDate;
    }
    public void setToDate(DateTime toDate) {
        this.toDate = toDate;
    }

    public boolean isComplete() {
        return complete;
    }
    public void setComplete(boolean complete) {
        this.complete = complete;
    }

    public boolean isPlannedEnd() {
        return plannedEnd;
    }
    public void setPlannedEnd(boolean plannedEnd) {
        this.plannedEnd = plannedEnd;
    }
    public boolean isAgreementInPlace() {
        return agreementInPlace;
    }
    public void setAgreementInPlace(boolean agreementInPlace) {
        this.agreementInPlace = agreementInPlace;
    }
    public String getPONumber() {
        return PONumber;
    }
    public void setPONumber(String pONumber) {
        PONumber = pONumber;
    }

    public int getFundingBand() {
        return fundingBand;
    }
    public void setFundingBand(int fundingBand) {
        this.fundingBand = fundingBand;
    }

    public BigDecimal getValue() {
        return value;
    }
    public void setValue(BigDecimal value) {
        this.value = value;
    }


}
