package com.ecco.dom;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.serviceConfig.dom.OutcomeThreat;

import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

@javax.persistence.Entity
@Table(name="evdnc_threatoutcomes")
@NamedQueries(@NamedQuery(name = EvidenceThreatOutcome.BULK_UPDATE_AUTHOR_QUERY, query = "update EvidenceThreatOutcome set author = :newContact where author = :oldContact"))
public class EvidenceThreatOutcome extends EvidenceOutcome implements ServiceRecipientId {

    private static final long serialVersionUID = 1L;

    public static final String BULK_UPDATE_AUTHOR_QUERY = "evidenceThreatOutcome.bulkUpdateAuthor";

    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="serviceRecipientId", insertable=false, updatable=false)
    @NotFound(action=NotFoundAction.EXCEPTION)
    BaseServiceRecipient serviceRecipient;

    @Column(name="serviceRecipientId")
    private Integer serviceRecipientId;

    @ManyToOne(fetch=FetchType.LAZY, targetEntity= EvidenceThreatWork.class)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    EvidenceThreatWork work;

    @Column(name="STO_LEVEL")
    int level = -1; // default unknown -1

    String levelMeasure = "rag";

    @Lob
    @Column(name = "C_TRIGGER")
    String trigger;

    @Lob
    @Column(name = "C_CONTROL")
    String control;

    public static class Builder {

        private final EvidenceThreatOutcome outcome;

        private Builder(EvidenceThreatOutcome outcome) {
            this.outcome = outcome;
        }

        public Builder withOutcomeId(long id) {
            outcome.outcome = new OutcomeThreat();
            outcome.outcome.setId(id);
            return this;
        }

        public EvidenceThreatOutcome build() {
            return outcome;
        }

    }

    public static Builder builder(int serviceRecipientId, long areaDefId) {
        return builder(serviceRecipientId).withOutcomeId(areaDefId);
    }

    public static Builder builder(int serviceRecipientId) {
        EvidenceThreatOutcome outcome = new EvidenceThreatOutcome(serviceRecipientId);
        return new Builder(outcome);
    }


    public EvidenceThreatOutcome() {
        // For hibernate etc
    }

    public EvidenceThreatOutcome(int serviceRecipientId) {
        this.serviceRecipientId = serviceRecipientId;
    }

    public EvidenceThreatOutcome copy() {
        EvidenceThreatOutcome newSnapshot = new EvidenceThreatOutcome(serviceRecipientId);
        newSnapshot.outcome = outcome;
        newSnapshot.level = level;
        newSnapshot.levelMeasure = levelMeasure;
        newSnapshot.trigger = trigger;
        newSnapshot.control = control;
        newSnapshot.status = status;

        return newSnapshot;
    }

    public String getTrigger() {
        return trigger;
    }

    public String getControl() {
        return control;
    }

    public EvidenceWork getWork() {
        return work;
    }

    @Override
    public UUID getWorkId() {
        return identifier(work);
    }

    @Override
    public Integer getServiceRecipientId() {
        return identifier(serviceRecipient);
    }

    public BaseServiceRecipient getServiceRecipient() {
        return serviceRecipient;
    }

    public int getLevel() {
        return level;
    }

    public void setServiceRecipient(BaseServiceRecipient serviceRecipient) {
        this.serviceRecipient = serviceRecipient;
    }

    public void setWork(EvidenceThreatWork work) {
        this.work = work;
    }
    public void setTrigger(String trigger) {
        this.trigger = trigger;
    }
    public void setControl(String control) {
        this.control = control;
    }
    public void setLevel(int level) {
        this.level = level;
    }
    public String getLevelMeasure() {
        return levelMeasure;
    }

    // UNUSED - so we are always RAG until otherwise noted
    public void setLevelMeasure(String levelMeasure) {
        this.levelMeasure = levelMeasure;
    }

}
