package com.ecco.dom;

import java.util.UUID;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToOne;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import com.ecco.evidence.dom.Signature;

@MappedSuperclass
@Getter
@Setter
public abstract class BaseWorkWithChild extends AbstractBaseWorkEvidence implements EvidenceWork {

    private static final long serialVersionUID = 1L;

    @Override
    public void accept(EvidenceWorkVisitor visitor) {
        visitor.visit(this);
    }

    /**
     * @deprecated use getServiceRecipientId
     */
    @Deprecated
    @Override
    public Integer getParentId() {
        return getServiceRecipientId();
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "childSvcRecId", insertable=false, updatable=false)
    @NotFound(action = NotFoundAction.EXCEPTION)
    BaseServiceRecipient childServiceRecipient;

    @Column(name="childSvcRecId")
    private Integer childServiceRecipientId;


    @OneToOne
    @JoinColumn(name = "signatureUuid", columnDefinition="CHAR(36)")
    Signature signature;


    protected BaseWorkWithChild() {
        super();
    }

    protected BaseWorkWithChild(int serviceRecipientId) {
        super(serviceRecipientId);
    }

    protected BaseWorkWithChild(UUID id) {
        super(id);
    }

}