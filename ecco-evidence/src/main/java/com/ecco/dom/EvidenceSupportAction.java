package com.ecco.dom;

import java.util.UUID;

import javax.annotation.Nullable;
import javax.persistence.*;

import com.ecco.serviceConfig.dom.Action;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

@Setter
@Getter
@NoArgsConstructor
@Entity
@Table(name="evdnc_supportactions")
@NamedQueries(@NamedQuery(name = EvidenceSupportAction.BULK_UPDATE_AUTHOR_QUERY,
        query = "update EvidenceSupportAction set author = :newContact where author = :oldContact"))
public class EvidenceSupportAction extends EvidenceAction<EvidenceSupportWork> {

    private static final long serialVersionUID = 1L;

    public static final String BULK_UPDATE_AUTHOR_QUERY = "evidenceSupportAction.bulkUpdateAuthor";


    public static class Builder {

        private final EvidenceSupportAction a;

        private Builder(EvidenceSupportAction a) {
            this.a = a;
        }

        public Builder withActionInstanceUuid(UUID actionInstanceUuid) {
            a.actionInstanceUuid = actionInstanceUuid;
            return this;
        }

        public Builder withParentActionInstanceUuid(UUID parentActionInstanceUuid) {
            a.setParentActionInstanceUuid(parentActionInstanceUuid);
            return this;
        }

        public Builder withActionId(long id) {
            a.action = new Action(id);
            return this;
        }

        public EvidenceSupportAction build() {
            return a;
        }

        public Builder withStatus(int status) {
            a.status = status;
            return this;
        }

        public Builder withStatusChange(boolean isChanged) {
            a.statusChange = isChanged;
            return this;
        }
        public Builder withGoalName(String goalName) {
            a.goalName = goalName;
            return this;
        }

        public Builder withGoalPlan(String goalPlan) {
            a.goalPlan = goalPlan;
            return this;
        }
    }

    public static Builder builder(int serviceRecipientId, UUID actionInstanceUuid, UUID parentActionInstanceUuid, long actionDefId) {
        return builder(serviceRecipientId)
                .withActionInstanceUuid(actionInstanceUuid)
                .withParentActionInstanceUuid(parentActionInstanceUuid)
                .withActionId(actionDefId).withStatus(EvidenceSupportAction.isRelevant);
    }

    public static Builder builder(int serviceRecipientId) {
        EvidenceSupportAction a = new EvidenceSupportAction(serviceRecipientId);
        return new Builder(a);
    }

    /** Create a new action with defaults from the previous action */
    public static EvidenceSupportAction fromPrevious(EvidenceSupportAction previousSnapshot) {
        EvidenceSupportAction newSnapshot = new EvidenceSupportAction(previousSnapshot.getServiceRecipientId());
        newSnapshot.action = previousSnapshot.action;
        newSnapshot.actionInstanceUuid = previousSnapshot.actionInstanceUuid;
        newSnapshot.setParentActionInstanceUuid(previousSnapshot.parentActionInstanceUuid);
        newSnapshot.hierarchy = previousSnapshot.getHierarchy();
        newSnapshot.position = previousSnapshot.position;
        newSnapshot.activity = previousSnapshot.activity;
        newSnapshot.goalName = previousSnapshot.goalName;
        newSnapshot.goalPlan = previousSnapshot.goalPlan;
        newSnapshot.expiryDate = previousSnapshot.expiryDate;
        newSnapshot.score = previousSnapshot.score;
        newSnapshot.target = previousSnapshot.target;
        newSnapshot.targetSchedule = previousSnapshot.targetSchedule;
        newSnapshot.expiryDate = previousSnapshot.expiryDate;
        newSnapshot.statusChangeReason = previousSnapshot.statusChangeReason;
        newSnapshot.statusChange = false;
        newSnapshot.status = previousSnapshot.status;
        return newSnapshot;
    }

    @ManyToOne(cascade = CascadeType.PERSIST, fetch = FetchType.LAZY, targetEntity = EvidenceSupportWork.class)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    protected EvidenceSupportWork work;

    /** CRON-like schedule specification for how to determine next targetDate
     * Could be pulled up if we want this for Risk or HR */
    @Column
    @Nullable
    protected String targetSchedule;



    public EvidenceSupportAction(int serviceRecipientId) {
        super(serviceRecipientId);
    }


    @Override
    public UUID getWorkId() {
        if (work == null) {
            return null;
        } else {
            return identifier(work);
        }
    }

}
