package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;

public class RiskEvidenceBuilder extends EvidenceWithOutcomesBuilder<EvidenceThreatWork, EvidenceThreatOutcome, EvidenceThreatAction, EvidenceThreatComment> {

    public RiskEvidenceBuilder() {
        super();
    }

    public RiskEvidenceBuilder(EvidenceThreatWork existingWork) {
        super(existingWork);
    }

    public RiskEvidenceBuilder(Integer serviceRecipientId) {
        super(new EvidenceThreatWork(serviceRecipientId));
    }

    @Override
    protected EvidenceThreatWork newWork() {
        return new EvidenceThreatWork();
    }

    @Override
    protected EvidenceThreatOutcome newOutcome(int serviceRecipientId) {
        return new EvidenceThreatOutcome(serviceRecipientId);
    }

    @Override
    protected EvidenceThreatAction newAction(int serviceRecipientId) {
        return new EvidenceThreatAction(serviceRecipientId);
    }

    @Override
    protected EvidenceThreatComment newComment(int serviceRecipientId) {
        return new EvidenceThreatComment(serviceRecipientId);
    }

    @Override
    public RiskEvidenceBuilder setChild(BaseServiceRecipient child) {
        if (child != null) {
            getWork().setChildServiceRecipientId(child.getId());
        }
        return this;
    }

}
