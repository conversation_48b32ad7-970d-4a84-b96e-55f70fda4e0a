package com.ecco.dom;

import com.ecco.serviceConfig.dom.Action;
import com.ecco.serviceConfig.dom.Outcome;
import org.joda.time.DateTime;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Set;

public abstract class EvidenceWithOutcomesBuilder<WORK extends EvidenceWork, OUTCOME extends EvidenceOutcome,
    ACTION extends EvidenceAction<WORK>, COMMENT extends EvidenceComment> extends EvidenceWithCommentBuilder<WORK, COMMENT> {

    /** Latest actions from previous work - needed where we carry forward targetDate, comment etc */
    private List<ACTION> previousActions;

    protected abstract OUTCOME newOutcome(int serviceRecipientId);
    protected abstract ACTION newAction(int serviceRecipientId);

    public EvidenceWithOutcomesBuilder() {
        // we ensure a work item before we do any operations
        super();
    }

    public EvidenceWithOutcomesBuilder(WORK providedWork) {
        // we ensure a work item before we do any operations
        super(providedWork);
    }

    public EvidenceWithOutcomesBuilder<WORK, OUTCOME, ACTION, COMMENT> usingLatestActions(List<ACTION> actions) {
        previousActions = actions;
        return this;
    }

    public EvidenceWithOutcomesBuilder<WORK, OUTCOME, ACTION, COMMENT> review(Review review) {
        return this;
    }

    /**
     * Add an outcome to the piece of work - an outcome status has changed
     */
    public EvidenceWithOutcomesBuilder<WORK, OUTCOME, ACTION, COMMENT> withOutcome(Outcome o, int status) {
        EvidenceOutcome go = newOutcome(work.getServiceRecipientId());
        go.setOutcome(o);
        go.setAuthor(work.getAuthor());
        go.setCreated(work.getCreated());
        go.setStatus(status);
        // we mimic existing fn, but don't support statusChangeDate anymore
        //if (displayOutcome.getStatusChangeDate() == null)
            go.setStatusChangeDate(work.getWorkDate());
        // nothing extra required on support
        //toWorkLogicExtra(go, displayOutcome);
        // needs an identifier since in the set, the equals method compares the id, or collectionId
        go.setCollectionId(work.getOutcomes().size());
        work.addOutcome(go);

        return this;
    }

    /** Creates a new action with relevant info inherited from parent work item */
    protected ACTION createNewAction(Action a, Integer actionStatus, Boolean actionStatusChange) {
        ACTION sa = newAction(work.getServiceRecipientId());
        sa.setCollectionId(work.getActions().size()); // Use size() to create 'unique' IDs until saved
        work.addAction(sa);
        sa.setAction(a);
        sa.setStatus(actionStatus);
        sa.setStatusChange(actionStatusChange);
        sa.setCreated(work.getCreated());

        EvidenceComment c = work.getComment();
        sa.setWorkDate(work.getWorkDate());
        sa.setAuthor(c.getAuthor());
        sa.setCreated(c.getCreated());
        return sa;
    }

    // assumes no target date
    public EvidenceWithOutcomesBuilder<WORK, OUTCOME, ACTION, COMMENT> withAction(Action a, Integer actionStatus, Boolean actionStatusChange) {
        return withAction(a, actionStatus, actionStatusChange, null);
    }

    public EvidenceWithOutcomesBuilder<WORK, OUTCOME, ACTION, COMMENT> withAction(Action a, Integer actionStatus, Boolean actionStatusChange, DateTime targetDate) {
        return withAction(a, actionStatus, actionStatusChange, targetDate, null, null, 0);
    }

    /**
     * When a smart step comment changes we find the previous entry, update it and save as a new entry
     */
    public EvidenceWithOutcomesBuilder<WORK, OUTCOME, ACTION, COMMENT> withActionCommentChange(Action actionDef, String comment) {
        getWorkActionOrPrevWithSmartStepId(actionDef.getId());
        return this;
    }

    // all properties
    public EvidenceWithOutcomesBuilder<WORK, OUTCOME, ACTION, COMMENT> withAction(Action a, Integer actionStatus, Boolean actionStatusChange, DateTime targetDate, DateTime expiryDate, String commentUnderSmartStep, int activity) {
        ACTION sa = createNewAction(a, actionStatus, actionStatusChange);

        sa.setTarget(targetDate);
        sa.setExpiryDate(expiryDate);
        sa.setActivity(activity);
        sa.setGoalName(commentUnderSmartStep);

        return this;
    }

    public EvidenceWithOutcomesBuilder<WORK, OUTCOME, ACTION, COMMENT> withAssociatedAction(Action a) {
        work.addAssociatedAction(a);
        return this;
    }

    /**
     * Must ensure that all actions that are modified are marked as associated.
     * This should be part of build() but current breaks some tests
     */
    public void ensureActionsAreMarkedAssociated() {
        Set<? extends EvidenceAction> swaList = work.getActions();
        for (EvidenceAction swa : swaList) {
            withAssociatedAction(swa.getAction());
        }
    }

    /** Get work action matching the smartStepId */
    public EvidenceAction getExistingWorkActionWithSmartStepId(long smartStepId) {
        for (EvidenceAction action : work.getActions()) {
            if (action.getActionId() == smartStepId) {
                return action;
            }
        }
        return null;
    }

    /**
     * Return a {@link EvidenceSupportAction} which is either the existing match that is part of this
     * support work, a clone of match from {@link #previousActions}, or a completely new one.
     * If new, the action will have been added via work.addAction(action).
     */
    public EvidenceAction getWorkActionOrPrevWithSmartStepId(long smartStepId) {
        EvidenceAction action = getExistingWorkActionWithSmartStepId(smartStepId);
        if (action != null) {
            return action;
        }

        EvidenceAction prev = getPreviousActionWithSmartStepId(smartStepId);
        if (prev != null) {
            action = createNewAction( prev.action, prev.getStatus(), false); // NOTE: not statusChanged - that should be set if we do change it
            action.setGoalName(prev.goalName);
            action.setActivity(prev.activity);
            action.setExpiryDate(prev.expiryDate);
            action.setTarget(prev.target);
        }
        else {
            action = createNewAction(new Action(smartStepId), EvidenceAction.isRelevant, true);
        }
        work.addAction(action);
        return action;
    }

    private EvidenceAction getPreviousActionWithSmartStepId(long smartStepId) {
        Assert.notNull(previousActions, "Cannot use this unless you've called usingLatestActions() first");
        for (EvidenceAction action : previousActions) {
            if (action.getActionId() == smartStepId) {
                return action;
            }
        }
        return null;
    }
}
