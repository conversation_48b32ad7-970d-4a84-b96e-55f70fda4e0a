package com.ecco.dom;

import java.util.List;

public interface EvidenceWorkVisitor {

    void visit(EvidenceWork e);
    boolean isGenerateDataPoints();
    List<OutcomeDataPoint> getOutcomeDataPoints();

    /*
    void visit(GenericTypeOutcome e);
    //void visit(GenericTypeRisk e);
    void visit(GenericTypeAction e);
    void visit(GenericTypeComment e);
    //void visit(OutcomeDataPoint e);
    */

    //public int getSortDirection();

}
