package com.ecco.dom;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.joda.time.DateTime;

public class WorkDataPoint implements Serializable, EvidenceData {

    DateTime dateTime;
    UUID workId;
    List<OutcomeDataPoint> outcomeDataPoints = new ArrayList<>();

    public void addOutcomeDataPoint(OutcomeDataPoint odp) {
        outcomeDataPoints.add(odp);
    }
    public List<OutcomeDataPoint> getOutcomeDataPoints() {
        return outcomeDataPoints;
    }

    public DateTime getDateTime() {
        return dateTime;
    }
    public void setDateTime(DateTime dateTime) {
        this.dateTime = dateTime;
    }
    @Override
    public UUID getWorkId() {
        return workId;
    }
    public void setWorkId(UUID workId) {
        this.workId = workId;
    }
    public DateTime getCreated() {
        return dateTime;
    }

    @Override
    public int compareTo(EvidenceDataAcceptor o) {
        // if equal, then we return 'less than' to indicate the data point is shown higher than the action
        int cmp = getWorkId().compareTo(o.getWorkId());
        if (cmp == 0) {
            return -1;
        }
        return cmp;
    }

}
