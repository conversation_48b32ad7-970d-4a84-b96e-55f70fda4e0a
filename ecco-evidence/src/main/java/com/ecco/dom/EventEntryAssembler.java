package com.ecco.dom;

import com.ecco.calendar.dom.MedDate;
import org.joda.time.DateTimeZone;
import org.joda.time.Instant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Converts from the ical Entry, to our own EventEntry - CustomEvent varieties.
 * This is for when the rota requires some data 'after the fact' which is less appropriate on standard calendar systems
 * which are future-looking, but ideal for our local events table.
 * Our calendar support handler ensures the ical calendar is updated when the local event is changed, but rota entries bypass
 * the local events table - instead directly using CosmoCalendarService.createRecurringEntryInternal, so we need this to
 * create the local event table entry.
 * This converter creates the local calendar event from the iCal event using similar logic to ItemCollectionAdapter.
 *
 * NB Maintenance is not considered - in terms of updates from the calendar system filtering here also, so we just
 * NB keep the local event data to items that do not overlap the ical data.
 * @see EventResourceAssembler which translates from the iCal system.
 */
public class EventEntryAssembler {

    private static final Logger log = LoggerFactory.getLogger(EventEntryAssembler.class);

    public static CustomEventWithServiceRecipientRota setUpNewCalendarEntry(int serviceRecipientId, String calUid) {
        // we could try to mimic the Entry, but we're currently not syncing - so safer to make it obvious it may be wrong
        // by specifying required properties, but using a different discriminator - 'rota' in CustomEventWithServiceRecipientRota.
        // If we did want to sync, see CustomEventImpl.setUpNewCalendarEntry() and ServiceRecipientCalendarEntryCommandHandler.addNonRecurringCalendarEntry
        // also see new ModificationUid(recurrence.toString()).getRecurrenceId();
        final CustomEventWithServiceRecipientRota result = new CustomEventWithServiceRecipientRota(serviceRecipientId);
        result.setEventDate(MedDate.from(new Instant(0).toDateTime(DateTimeZone.UTC).toLocalDate()));
        result.setUid(calUid);
        return result;
    }
}
