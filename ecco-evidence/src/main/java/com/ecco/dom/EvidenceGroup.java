package com.ecco.dom;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * The evidence group for a piece of evidence. Data from multiple taskDefIds may share a single
 * evidence group so as to appear on the same history.
 * This is used by reporting; data collection and when saving evidence.
 * @see EvidenceWork
 */
@Data
@NoArgsConstructor
public class EvidenceGroup {

    public static List<String> NEEDS_NOTSHARED = List.of("SUPPORTSTAFFNOTES", "MANAGERNOTES");

    public static EvidenceGroup NEEDS = new EvidenceGroup("NEEDS", 19);
    public static EvidenceGroup THREAT = new EvidenceGroup("THREAT", 28);
    public static EvidenceGroup AUDITONLY = new EvidenceGroup("AUDITONLY", -1); // my plan only exists in commands, but the command needs to identify with the EvidenceGroup
    public static EvidenceGroup CHECKLIST = new EvidenceGroup("CHECKLIST", -2); // trying to avoid this having a valid number... I may not succeed

    public static EvidenceGroup SUPPORTSTAFFNOTES = new EvidenceGroup("SUPPORTSTAFFNOTES", 94);
    public static EvidenceGroup ENGAGEMENTCOMMENTS = new EvidenceGroup("ENGAGEMENTCOMMENTS", 19); // 97
    public static EvidenceGroup MANAGERNOTES = new EvidenceGroup("MANAGERNOTES", 103);

    // QUESTIONNAIRES CAN USE THEMSELVES AS THE HISTORY KEY
    public static EvidenceGroup GENERALQUESTIONNAIRE = new EvidenceGroup("GENERALQUESTIONNAIRE", 79);
    public static EvidenceGroup IAPTFEEDBACK = new EvidenceGroup("IAPTFEEDBACK", 89);

    public static EvidenceGroup CUSTOMFORM1 = new EvidenceGroup("CUSTOMFORM1",120);

    private long id;
    private String name;

    private EvidenceGroup(String name, long id) {
        this.name = name;
        this.id = id;
    }

    public String nameAsLowercase() {
        return name.toLowerCase();
    }

    public static EvidenceGroup fromGroupIdName(String name, long id) {
        return new EvidenceGroup(name, id);
    }

}
