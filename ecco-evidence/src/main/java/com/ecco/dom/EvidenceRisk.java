package com.ecco.dom;

import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import com.ecco.serviceConfig.dom.ActionGroup;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;

import javax.annotation.Nonnull;
import javax.persistence.*;

@MappedSuperclass
public abstract class EvidenceRisk extends AbstractLongKeyedEntity implements EvidenceData, Created {

    // lazy here actually means it loads a proxy (ie id) - which is good since we should have loaded all the risks separately
    @ManyToOne(fetch=FetchType.EAGER) // TODO: can we just use riskId here
    @Nonnull
    @JoinColumn(name="riskId")
    ActionGroup risk;

    // 0 = unknown
    // 1 = relevant
    // 2 = not relevant
    @Column
    int relevant;

    // many to one since we can have many riskActionComments to one contact
    @ManyToOne(fetch=FetchType.EAGER) // TODO: ContactSummary projection with first/last/orgName/id
    @JoinColumn(name="contactId")
    ContactImpl author;

    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    DateTime created;

    public void accept(EvidenceWorkVisitor visitor) {
        //visitor.visit(this);
    }

    @Override
    public int compareTo(EvidenceDataAcceptor o) {
        return getWorkId().compareTo(o.getWorkId());
    }

    /*
    Set<evidenceSupportComment> supportComments = new HashSet<evidenceSupportComment>();
    @ManyToMany(cascade={}, fetch=FetchType.EAGER)
    @JoinTable(name = "supportrisks_supportcomments", joinColumns = @JoinColumn(name = "supportRiskId"), inverseJoinColumns = @JoinColumn(name = "supportCommentId"))
    public Set<evidenceSupportComment> getSupportComments() {
        return supportComments;
    }
    public void setSupportComments(Set<evidenceSupportComment> supportComments) {
        this.supportComments = supportComments;
    }
    */

    public ContactImpl getAuthor() {
        return author;
    }

    public int getRelevant() {
        return relevant;
    }

    public ActionGroup getRisk() {
        return risk;
    }

    @Override
    public DateTime getCreated() {
        //if (created == null)
        //    created = new DateTimeUtils().getUtcNow();
        return created;
    }

    public void setRelevant(int relevant) {
        this.relevant = relevant;
    }

    public void setRisk(ActionGroup risk) {
        this.risk = risk;
    }

    @Override
    public void setCreated(DateTime created) {
        this.created = created;
    }

    public void setAuthor(ContactImpl author) {
        this.author = author;
    }

    /*
    // example of client ref: <many-to-one name="clientDetail" class="ClientDetail" column="clientsId" cascade="none" unique="false" not-null="true" not-found="exception" lazy="proxy" fetch="select"/>
    // name, class taken from annotation
    // cascade, lazy & fetch - equivalent to @LazyToOne(LazyToOneOption.PROXY) and @Fetch(FetchMode.SELECT) - see http://docs.jboss.org/hibernate/stable/annotations/reference/en/html_single/index.html#d0e2528
    @ManyToOne(cascade={}, fetch=FetchType.LAZY)
    // unique is by default false (since when its true, its actually a onetoone)
    // NB - manytoone unique="true" doesn't appear to exist in annotations
    // column
    @JoinColumn(name="clientId")
    // NotNull is in the hibernate validation framework, so we trust our code; run in eg pre-insert; rely on db
    // we rely on the db
    // not-null (@Column(nullable=false) - not allowed Column with ManyToOne)
    //@NotNull
    // not-found
    @NotFound(action=NotFoundAction.EXCEPTION)
    public Client getClient() {
        return client;
    }
    */

    /*
    //GenericTypeSupportOutcome supportOutcome;
    Set<SupportRiskAction> supportRiskActions = new HashSet<SupportRiskAction>();

    @ManyToOne(cascade={}, fetch=FetchType.LAZY)
    @NotFound(action=NotFoundAction.EXCEPTION)
    @JoinColumn(name="supportoutcomeId")
    public GenericTypeSupportOutcome getSupportOutcome() {
        return supportOutcome;
    }
    public void setSupportOutcome(GenericTypeSupportOutcome supportOutcome) {
        this.supportOutcome = supportOutcome;
    }
    @Transient
    public Long getServiceRecipientId() {
        return getSupportOutcome().getServiceRecipientId();
    }
    */

    // example is a client who has a set of addresses
    // so add a new address and two objects have been updated - the parent's set, and the child
    // but only one sql entry is required - inserting the child contains all the information because it includes the relationship info, clientId
    // so to be efficient, we say that hibernate should ignore the parent (client) and just use the child (the address histories)
    // so ClientAddressHistory becomes is the owner of the relationship (since it has the clientsId is)
    // inverse=true means ignore the set when saving the parent, i'm a mirror, use the one-to-many class, ClientAddressHistory, to find the links
    // so child.getParent() will be used
    // <set name="clientAddressHistories" cascade="all-delete-orphan" inverse="true" lazy="true" fetch="subselect">
    //    <key column="clientsId"/>
    //    <one-to-many class="ClientAddressHistory"/>
    // </set>
    // so since 'this' is not the owner of the relationship, we specify the property name that is in the mappedBy property on the owner
    // (if we want this side, the "one-to-many side as the owning side", then see http://docs.jboss.org/hibernate/stable/annotations/reference/en/html_single/index.html#d0e2528)
    // recommendation is to use jpa cascade with the extras in @Cascade hibernate annotation - see http://docs.jboss.org/hibernate/stable/annotations/reference/en/html_single/#entity-hibspec-cascade
    // we override with subselect as the default xToMany lazy fetch means @LazyCollection(TRUE), @Fetch(SELECT)
    // if we want the actions when we get the risks, we can make it eager as below
    //@OneToMany(mappedBy="supportRisk", cascade=CascadeType.ALL, fetch=FetchType.EAGER)
    /*
    @OneToMany(mappedBy="supportRisk", cascade=CascadeType.ALL, fetch=FetchType.LAZY, orphanRemoval=true)
    // subselect means that if the actions are accessed at all, they all will be loaded, as opposed to batch=5 for 5 of them at a time
    // if eager then its already done in a join - http://docs.jboss.org/hibernate/stable/annotations/reference/en/html_single/#entity-hibspec-singleassoc-fetching
    @Fetch(FetchMode.SUBSELECT)
    // we specify a new hashset since any set method will override it and any get won't be null (for jsps and the add method)
    public Set<SupportRiskAction> getSupportRiskActions() {
        return supportRiskActions;
    }

    // ensure both sides are linked from the parent
    public void addSupportRiskAction(SupportRiskAction sra){
        sra.setSupportRisk(this);
        sra.setCollectionId(supportRiskActions.size());
        supportRiskActions.add(sra);
    }

    public void setSupportRiskActions(Set<SupportRiskAction> supportRiskActions) {
        this.supportRiskActions = supportRiskActions;
    }
    */
}
