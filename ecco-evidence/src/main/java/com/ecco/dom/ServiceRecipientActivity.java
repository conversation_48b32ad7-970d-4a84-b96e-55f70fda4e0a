package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;

import javax.persistence.*;

import java.util.Set;

/**
 * ReferralActivity is the idea of a more structured client activity whilst under the support of a service - rather than
 * the support plan which is unstructured. Typically this is used for 'external referrals' for support, or details of the school
 * whilst receiving support to attend etc.
 */
@Entity
@Table(name="referralactivities")
public class ServiceRecipientActivity extends EvidenceActivity {

    private static final long serialVersionUID = 1L;


    @ManyToOne(optional=false)
    @JoinColumn(name="serviceRecipientId", insertable=false, updatable=false)
    private BaseServiceRecipient serviceRecipient;

    @Column
    private Integer serviceRecipientId;

    // referencing - optional (because we could create this from several places)
    // we use an exact type for now (not GenericTypeWork)
    // because we are using a referral, only support or threat are available - and even they are linked
    @ManyToOne //, fetch=FetchType.LAZY) - eager assumed, but SupportWork has eager collections
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    EvidenceSupportWork work;

    // the type of activity - eg 'phychotherapy'
    // needs to be optional because we auto-generate it from the support plan
    @ManyToOne
    @JoinColumn(name="activityTypeId")
    //@NotFound(action=NotFoundAction.EXCEPTION)
    ReferralActivityType activityType;

    String clientCalendarEntryId;

    @OneToMany(mappedBy = "referralActivity", cascade= {CascadeType.PERSIST, CascadeType.REMOVE}, fetch = FetchType.EAGER)
    Set<ReferralActivityWorker> workers;


    ServiceRecipientActivity() {
        // For hibernate etc
    }

    public ServiceRecipientActivity(int serviceRecipientId) {
        this.serviceRecipientId = serviceRecipientId;
    }

    // referencing
    public BaseServiceRecipient getServiceRecipient() {
        return serviceRecipient;
    }

    public EvidenceSupportWork getWork() {
        return work;
    }
    public void setWork(EvidenceSupportWork work) {
        this.work = work;
    }

    public ReferralActivityType getActivityType() {
        return activityType;
    }
    public void setActivityType(ReferralActivityType activityType) {
        this.activityType = activityType;
    }

    public String getClientCalendarEntryId() {
        return clientCalendarEntryId;
    }

    public void setClientCalendarEntryId(String clientCalendarEntryId) {
        this.clientCalendarEntryId = clientCalendarEntryId;
    }

    public Set<ReferralActivityWorker> getWorkers() {
        return workers;
    }

    public void setWorkers(Set<ReferralActivityWorker> workers) {
        this.workers = workers;
    }

}
