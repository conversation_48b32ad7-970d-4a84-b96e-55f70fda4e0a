package com.ecco.dom;

import java.io.Serializable;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;


// NB extracting the class id into its own class really helped solve some hql problems (no default constructor)
// this literally is what was embedded, except we expand the MultiId to be the new classname

@Embeddable
public class Contact_Event_MultiId implements Serializable {

    private static final long serialVersionUID = 1L;

    private ContactImpl contact;
    private CustomEventImpl event;

    /*
     * without lazy, causes
        from User_Seminar e where e.multiId.user.id=:userId  select user_semin0_.seminarId as seminarId15_, user_semin0_.userId as userId15_, user_semin0_.status as status15_, user_semin0_.version as version15_ from users_seminars user_semin0_ where user_semin0_.userId=?
        load com.cpdfinder.dom.Seminar  select seminar0_.id as id14_0_, seminar0_.version as version14_0_, seminar0_.accredited as accredited14_0_, seminar0_.dateTime as dateTime14_0_, seminar0_.description as descript5_14_0_, seminar0_.title as title14_0_ from seminars seminar0_ where seminar0_.id=?
        load com.ecco.dom.security.User  select user0_.id as id1_0_ from ourusers user0_ where user0_.id=?
     */
    @ManyToOne(fetch=FetchType.LAZY, optional=false)
    public ContactImpl getContact() {
        return contact;
    }
    @ManyToOne(fetch=FetchType.LAZY, optional=false)
    public CustomEventImpl getEvent() {
        return event;
    }
    public void setContact(ContactImpl contact) {
        this.contact = contact;
    }
    public void setEvent(CustomEventImpl event) {
        this.event = event;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Contact_Event_MultiId that = (Contact_Event_MultiId) o;

        if (contact != null ? !contact.equals(that.contact) : that.contact != null) {
            return false;
        }
        return event != null ? event.equals(that.event) : that.event == null;
    }

    @Override
    public int hashCode() {
        int result;
        result = (contact != null ? contact.hashCode() : 0);
        result = 31 * result + (event != null ? event.hashCode() : 0);
        return result;
    }

}
