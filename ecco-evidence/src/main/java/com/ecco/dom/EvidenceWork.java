package com.ecco.dom;

import java.util.Set;
import java.util.UUID;

import com.ecco.evidence.dom.Signature;
import com.ecco.serviceConfig.dom.Action;
import com.google.common.base.Predicate;

// was mappedsuperclass abstract class, but the only properties were associations
// and associations can't map the 'other side' to mappedsuperclass because they aren't entities (eg what would OutcomeSupport.genericWork actually point to?)
// we could use associationoverride to override them all, but seems a bit pointless
// could also make the class an entity (and not an abstract class - using some appropriate inheritance)
// see http://stackoverflow.com/questions/3227854/hibernate-mapped-superclass-relationships-and-overriding
public interface EvidenceWork extends BaseEntity<UUID>, BaseWorkEvidence {

    void accept(EvidenceWorkVisitor visitor);

    void addAttachment(EvidenceAttachment attachment);

    // currently, one bit of data is associated with one bit of work
    // if we need multiple bits of evidence, then we change this class
    // if we need to re-affirm the work, then we repeat the data with another bit of work
    Set<? extends EvidenceOutcome> getOutcomes();
    //public Set<? extends GenericTypeRisk> getRisks();
    Set<? extends EvidenceAction> getActions();
    Set<? extends EvidenceActivity> getActivities();

    Set<Action> getAssociatedActions();

    void addAction(EvidenceAction action);
    void addActivity(EvidenceActivity activity);
    void addOutcome(EvidenceOutcome outcome);
    void addAnswer(EvidenceAnswer answer);
    boolean addAssociatedAction(Action action);

    /**
     * The <code>taskDefId</code> is the task via which the work was carried out.  To get a list, do:
     * <p>
     * <pre>    select id,name from taskdefinitions;</pre>
     * <p>
     *
     * Each taskDefId maps to only one arbitrary evidenceGroupId. For example:
     * <ul>
     * <li>needsAssessment(19) -> 19</li>
     * <li>needsReduction(27) -> 19</li>
     * <li>needsAssessmentReduction(31) -> 19</li>
     * </ul>
     */
    long getTaskDefId();
    /**
     * The evidence group for this data.  Data from multiple taskDefIds may build up a single
     * evidence set.
     */
    long getEvidenceGroupId();

    // return a parent identifier, if any
    /**
     * @deprecated use getServiceRecipientId
     */
    @Deprecated
    Integer getParentId(); // TODO: refactor to use getServiceRecipientId()
    //public void setParentId(long parentId);

    Signature getSignature();
    void setSignature(Signature signature);

    class UnsignedPredicate implements Predicate<EvidenceWork> {

        private UnsignedPredicate(){}

        @Override
            public boolean apply(EvidenceWork work) {
                return work.getSignature() == null;
            }

        public static Predicate<EvidenceWork> hasNoSignature() {
            return new UnsignedPredicate();
        }
    }

}
