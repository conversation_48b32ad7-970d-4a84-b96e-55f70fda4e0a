package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@javax.persistence.Entity
@DiscriminatorValue("referral")
public class CustomEventWithServiceRecipient extends CustomEventImpl {

    private static final long serialVersionUID = 1L;


    @ManyToOne(optional=false)
    @JoinColumn(name="serviceRecipientId", insertable=false, updatable=false)
    private BaseServiceRecipient serviceRecipient;

    @Column
    private Integer serviceRecipientId;


    protected CustomEventWithServiceRecipient() {
        // for hibernate etc
    }

    public CustomEventWithServiceRecipient(int serviceRecipientId) {
        this.serviceRecipientId = serviceRecipientId;
    }


    public BaseServiceRecipient getServiceRecipient() {
        return serviceRecipient;
    }

    @Override
    public Integer getServiceRecipientId() {
        return serviceRecipientId;
    }

    @Override
    public Long getContactId() {
        return null;
    }

}
