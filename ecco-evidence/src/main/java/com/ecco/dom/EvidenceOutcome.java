package com.ecco.dom;

import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import com.ecco.serviceConfig.dom.Outcome;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;

@MappedSuperclass
public abstract class EvidenceOutcome extends AbstractLongKeyedEntity implements EvidenceData, Created {

    @ManyToOne(fetch=FetchType.EAGER) // TODO: Surely we can make this just outcomeId
    @JoinColumn(name="outcomeId")
    Outcome outcome;
    @DateTimeFormat(style="S-")
    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    DateTime statusChangeDate; // FIXME: LocalDateTime
    // many to one since we can have many riskActionComments to one contact
    @ManyToOne(fetch=FetchType.EAGER) // TODO: ContactView with first/last/org name + id
    @JoinColumn(name="contactId")
    ContactImpl author;
    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    DateTime created;

    public static int unsetRelevant = 0;
    public static int isRelevant = 1;
    public static int notRelevant = 2;
    public static int nonEngaging = 3;
    int status;

    /*
    public void accept(GenericTypeWorkVisitor visitor) {
        visitor.visit(this);
    }
    */

    @Override
    public int compareTo(EvidenceDataAcceptor o) {
        return getWorkId().compareTo(o.getWorkId());
    }

    public ContactImpl getAuthor() {
        return author;
    }

    public Outcome getOutcome() {
        return outcome;
    }

    @Override
    public DateTime getCreated() {
        return created;
    }

    public DateTime getStatusChangeDate() {
        return statusChangeDate;
    }

    // example is a client who has a set of addresses
    // so add a new address and two objects have been updated - the parent's set, and the child
    // but only one sql entry is required - inserting the child contains all the information because it includes the relationship info, clientId
    // so to be efficient, we say that hibernate should ignore the parent (client) and just use the child (the address histories)
    // so ClientAddressHistory becomes is the owner of the relationship (since it has the clientsId is)
    // inverse=true means ignore the set when saving the parent, i'm a mirror, use the one-to-many class, ClientAddressHistory, to find the links
    // so child.getParent() will be used
    // <set name="clientAddressHistories" cascade="all-delete-orphan" inverse="true" lazy="true" fetch="subselect">
    //    <key column="clientsId"/>
    //    <one-to-many class="ClientAddressHistory"/>
    // </set>
    // so since 'this' is not the owner of the relationship, we specify the property name that is in the mappedBy property on the owner
    // (if we want this side, the "one-to-many side as the owning side", then see http://docs.jboss.org/hibernate/stable/annotations/reference/en/html_single/index.html#d0e2528)
    // recommendation is to use jpa cascade with the extras in @Cascade hibernate annotation - see http://docs.jboss.org/hibernate/stable/annotations/reference/en/html_single/#entity-hibspec-cascade
    // we override with subselect as the default xToMany lazy fetch means @LazyCollection(TRUE), @Fetch(SELECT)
    // if we want the actions when we get the risks, we can make it eager as below
    //@OneToMany(mappedBy="supportRisk", cascade=CascadeType.ALL, fetch=FetchType.EAGER)
    /*
    @OneToMany(mappedBy="supportOutcome", cascade=CascadeType.ALL, fetch=FetchType.LAZY, orphanRemoval=true)
    // subselect means that if the actions are accessed at all, they all will be loaded, as opposed to batch=5 for 5 of them at a time
    // if eager then its already done in a join - http://docs.jboss.org/hibernate/stable/annotations/reference/en/html_single/#entity-hibspec-singleassoc-fetching
    @Fetch(FetchMode.SUBSELECT)
    // we specify a new hashset since any set method will override it and any get won't be null (for jsps and the add method)
    public Set<GenericTypeSupportRisk> getSupportRisks() {
        return supportRisks;
    }
    // ensure both sides are linked from the parent
    public void addSupportRisk(GenericTypeSupportRisk sr){
        sr.setSupportOutcome(this);
        sr.setCollectionId(supportRisks.size());
        supportRisks.add(sr);
    }
    public void setSupportRisks(Set<GenericTypeSupportRisk> supportRisks) {
        this.supportRisks = supportRisks;
    }
    */
    public void setOutcome(Outcome outcome) {
        this.outcome = outcome;
    }
    @Override
    public void setCreated(DateTime created) {
        this.created = created;
    }
    public void setAuthor(ContactImpl author) {
        this.author = author;
    }
    public int getStatus() {
        return status;
    }
    public void setStatus(int status) {
        this.status = status;
    }
    public void setStatusChangeDate(DateTime statusChangeDate) {
        this.statusChangeDate = statusChangeDate;
    }
}
