package com.ecco.dom;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;

@Getter
@Setter
@NoArgsConstructor
@MappedSuperclass
public abstract class BaseEvidence extends AbstractLongKeyedEntity {

    private static final long serialVersionUID = 1L;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "serviceRecipientId", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.EXCEPTION)
    protected BaseServiceRecipient serviceRecipient;

    @Column
    private Integer serviceRecipientId;

    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    protected DateTime created;

    // workDate is largely a redundant field through the Work class
    // but indicates when this action actually took place
    @Column
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @DateTimeFormat(style="S-")
    protected DateTime workDate;

    public BaseEvidence(int serviceRecipientId) {
        this.serviceRecipientId = serviceRecipientId;
    }

}