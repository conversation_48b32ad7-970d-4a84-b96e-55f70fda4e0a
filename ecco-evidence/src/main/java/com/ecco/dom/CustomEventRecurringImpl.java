package com.ecco.dom;

import com.ecco.calendar.dom.EventEntry;
import com.ecco.calendar.dom.EventEntryDefinition;
import com.ecco.calendar.dom.MedDate;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.dom.contacts.Contact;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTimeZone;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;
import javax.persistence.*;
import java.util.List;

/**
 * An ecco-representation of a recurring calendar event (not related to rota recurring events).
 * The underlying calendar system is the representation of calendar information. This class is to augment with
 * information that doesn't always fit the calendar model and to better index against srId or contactId.
 * This class was created to allow recurring entries to record non-standard attributes, such as eventCategory (see
 * CalendarEntryCommandHandlerSupport).
 */
@Getter
@Setter
@javax.persistence.Entity
@Table(name="eventsrecurring")
public class CustomEventRecurringImpl extends CustomEventAbstract implements EventEntry, EventEntryDefinition {

    private static final long serialVersionUID = 1L;

    @Transient
    protected final Logger log = LoggerFactory.getLogger(getClass());

    @Override
    public String toString() {
        return "event: "+super.toString();
    }

    @ManyToOne(optional=false)
    @JoinColumn(name="serviceRecipientId", insertable=false, updatable=false)
    private BaseServiceRecipient serviceRecipient;

    @Column
    @Nullable
    private Integer serviceRecipientId;

    @ManyToOne(optional=false)
    @JoinColumn(name="contactId", insertable=false, updatable=false)
    private Individual contact;

    @Column
    @Nullable
    private Long contactId;

    // for hibernate etc
    protected CustomEventRecurringImpl() {}
    public CustomEventRecurringImpl(@Nullable Integer serviceRecipientId, @Nullable Long contactId) {
        this.serviceRecipientId = serviceRecipientId;
        this.contactId = contactId;
    }

    public BaseServiceRecipient getServiceRecipient() {
        return serviceRecipient;
    }

    @Override
    public Integer getServiceRecipientId() {
        return serviceRecipientId;
    }

    public Long getContactId() {
        return contactId;
    }


    // *********************
    // EVENTENTRY EMPTY IMPL - stub out remaining methods


    @Override
    public Integer getEventStatusId() {
        return null;
    }

    @Override
    public Integer getEventStatusRateId() {
        return null;
    }

    @Override
    public boolean isRecurringMasterEntry() {
        return true;
    }

    @Override
    public String getNameToPersist() {
        return null;
    }

    @Override
    public DateTimeZone getTimeZone() {
        return null;
    }

    @Override
    public List<String> getNewCalendarIds() {
        return null;
    }

    @Override
    public void addNewCalendarId(String calendarId) {
    }

    @Override
    public void setOwnerCalendarIdForNewEvent(String calendarId) {
    }

    @Override
    public String getOwnerCalendarIdForNewEvent() {
        return null;
    }

    @Override
    public MedDate getEventDateToPersist() {
        return null;
    }

    @Override
    public MedDate getEventEndDateToPersist() {
        return null;
    }

    @Override
    public boolean isRepeatYears() {
        return false;
    }

    @Override
    public String getLocation() {
        return null;
    }

    // we specifically don't deal with Contact on recurring events
    @Override
    public Contact getLocationContact() {
        return null;
    }

}
