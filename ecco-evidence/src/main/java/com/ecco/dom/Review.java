package com.ecco.dom;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.evidence.dom.TaskStatus;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import com.querydsl.core.annotations.QueryInit;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.util.List;

@Entity
@Table(name = "reviews")
public class Review extends AbstractLongKeyedEntity {

    private static final long serialVersionUID = 1L;

    @SuppressWarnings("unused")
    @QueryInit("*.*")
    @ManyToOne(optional=false, fetch=FetchType.LAZY)
    @JoinColumn(name="serviceRecipientId", insertable=false, updatable=false)
    private BaseServiceRecipient serviceRecipient;

    @SuppressWarnings({"unused", "FieldCanBeLocal"})
    @Column(name="serviceRecipientId")
    private Integer serviceRecipientId;

    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime startDate;

    //long currentOutcomeId;
    private int reviewPage;

    private boolean complete;

    protected Review() {
        // For hibernate etc
    }

    public Review(int serviceRecipientId) {
        this.serviceRecipientId = serviceRecipientId;
    }

    public BaseServiceRecipient getServiceRecipient() {
        return serviceRecipient;
    }

    public int getReviewPage() {
        return reviewPage;
    }
    public void setReviewPage(int reviewPage) {
        this.reviewPage = reviewPage;
    }

    public DateTime getStartDate() {
        return startDate;
    }
    public void setStartDate(DateTime startDate) {
        this.startDate = startDate;
    }

    public boolean isComplete() {
        return complete;
    }
    public void setComplete(boolean complete) {
        this.complete = complete;
    }

    public static class Builder {
        private final Review r;
        public Builder(Review r) {
            this.r= r;
        }
        public Review build() {
            return r;
        }
    }
    public Builder build() {
        return new Builder(this);
    }
    public static Builder builder(BaseServiceRecipient ref, DateTime startDate, int reviewPage, boolean complete) {
        Review review = new Review(ref.getId());
        review.setStartDate(startDate);
        review.setComplete(complete);
        review.setReviewPage(reviewPage);
        return new Builder(review);
    }

    public static List<DateTime> calculateNextReviewDates(String reviewSchedule, DateTime startDate) {
        var now = new LocalDate().toDateTimeAtStartOfDay();
        var end = startDate.plusYears(2);
        var after = now.compareTo(end) > 0 ? now : end;
        return TaskStatus.calculateAllScheduleDates(reviewSchedule, startDate, null, after);
    }

}
