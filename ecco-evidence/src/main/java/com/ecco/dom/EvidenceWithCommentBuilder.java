package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.evidence.EvidenceTask;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.Instant;
import org.joda.time.LocalDateTime;
import org.springframework.security.core.Authentication;

import java.util.UUID;

import static com.ecco.security.SecurityUtil.getUser;

public abstract class EvidenceWithCommentBuilder<WORK extends BaseWorkEvidence, COMMENT extends EvidenceComment> {
    /** The new work being built */
    protected WORK work;

    public EvidenceWithCommentBuilder() {
        // we ensure a work item before we do any operations
        work = newWork();
    }

    public EvidenceWithCommentBuilder(WORK providedWork) {
        // we ensure a work item before we do any operations
        if (providedWork == null) {
            work = newWork();
        } else {
            work = providedWork;
        }
    }

    // we can't instantiate a type paratmeter without also providing the class
    // see http://stackoverflow.com/questions/299998/instantiating-object-of-type-parameter
    // so we just provide an abstract method instead
    protected abstract WORK newWork();

    protected abstract COMMENT newComment(int serviceRecipientId);

    /** Allow subclass access */
    public WORK getWork() {
        return work;
    }

    // NB remove this - this central processing is unused and will be achieved by sharing the load, not saving
    @Deprecated
    public abstract <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T setChild(BaseServiceRecipient child);

    /** for ts generated workUuid we need the option to specify one already */
    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T setId(UUID workUuid) {
        work.setId(workUuid);
        return (T) this;
    }

    /**
     * @param evidenceGroup - e.g. "needs", but for legacy can currently also be a number in a string
     */
    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T fromSource(EvidenceTask task, EvidenceGroup evidenceGroup) {
        work.setTaskDefId(TaskDefinitionNameIdMappings.fromTaskNameToTaskDefId(task.getTaskName()));
        work.setEvidenceGroupId(evidenceGroup.getId());
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withComment(String comment, int minutesSpent, Integer typeId) {
        return withComment(comment)
                .withMinutesSpent(minutesSpent)
                .withType(typeId);
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withComment(String comment) {
        getWorkComment().setComment(comment);
        return (T) this;
    }

    /** mins spent - zero for not specified */
    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withMinutesSpent(int minutesSpent) {
        getWorkComment().setMinutesSpent(minutesSpent);
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withMinutesSpent(Integer minutesSpent) {
        if (minutesSpent != null) {
            withMinutesSpent(minutesSpent.intValue());
        }
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withType(Integer typeId) {
        getWorkComment().setTypeDefId(typeId);
        return (T) this;
    }

    /**
     * Ensure that when a comment is needed that it is constructed correctly
     */
    protected COMMENT getWorkComment() {
        if (work.getComment() != null) {
            return (COMMENT) work.getComment();
        }
        COMMENT c = newComment(work.getServiceRecipientId());
        c.setAuthor((Individual) work.getAuthor());
        c.setCreated(work.getCreated());
        c.setWork(work);
        work.addComment(c);
        return c;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withAuthor(Individual author) {
        work.setAuthor(author);
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withAuthor(Authentication auth) {
        withAuthor(getUser(auth).getContact());
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withContact(Contactable contactable) {
        if (contactable != null) {
            work.setIndividual(contactable.getContact());
        }
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withCreatedDate(DateTime createDate) {
        work.setCreated(createDate);
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withClient(Contactable client) {
        if (client != null) {
            work.setIndividual(client.getContact());
        }
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withClient(Long clientContactId) {
        if (clientContactId != null) {
            ContactImpl individual = new Individual();
            individual.setId(clientContactId);
            work.setIndividual(individual);
        }
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withContact(Long contactId) {
        if (contactId != null) {
            ContactImpl individual = new Individual();
            individual.setId(contactId);
            work.setIndividual(individual);
        }
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withWorkDate(DateTime workDate) {
        work.setWorkDate(workDate);
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withWorkDate(Instant workDate) {
        work.setWorkDate(workDate.toDateTime(DateTimeZone.UTC));
        return (T) this;
    }

    public <T extends EvidenceWithCommentBuilder<WORK, COMMENT>> T withWorkDate(LocalDateTime workDate) {
        work.setWorkDate(workDate.toDateTime(DateTimeZone.UTC));
        return (T) this;
    }

    public WORK build() {
        // FIXME: This would be correct but breaks tests -> ensureActionsAreMarkedAssociated();
        getWorkComment(); // trigger the comment if there isn't one already (could be null text)
        return work;
    }
}
