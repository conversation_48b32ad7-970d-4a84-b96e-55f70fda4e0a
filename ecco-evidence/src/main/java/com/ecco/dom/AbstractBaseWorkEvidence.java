package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.infrastructure.entity.AbstractUUIDKeyedEntity;
import com.querydsl.core.annotations.QueryInit;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.Instant;
import org.joda.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.Assert;

import javax.persistence.*;
import java.util.UUID;

@MappedSuperclass
public abstract class AbstractBaseWorkEvidence extends AbstractUUIDKeyedEntity
        implements BaseWorkEvidence {

    private static final long serialVersionUID = 1L;


    /**
     * The datetime the support work was carried out.  Where this is combined with minsSpent to give a specific
     * duration, then this is the start of the period.
     *
     * This is the time as reported by the API client (so 7am BST will have been stored as 7am)
     */
    @DateTimeFormat
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    DateTime workDate; // FIXME: This should be java.util.LocalDateTime

    @QueryInit("*.*.*") // HERE see breakpoints
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "serviceRecipientId", insertable=false, updatable=false)
    @NotFound(action = NotFoundAction.EXCEPTION)
    BaseServiceRecipient serviceRecipient;
    long taskDefId;
    long evidenceGroupId;

    @Column(name="serviceRecipientId")
    private Integer serviceRecipientId;


    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "contactId")
    ContactImpl author; // TODO: For ToViewModel we just want a projection for displayName

    /** NOTE: This is defaulted to now by {@link com.ecco.infrastructure.hibernate.CreatedInterceptor} if null */
    @Column
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    DateTime created;

    @Transient
    ContactImpl individual;

    @Column
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentInstantAsTimestamp")
    private Instant requestedDelete;

    protected AbstractBaseWorkEvidence() {
        super();
    }

    public AbstractBaseWorkEvidence(int serviceRecipientId) {
        super();
        this.serviceRecipientId = serviceRecipientId;
    }

    public AbstractBaseWorkEvidence(UUID id) {
        super(id);
    }

    @Override
    public UUID getWorkId() {
        return getId();
    }

    @Override
    public int compareTo(EvidenceDataAcceptor o) {
        return getWorkId().compareTo(o.getWorkId());
    }

    @Override
    public Integer getServiceRecipientId() {
        return serviceRecipientId;
    }

    @Override
    public ContactImpl getIndividual() {
        return individual;
    }

    @Override
    public void setIndividual(ContactImpl individual) {
        this.individual = individual;
    }

    @Override
    public DateTime getWorkDate() {
        return workDate;
    }

    public void setWorkDate(LocalDateTime workDate) {
        this.workDate = workDate.toDateTime(DateTimeZone.UTC).withMillisOfSecond(0);
    }

    @Override
    public void setWorkDate(DateTime workDate) {
        this.workDate = workDate.withMillisOfSecond(0);
    }

    @Override
    public ContactImpl getAuthor() {
        return author;
    }

    @Override
    public DateTime getCreated() {
        return created;
    }

    @Override
    public BaseServiceRecipient getServiceRecipient() {
        return serviceRecipient;
    }

    @Override
    public void setCreated(DateTime created) {
        this.created = created;
    }

    @Override
    public void setAuthor(ContactImpl author) {
        this.author = author;
    }


    void setServiceRecipientId(Integer serviceRecipientId) {
        Assert.isNull(serviceRecipient);
        this.serviceRecipientId = serviceRecipientId;
    }

    public Instant getRequestedDelete() {
        return requestedDelete;
    }

    public void setRequestedDelete(Instant requestedDelete) {
        this.requestedDelete = requestedDelete;
    }

    @Override
    public boolean isRequestedDelete() {
        return this.requestedDelete != null;
    }

    @Override
    public void requestDelete() {
        this.setRequestedDelete(new Instant());
    }

    @Override
    public void cancelDeleteRequest() {
        this.requestedDelete = null;
    }

    public long getTaskDefId() {
        return this.taskDefId;
    }

    public long getEvidenceGroupId() {
        return this.evidenceGroupId;
    }

    public void setTaskDefId(long taskDefId) {
        this.taskDefId = taskDefId;
    }

    public void setEvidenceGroupId(long evidenceGroupId) {
        this.evidenceGroupId = evidenceGroupId;
    }
}