package com.ecco.dom.commands;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

@Entity
@DiscriminatorValue("taskUpdate")
public class ReferralTaskUpdateCommand extends ServiceRecipientTaskCommand {
    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ReferralTaskUpdateCommand() {
    }

    public ReferralTaskUpdateCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body, int serviceRecipientId,
            @Nonnull String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName);
    }
}
