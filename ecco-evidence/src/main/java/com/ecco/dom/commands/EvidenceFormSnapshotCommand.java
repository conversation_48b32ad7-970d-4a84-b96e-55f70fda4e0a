package com.ecco.dom.commands;

import lombok.NoArgsConstructor;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

/**
 * Form update command which holds changes required as a JsonPatch in the body of the command,
 * which updates or creates a EvidenceSnapshotJson entry for a particular record (taskName).
 */
@Entity
@DiscriminatorValue("formUpdate")
@NoArgsConstructor
public class EvidenceFormSnapshotCommand extends ServiceRecipientEvidenceCommand {

    public EvidenceFormSnapshotCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                                       long userId, @Nonnull String body, int serviceRecipientId,
                                       String taskName, @Nonnull String evidenceTaskGroupKey) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceTaskGroupKey);
    }

}
