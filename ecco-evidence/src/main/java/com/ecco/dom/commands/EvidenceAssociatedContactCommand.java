package com.ecco.dom.commands;

import lombok.NoArgsConstructor;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

/**
 * Associated Contact command which holds information on a contact associated with evidence.
 * Currently used when on a rota visit 'start'/'stop', where client side createLoneWorkerCommand creates this audit.
 */
@Entity
@DiscriminatorValue("associatedContact")
@NoArgsConstructor
public class EvidenceAssociatedContactCommand extends ServiceRecipientEvidenceCommand {

    // NB we're not going to bother indexing on the contactId

    public EvidenceAssociatedContactCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                                            long userId, @Nonnull String body, int serviceRecipientId,
                                            String taskName, @Nonnull String evidenceTaskGroupKey) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceTaskGroupKey);
    }

}
