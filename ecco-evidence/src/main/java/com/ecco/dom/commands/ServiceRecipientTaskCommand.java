package com.ecco.dom.commands;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import com.ecco.event.CacheEvictEvent;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.config.root.CacheConfig;
import org.joda.time.Instant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.context.ApplicationEvent;


@Entity
@Configurable
public abstract class ServiceRecipientTaskCommand extends ServiceRecipientCommand {

    @Autowired
    @Transient
    protected transient MessageBus<ApplicationEvent> messageBus;

    {
        // This class is instantiated by Hibernate, so not a managed Spring bean.
        injectServices();
    }
    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    @Nonnull
    @Column(nullable = false) // NOTE: nullable in database as other entities don't use it
    protected String taskName;

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ServiceRecipientTaskCommand() {
        super();
    }

    public ServiceRecipientTaskCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body, int serviceRecipientId,
            @Nonnull String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId);
        this.taskName = taskName;
    }

    @PostPersist
    @PostUpdate
    @PostRemove
    void emptyCache() {
        final CacheEvictEvent event = new CacheEvictEvent(this, CacheConfig.CACHE_SR_TASK_COMMANDS, this.getServiceRecipientId());
        messageBus.publishAfterTxEnd(event);
    }

    /** aka taskDefinitionName */
    @Nonnull
    public String getTaskName() {
        return translateTaskName(taskName);
    }

    // see also translateTaskName in TaskAudit.tsx
    public static String translateTaskName(String taskName) {
        // we received commands which should be called 'start'
        if ("startOnService".equals(taskName)) {
            return "start";
        }
        if ("repairDetails".equals(taskName)) {
            return "referralDetails";
        }
        if ("editSource".equals(taskName)) {
            return "sourceWithIndividual";
        }
        /*if ("allocate".equals(taskName)) {
            return "allocateToServices";
        }*/
        return taskName;
    }
}
