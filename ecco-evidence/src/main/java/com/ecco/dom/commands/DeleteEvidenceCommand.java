package com.ecco.dom.commands;

import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("deleteEvidence")
public class DeleteEvidenceCommand extends ServiceRecipientEvidenceCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public DeleteEvidenceCommand() {
    }

    public DeleteEvidenceCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                                 long userId, @Nonnull String body, int serviceRecipientId,
                                 @Nonnull String evidenceGroupKey, @Nonnull String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceGroupKey);
    }

}
