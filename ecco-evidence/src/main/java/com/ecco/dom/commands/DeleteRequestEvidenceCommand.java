package com.ecco.dom.commands;

import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue(DeleteRequestEvidenceCommand.DISCRIMINATOR)
public class DeleteRequestEvidenceCommand extends ServiceRecipientEvidenceCommand {

    public final static String DISCRIMINATOR = "deleteReqEvidence";

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public DeleteRequestEvidenceCommand() {
    }

    public DeleteRequestEvidenceCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                         long userId, @Nonnull String body, int serviceRecipientId,
                         @Nonnull String evidenceGroupKey, @Nonnull String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceGroupKey);
    }

}
