package com.ecco.dom.commands;

import java.util.UUID;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import org.joda.time.Instant;

@Entity
@DiscriminatorValue("areaUpdate")
public class AreaUpdateCommand extends ServiceRecipientCommand {
    @Nonnull
    @Column(nullable = false)
    protected String evidenceGroupKey;

    @Nullable
    @Column(nullable = true)
    protected Long areaDefId;

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public AreaUpdateCommand() {
        super();
    }

    public AreaUpdateCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body, int serviceRecipientId,
            @Nullable Long areaDefId, @Nonnull String evidenceGroupKey) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId);
        this.areaDefId = areaDefId;
        this.evidenceGroupKey = evidenceGroupKey;
    }

    @Nonnull
    public String getEvidenceGroupKey() {
        return evidenceGroupKey;
    }

    public long getAreaDefId() {
        return areaDefId;
    }
}
