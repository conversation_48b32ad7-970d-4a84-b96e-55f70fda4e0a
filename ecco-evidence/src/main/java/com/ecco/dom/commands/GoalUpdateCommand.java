package com.ecco.dom.commands;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

@Entity
@DiscriminatorValue("goalUpdate")
public class GoalUpdateCommand extends GoalCommand {
    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public GoalUpdateCommand() {
    }

    public GoalUpdateCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body, int serviceRecipientId,
            long actionDefId, @Nonnull String evidenceGroupKey, String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, actionDefId, evidenceGroupKey, taskName);
    }
}
