package com.ecco.dom.commands;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.Entity;

import org.joda.time.Instant;


@Entity
public class GoalCommand extends ServiceRecipientEvidenceCommand {

    @Nullable
    @Column(nullable = true)
    protected Long actionDefId;

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public GoalCommand() {
        super();
    }

    public GoalCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body, int serviceRecipientId,
            @Nullable Long actionDefId, @Nonnull String evidenceGroupKey, String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceGroupKey);
        this.actionDefId = actionDefId;
    }

    public long getActionDefId() {
        return actionDefId;
    }
}
