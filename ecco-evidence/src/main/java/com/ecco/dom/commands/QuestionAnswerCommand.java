package com.ecco.dom.commands;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

@Entity
@DiscriminatorValue("questionanswer")
public class QuestionAnswerCommand extends ServiceRecipientEvidenceCommand {

    @Nullable
    @Column(nullable = true)
    protected Long questionDefId;

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public QuestionAnswerCommand() {
        super();
    }

    public QuestionAnswerCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body, int serviceRecipientId,
            @Nonnull Long questionDefId, @Nonnull String evidenceGroupKey, String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName, evidenceGroupKey);
        this.questionDefId = questionDefId;
    }

    public long getQuestionDefId() {
        return questionDefId;
    }

}
