package com.ecco.dom.commands;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

import com.ecco.dom.commands.GoalCommand;

@Entity
@DiscriminatorValue("signWork")
public class SignWorkCommand extends GoalCommand {
    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public SignWorkCommand() {
    }

    public SignWorkCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body, int serviceRecipientId, String evidenceGroupKey, String taskName) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, null, evidenceGroupKey, taskName);
    }
}
