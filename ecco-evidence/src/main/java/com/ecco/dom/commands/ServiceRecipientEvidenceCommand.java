package com.ecco.dom.commands;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.persistence.Column;
import javax.persistence.Entity;
import org.joda.time.Instant;

@Entity
public abstract class ServiceRecipientEvidenceCommand extends ServiceRecipientTaskCommand {

    @Nonnull
    @Column(nullable = false)
    private String evidenceGroupKey;

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ServiceRecipientEvidenceCommand() {
    }

    public ServiceRecipientEvidenceCommand(UUID uuid, Instant remoteCreationTime, long userId, String body,
            int serviceRecipientId, String taskName, String evidenceGroupKey) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId, taskName);
        this.evidenceGroupKey = evidenceGroupKey;
    }

    @Nonnull
    public String getEvidenceGroupKey() {
        return evidenceGroupKey;
    }

}