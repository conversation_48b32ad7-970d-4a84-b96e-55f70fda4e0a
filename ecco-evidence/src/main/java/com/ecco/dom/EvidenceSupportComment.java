package com.ecco.dom;

import com.ecco.evidence.event.EvidenceCommentSavedEvent;
import com.ecco.infrastructure.bus.MessageBus;
import com.querydsl.core.annotations.QueryInit;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.context.ApplicationEvent;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

@Entity
@Table(name="evdnc_supportcomments")
@NamedQueries(@NamedQuery(name = EvidenceSupportComment.BULK_UPDATE_AUTHOR_QUERY,
        query = "update EvidenceSupportComment set author = :newContact where author = :oldContact"))
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
@Setter
@Configurable
public class EvidenceSupportComment extends EvidenceComment implements ServiceRecipientId {

    private static final long serialVersionUID = 1L;

    public static final String BULK_UPDATE_AUTHOR_QUERY = "evidenceSupportComment.bulkUpdateAuthor";

    {
        injectServices(); // This class is instantiated by Hibernate, so not a managed Spring bean.
    }

    public Object readResolve() {
        injectServices();
        return this;
    }

    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    public static class Builder {

        private final EvidenceSupportComment c;

        public Builder(EvidenceSupportComment c) {
            this.c = c;
        }

        public EvidenceSupportComment build() {
            return c;
        }
    }

    public static Builder builder(int serviceRecipientId) {
        EvidenceSupportComment c = new EvidenceSupportComment(serviceRecipientId);
        return new Builder(c);
    }

    @Autowired
    @Transient
    protected transient MessageBus<ApplicationEvent> messageBus;

    @QueryInit("*.*.*")
    @OneToOne(fetch=FetchType.LAZY, targetEntity= EvidenceSupportWork.class)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    protected EvidenceSupportWork work;

    protected BigDecimal mileageTo = BigDecimal.ZERO;
    protected BigDecimal mileageDuring = BigDecimal.ZERO;

    private Integer minsTravel;

    // have an indication and association with the threat to manage this comment
    protected boolean requiresThreatManagement;

    /** @see CommentCommandViewModel#clientStatusId */
    Integer clientStatusId;

    /** @see CommentCommandViewModel#meetingStatusId */
    Integer meetingStatusId;

    /** @see CommentCommandViewModel#locationId */
    Integer locationId;

    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="threatWorkUuid")
    EvidenceThreatWork threatWork;


    public EvidenceSupportComment(Integer serviceRecipientId) {
        super(serviceRecipientId);
    }


    @Override
    @Transient
    public UUID getWorkId() {
        return identifier(work);
    }

    @Override
    public void setWork(BaseWorkEvidence work) {
        this.work = (EvidenceSupportWork) work;
    }

    @PostPersist
    void notifyNewCommentCreated() {
        messageBus.publishAfterTxEnd(new EvidenceCommentSavedEvent(this, getServiceRecipientId()));
    }

    @Override
    public Instant getRelevantInstant() {
        return Instant.ofEpochMilli(work.getWorkDate().getMillis());
    }
}
