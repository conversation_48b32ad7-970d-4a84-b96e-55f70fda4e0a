package com.ecco.dom;

import com.ecco.calendar.dom.*;
import com.ecco.dom.contacts.Contact;
import com.ecco.calendar.core.util.DateTimeUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 'free' events are non-serviceRecipient events - basically they are the users own events.
 * We now have CustomEventWithContact for such 'free' events which could be the user, or another contact
 * but either way this 'free' value will no longer be used.
 */
@Getter
@Setter
@javax.persistence.Entity
@Table(name="events")
@Inheritance(strategy=InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="type")
@DiscriminatorValue("free")
public class CustomEventImpl extends CustomEventAbstract implements EventEntry, EventEntryDefinition {

    private static final long serialVersionUID = 1L;

    @Transient
    protected final Logger log = LoggerFactory.getLogger(getClass());

    // *********************
    // ecco specific aspects
    // *********************

    // associate with an optional action at this level
    // because a calendar entry can be for generic use or specific action use
    // which means its not a great fit anywhere
    @Nullable
    @Column(name="actionId")
    Long actionId;

    // for now, just associate with a set of contacts (eg clients, staff, external agencies etc) - think about groups later
    // we use a joining table since we want info on the join
    // although hibernate is meant to wrap sets, we still get npe (eg when used with EntityTypeEvent) - so make non-null
    @OneToMany(mappedBy = "multiId.event", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private Set<Contact_Event> contactsEvents = new HashSet<>();

    public void addContact_Event(Contact_Event contact_event) {
        if (contactsEvents == null) {
            contactsEvents = new HashSet<>();
        }
        contactsEvents.add(contact_event);
    }

    // domain rich method


    // anyTime would only be relevant for user generated events
    // but since the calendar shows no difference between allDay and anyTime - we don't bother
    //private boolean anyTime = true;
    // add a timeZone for future possibility - and when using cosmo in standalone mode (current user's timeZone can't be used)
    @Transient
    private DateTimeZone timeZone;
    // apply a calendar to attach a new event to
    // in cosmo, calendarId only makes sense for new events
    // from which a uid is generated, so this is not persisted
    @Transient
    private final List<String> newCalendarIds = new ArrayList<>();
    @Transient
    private String ownerCalendarIdForNewEvent;

    @Transient
    private int minutesDuration = 0;

    // the dates and repeating
    @Embedded
    // we need to specify hour and minute in the db because of a hibernate issue
    @AttributeOverrides( {
        @AttributeOverride(name="day", column = @Column(name="eventday")),
        @AttributeOverride(name="month", column = @Column(name="eventmonth")),
        @AttributeOverride(name="year", column = @Column(name="eventyear")),
        @AttributeOverride(name="time.hour", column = @Column(name="eventhour")),
        @AttributeOverride(name="time.minute", column = @Column(name="eventminute"))
    })
    private MedDate eventDate = null;
    @Embedded
    @AttributeOverrides( {
        @AttributeOverride(name="day", column = @Column(name="eventendday")),
        @AttributeOverride(name="month", column = @Column(name="eventendmonth")),
        @AttributeOverride(name="year", column = @Column(name="eventendyear")),
        @AttributeOverride(name="time.hour", column = @Column(name="eventendhour")),
        @AttributeOverride(name="time.minute", column = @Column(name="eventendminute"))
    })
    private MedDate eventEndDate = null;
    private boolean repeatYears;

    // location
    @Column
    private String location;

    @Column(name="eventStatusRateId")
    private Integer eventStatusRateId;

    @Column(name="eventStatusId")
    private Integer eventStatusId;

    // get is used in the aop code to assign the item to a calendar
    @Override
    public List<String> getNewCalendarIds() {
        return newCalendarIds;
    }

    @Override
    public void addNewCalendarId(String calendarId) {
        if (calendarId == null) {
            // we get an exception already, so just make it clearer what the problem is
            throw new IllegalArgumentException("attendee calendarId is null");
        }
        newCalendarIds.add(calendarId);
    }

    @Override
    public void setOwnerCalendarIdForNewEvent(String ownerCalendarId) {
        if (ownerCalendarId == null) {
            // we get an exception already, so just make it clearer what the problem is
            throw new IllegalArgumentException("ownerCalendarId is null");
        }
        ownerCalendarIdForNewEvent = ownerCalendarId;
    }

    @Override
    public String getOwnerCalendarIdForNewEvent() {
        return ownerCalendarIdForNewEvent;
    }

    @Override
    public MedDate getEventDateToPersist() {
        if (eventDate == null) {
            eventDate = new MedDate(true, true, true, true);
        }
        return eventDate;
    }
    @Override
    public MedDate getEventEndDateToPersist() {
        if (eventEndDate == null) {
            eventEndDate = new MedDate(true, true, true, true);
        }
        return eventEndDate;
    }

    /*    @Override
    @Nullable
    public Contact getLocationContact() {
        for (Contact_Event contact_event : contactsEvents) {
            ContactImpl contact = contact_event.getContact();
            // This used to default to the first one, but we're tightening this now to only
            // returning client addresses, never staff ones.
            if (contact.getIsUser() && Individual.class.isAssignableFrom(persistentClass(contact))) {
                // If we find a client, use that address
                Individual possibleClient = (Individual) deproxy(contact);
                User user = possibleClient.getUser();
                if (user.getGroups().contains(Group.CLIENT_GROUP)) {
                    return possibleClient;
                }
            }
        }
        return null;
    }*/

    private void updateDurationFromDates() {
        boolean hasTime = getEventDateToPersist().hasTime();
        // if hasTime, then force the timeEnabled property since its not set when loaded through hibernate
        // and without timeEnabled, the convertToDateTime does not use the time
        // so we could amend MedDate or convertToDateTime, but we want to limit changes elsewhere where possible
        if (hasTime) {
            getEventDateToPersist().setTimeEnabled(true);
            getEventEndDateToPersist().setTimeEnabled(true);
            DateTime jodaStart = DateTimeUtils.convertToDateTime(getEventDateToPersist(), true, true, getTimeZone());
            DateTime jodaEnd = DateTimeUtils.convertToDateTime(getEventEndDateToPersist(), true, true, getTimeZone());
            int minutesDuration = Long.valueOf(new Duration(jodaStart, jodaEnd).getStandardMinutes()).intValue();
            setMinutesDuration(minutesDuration);
        }
    }

    private void updateEndDateFromDuration() {
        // custom-created calendar entries can contain a minutesDuration
        // so convert it to an end date for cosmo which makes no reference to minutesDuration
        // minutesDuration usage appears to come from commit 92d73ee37 which makes reference to the 'fullcalendar dragging abilities which are no longer active.'
        if (getMinutesDuration() > 0) {
            DateTime jodaStart = DateTimeUtils.convertToDateTime(getEventDateToPersist(), true, true, getTimeZone());
            DateTime jodaEnd = jodaStart.plusMinutes(getMinutesDuration());
            MedDate endDate = DateTimeUtils.convertToMedDate(jodaEnd, true);
            setEventEndDate(endDate);
        }
    }

    // ***********************
    // EVENTCALENDAR INTERFACE
    // ***********************
    private static final int MAX_NAME_LENGTH = 40;

    private String name;

    /**
     * set the name of the event to appear on the calendar (and feed to outlook)
     * @param type of the event
     * @param eventStatusId the status set on on individual within contacts_events, but used in the display for prominance
     */
    // location here may be from the referral, not the bound backing object
    //
    public void generateNameForEvent(EventType type, Integer eventStatusId) {
        if (!isGenerated()) {
            return;
        }

        // The name shouldn't actually include the client name or location, they should be in the attendee and location
        // fields on the event, respectively.
        String newName = "";
        if (type == EventType.Meeting || type == EventType.Interview || type == EventType.Review) {
            newName = type.name();
        }

        // search for first ] should do it
        /*if (status != null) {
            newName = "[" + status.getName() + "] " + newName;
        }*/

        // we don't need to set it as a newName, we could label it 'target date' and show the target with a hover
        // however, for cosmo purposes, this what is seen...
        if (getNameToPersist() == null && newName.length() > 0) {
            this.name = StringUtils.abbreviate(newName, CustomEventImpl.MAX_NAME_LENGTH);
        }
    }

    public String getNameToPersist() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    // specify the contact where this meeting is taking place
    @Transient
    private Contact locationContact;

    // useful for debugging
    @Override
    public String toString() {
        return "event: "+super.toString();
    }

    /**
     * Verifies all parameters are consistent
     * UI uses fixed date (click in box) and editable time, with duration
     * TODO merge with EventUtils.createEventForIndividual (which is for generated events)
     */
    public static CustomEventImpl setUpNewCalendarEntry(int day, int month, int year,
                                                        boolean allDay, Integer hour, Integer minute,
                                                        int minutesDuration,
                                                        String tz, @Nullable Integer ownerServiceRecipientIdOptional,
                                                        @Nullable Integer ownerContactIdOptional) {

        // TODO should we supply location when creating (here) - it should default to CustomEventImpl.getLocationContact and find client address? or pick up when loading?

        month++; // js uses 0 as january, but MedDate uses 1-based

        DateTimeZone timeZone = DateTimeZone.forID(tz);

        MedTime time = null;
        if (!allDay) {
            time = new MedTime();
            time.setHour(hour != null ? hour.shortValue() : 0);
            time.setMinute(minute != null ? minute.shortValue() : 0);
        }
        MedDate startDate;
        if (allDay) {
            startDate = new MedDate(day, month, year);
        } else {
            startDate = new MedDate(time, (short) day, (short) month, year);
        }

        CustomEventImpl event = new CustomEventImpl();

        // assign a referral
        // TODO this should probably be some factory method...or facade pattern to wrap  another interface on top
        // currently we don't have a calendar view which sets a worker...apart from the worker logging in themselves - and thats the user
        // NB This currently determines whether an event is for a specific referral, and hence if appointment is found nearby for offline
        if (ownerServiceRecipientIdOptional != null && ownerServiceRecipientIdOptional > 0) {
            // create a CEWSR event from the calendar, if the query param is provided
            event = new CustomEventWithServiceRecipient(ownerServiceRecipientIdOptional);
        }
        if (ownerContactIdOptional != null) {
            event = new CustomEventWithContact(ownerContactIdOptional);
        }

        // ensure that we are not a system-generated entity, so we can edit/delete etc
        event.setGenerated(false);
        event.setRepeatYears(false);

        event.setEventDate(startDate);

        // assume no eventEndDate, but if we are not all day then set a transient minutesDuration
        // which is used on the form, and then calculated upon submission back which setEventEndDate
        if (!allDay) {
            // defaults are for 2 hours
            event.setMinutesDuration(minutesDuration);
        }

        return event;
    }

    /**
     * Verifies all parameters are consistent
     * NB the ONLY change that can be made to an event currently is the attendees - see EventTypeDefinition.preSaveEntity
     */
    public static void setupEditCalendarEntry(CustomEventImpl event) {

        // we did check for an occurrenceDate here - substringAfter(uid, ":");

        if (event == null || event.isGenerated()) {
            throw new IllegalArgumentException("cannot edit generated events");
        }

        event.updateDurationFromDates();
    }

    public static void preSaveCalendarEntry(CustomEventImpl event, boolean isNew,
                                            List<? extends CalendarableEntity> attendees,
                                            @Nonnull Individual ownerContact) {

        event.updateEndDateFromDuration();

        if ((event.getEventType() != null) && (event.getEventType().equals(EventType.Review))) {
            if (!StringUtils.startsWith(event.getNameToPersist(), "Review: ")) {
                event.setName("Review: " + StringUtils.abbreviate(event.getNameToPersist(), 40 - 8));
            }
        }

        if (isNew) {
            event.setOwnerCalendarIdForNewEvent(ownerContact.getCalendarId());
        }

        if ((attendees != null) && (!attendees.isEmpty())) {
            for (CalendarableEntity attendee : attendees) {
                ensureContactOnEvent(event, attendee.getId(), attendee.getCalendarId());
            }
        } else if (isNew) {
            // attach the current user to be consistent in behaviour to all other calendar usages
            // otherwise its a floating entry for any contact - its only link is with the underlying calendar
            ensureContactOnEvent(event, ownerContact.getId(), ownerContact.getCalendarId());
        }
    }

    private static void ensureContactOnEvent(CustomEventImpl event, Long contactId, String calendarId) {
        // CALENDAR TO APPLY
        boolean alreadyExists = event.getContactsEvents().stream().anyMatch(ce -> contactId.equals(ce.getContact().getId()));
        if (alreadyExists) {
            return;
        }

        // only if not already there
        event.addNewCalendarId(calendarId);

        // ASSOCIATED DATA
        // associate an entry with the client, so that we can assign a status - eg did not attend
        // NB a location is attempted to be set in ItemCollectionAdapter.postEntry using a client's contact address
        // BUT none is set here
        final Contact_Event ec = new Contact_Event();
        ec.setEvent(event);
        ContactImpl c = new Individual(); // we assume this but could be passed info to change it
        c.setId(contactId);
        ec.setContact(c);
        event.getContactsEvents().add(ec);
    }

    /**
     * The contact of an event to get its address, for the purposes of saving
     * the address on the calendar event (CalendarEntryCommandHandlerSupport)
     * NB This only works on fully loaded objects - not from EventServiceImpl.getContactWithCalendarId which projects basic id's
     */
    public Contact getLocationContact() {
        return this.locationContact;
    }

    public void setLocationContact(Contact locationContact) {
        this.locationContact = locationContact;
    }

    // only used by ReferralServiceImpl to set an interview date - see createEventForIndividual
    public void setLocation(String location) {
        this.location = location;
    }

}
