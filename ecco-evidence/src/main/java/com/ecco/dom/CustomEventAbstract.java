package com.ecco.dom;

import com.ecco.calendar.dom.EventEntry;
import com.ecco.calendar.dom.EventEntryDefinition;
import com.ecco.calendar.dom.EventType;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import javax.persistence.*;

/**
 * @see EventEntryDefinition
 * @see EventEntry
 */
@Getter
@Setter
@MappedSuperclass
public class CustomEventAbstract extends AbstractLongKeyedEntity {

    public static String EVENTCATEGORY_LISTNAME = "eventCategory";
    public static int EVENTCATEGORY_ANNUAL_LEAVE = 197;
    public static int EVENTCATEGORY_SICK = 198;


    // have a type of event, eg target, review meeting...
    @Type(type="enumEventType")
    private EventType eventType;
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "eventCategoryId")
    private ListDefinitionEntry eventCategory;

    @Column(name = "eventStatusId")
    private Integer eventStatusId;

    // ***********************
    // EVENTCALENDAR INTERFACE
    // ***********************
    @Column(name="isGenerated") // 'generated' is reserved word as of MySQL 5.7
    private boolean generated = false;
    // special case - where events are part of entities and need to processed before they are deleted
    // we can't simply set to null because we lose the reference to take away the tasks
    @Transient
    private boolean forDeletion;
    // add a uid - for linking to another system - eg cosmo
    @Column(name="CAL_UID")
    private String uid;

    public Integer getServiceRecipientId() {
        return null;
    }

    public Long getContactId() {
        return null;
    }


    public boolean isForDeletion() {
        return forDeletion;
    }

    public Integer getEventCategory() {
        return this.eventCategory != null ? this.eventCategory.getId() : null;
    }

    public boolean getForDeletion() {
        return this.forDeletion;
    }

    public void setForDeletion(boolean forDeletion) {
        this.forDeletion = forDeletion;
    }

    public void setEventType(EventType eventType) {
        this.eventType = eventType;
    }

    public void setEventCategory(com.ecco.config.dom.ListDefinitionEntry eventCategory) {
        this.eventCategory = eventCategory;
    }

    public void setGenerated(boolean generated) {
        this.generated = generated;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public boolean isGenerated() {
        return this.generated;
    }

    public String getUid() {
        return this.uid;
    }

    public boolean isRecurringMasterEntry() {
        return false;
    }
}
