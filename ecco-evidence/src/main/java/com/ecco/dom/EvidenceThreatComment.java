package com.ecco.dom;

import java.util.UUID;

import javax.persistence.*;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

import com.querydsl.core.annotations.QueryInit;

@Entity
@Table(name="evdnc_threatcomments")
@NamedQueries(@NamedQuery(name = EvidenceThreatComment.BULK_UPDATE_AUTHOR_QUERY,
        query = "update EvidenceThreatComment set author = :newContact where author = :oldContact"))
public class EvidenceThreatComment extends EvidenceComment implements ServiceRecipientId {

    private static final long serialVersionUID = 1L;

    public static final String BULK_UPDATE_AUTHOR_QUERY = "evidenceThreatComment.bulkUpdateAuthor";


    @QueryInit("*.*.*")
    @OneToOne(fetch=FetchType.LAZY, targetEntity= EvidenceThreatWork.class)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    EvidenceThreatWork work;

    RedAmberGreen levelOverall;

    EvidenceThreatComment() {
        // For Hibernate etc
        super();
    }

    public EvidenceThreatComment(Integer serviceRecipientId) {
        super(serviceRecipientId);
    }


    public EvidenceWork getWork() {
        return work;
    }
    @Override
    @Transient
    public UUID getWorkId() {
        return identifier(work);
    }


    public RedAmberGreen getLevelOverall() {
        return levelOverall;
    }
    public void setLevelOverall(RedAmberGreen levelOverall) {
        this.levelOverall = levelOverall;
    }

    @Override
    public void setWork(BaseWorkEvidence work) {
        this.work = (EvidenceThreatWork) work;
    }

}
