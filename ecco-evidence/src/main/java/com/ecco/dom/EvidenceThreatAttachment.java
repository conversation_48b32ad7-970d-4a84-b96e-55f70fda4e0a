package com.ecco.dom;

import lombok.Data;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

/**
 * Attachments for threat work evidence.
 *
 * @since 08/08/2014
 */
@Entity
@Data
@DiscriminatorValue(EvidenceThreatAttachment.DISCRIMINATOR_VALUE)
public class EvidenceThreatAttachment extends EvidenceAttachment {
    public static final String DISCRIMINATOR_VALUE = "threat";

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "fileid", nullable = false)
    private ServiceRecipientAttachment file;

    @ManyToOne(fetch = FetchType.LAZY, optional = false, targetEntity = EvidenceThreatWork.class)
    @JoinColumn(name = "workUuid", columnDefinition="CHAR(36)", nullable=false)
    private EvidenceWork work;

    public EvidenceThreatAttachment() {
    }

    public EvidenceThreatAttachment(ServiceRecipientAttachment file) {
        this.file = file;
    }

}
