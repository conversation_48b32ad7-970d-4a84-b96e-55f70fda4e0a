package com.ecco.dom;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.ecco.infrastructure.entity.IdNameWithCode;

@Entity
@Table(name = "referralactivitytypes")
public class ReferralActivityType extends IdNameWithCode {

    private static final long serialVersionUID = 1L;

    private boolean schedulable;

    public ReferralActivityType() {
    }

    public ReferralActivityType(Long id, String name) {
        super(id, name);
    }

    public boolean isSchedulable() {
        return schedulable;
    }

    public void setSchedulable(boolean schedulable) {
        this.schedulable = schedulable;
    }
}
