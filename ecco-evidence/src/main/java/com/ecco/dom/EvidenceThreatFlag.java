package com.ecco.dom;

import java.util.UUID;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name="evdnc_threatflags")
@Data
public class EvidenceThreatFlag extends EvidenceBaseFlag {

    private static final long serialVersionUID = 1L;

    // not sure its helpful to have an 'effective date' like other actions
    // as flags are a monitoring tool
    //@DateTimeFormat(style="S-")
    //DateTime workDate;

    @ManyToOne(fetch=FetchType.LAZY, targetEntity= EvidenceThreatWork.class)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    private EvidenceThreatWork work;

    @Override
    public UUID getWorkId() {
        return identifier(work);
    }

    public EvidenceThreatFlag() {}

    public EvidenceThreatFlag(Integer flagDefId) {
        this.flagDefId = flagDefId;
    }
}
