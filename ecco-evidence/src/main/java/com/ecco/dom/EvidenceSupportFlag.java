package com.ecco.dom;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.UUID;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name="evdnc_supportflags")
@Data
public class EvidenceSupportFlag extends EvidenceBaseFlag {

    private static final long serialVersionUID = 1L;

    @ManyToOne(fetch=FetchType.LAZY, targetEntity= EvidenceSupportWork.class)
    @JoinColumn(name="workUuid", columnDefinition="CHAR(36)")
    private EvidenceSupportWork work;

    @Override
    public UUID getWorkId() {
        return identifier(work);
    }

    public EvidenceSupportFlag() {}

    public EvidenceSupportFlag(Integer flagDefId) {
        this.flagDefId = flagDefId;
    }

}
