package com.ecco.dom;

import java.util.Set;
import java.util.UUID;

import org.joda.time.DateTime;

import com.ecco.evidence.dom.AssociatedAction;
import org.joda.time.Instant;

public abstract class BaseOutcomeBasedWorkSummary<ACTION_TYPE> extends BaseWorkSummary {

    protected Set<ACTION_TYPE> actions;
    protected Set<AssociatedAction> associatedActions;

    public BaseOutcomeBasedWorkSummary(UUID id, Instant requestedDelete, Long taskDefId, Integer serviceRecipientId, Integer serviceAllocationId,
                                       ContactImpl author, String comment,
                                       Integer commentTypeId, Integer commentMinutesSpent, UUID signatureId,
                                       DateTime workDate, DateTime createdDate) {
        super(id, requestedDelete, taskDefId, serviceRecipientId, serviceAllocationId, author, comment, commentTypeId, commentMinutesSpent, signatureId, workDate, createdDate);
    }

    public Set<ACTION_TYPE> getActions() {
        return actions;
    }

    public void setActions(Set<ACTION_TYPE> actions) {
        this.actions = actions;
    }

    public Set<AssociatedAction> getAssociatedActions() {
        return associatedActions;
    }

    public void setAssociatedActions(Set<AssociatedAction> associatedActions) {
        this.associatedActions = associatedActions;
    }

}