package com.ecco.dto;

import java.util.HashMap;
import java.util.Map;

/**
 * Type of history overview to show
 */
public enum OverviewType {

    /** all - designed to display all work */
    TYPE_ALL("_eventId_overview"),
    // show support only
    TYPE_SUPPORTONLY("_eventId_supportOnly"),
    TYPE_NEEDSONLY("_eventId_needsOnly"),
    TYPE_TARGETS("_eventId_targets"),
    TYPE_CHANGES("_eventId_changes"),
    TYPE_REVIEWS("_eventId_review"),
    /** show grouped by outcomes */
    TYPE_BYOUTCOME("_eventId_byOutcome"),
    // snapshot - designed to display a point in history
    // which implies a blank from date, and a possible specified to date
    TYPE_SNAPSHOT(null);


    String requestParam;

    static Map<String, OverviewType> requestParamToOverviewTypeMap;

    OverviewType(String requestParam) {
        this.requestParam = requestParam;
    }

    public static OverviewType fromRequestParams(Map<String, String[]> parameterMap) {

        if (requestParamToOverviewTypeMap == null) {
            initializeLookup();
        }
        for (String param : parameterMap.keySet()) {
            OverviewType type = requestParamToOverviewTypeMap.get(param);
            if (type != null) {
                return type;
            }
        }
        throw new IllegalArgumentException("Expected _eventId_ parameter for OverviewType, but couldn't find match in OverviewType");
    }

    protected static synchronized void initializeLookup() {
        requestParamToOverviewTypeMap = new HashMap<>();
        for (OverviewType type : values()) {
            if (type.requestParam != null) {
                requestParamToOverviewTypeMap.put(type.requestParam, type);
            }
        }
    }



}
