package com.ecco.evidence.dom;

import com.ecco.infrastructure.time.Clock;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

public class TaskStatusScheduleUnitTest {

    private final Clock clock = Clock.DEFAULT;
    protected org.joda.time.DateTime now;
    protected java.time.ZonedDateTime nowJdk;

    @Before
    public void setNow() {
        now = clock.now();
        nowJdk = clock.nowJdk();
    }

    @Test
    public void test_simpleNext() {
        // check 'end' makes no difference when testing the next schedule only
        verifySimple("1d", now.plusDays(1));
        verifySimple("2d", now.plusDays(2));

        verifySimple("1m", now.plusMonths(1));
        verifySimple("2m", now.plusMonths(2));

        verifySimple("1y", now.plusYears(1));
        verifySimple("2y", now.plusYears(2));
    }

    @Test
    public void test_complexRepeat() {
        // check 'end' makes no difference when testing the next schedule only
        verifyAll("1d, 2d, end", null, now.plusDays(1), now.plusDays(3));
        verifyAll("1m, 1d, end", null, now.plusMonths(1), now.plusMonths(1).plusDays(1));

        var extendUntil = now.plusMonths(5); // give it 5 months' worth of data
        var totalThisPeriod = Days.daysBetween(now.plusMonths(1), extendUntil).getDays() + 1; // between isn't inclusive
        verifySome("1m, 1d", totalThisPeriod, null, extendUntil, now.plusMonths(1), now.plusMonths(1).plusDays(1), now.plusMonths(1).plusDays(2));
    }

    // catch a scenario where a user completes an older task which has a repeating timer
    // where the repeating timer would create the next due date before 'today'
    // we create the due date in the past - as it's expected to operate on a schedule
    // only the Review.java wants to do this differently, so modify specifically there
    @Test
    public void test_nextCanBeBeforeTodayWithoutEnd() {
        now = now.minusYears(1); // go past 5 months to check test_complexRepeat operates in the past
        test_complexRepeat();
    }

    @Test
    public void test_complexRepeatWithLastCompleted() {
        // happy path where we mimic the first task being completed on 1d
        // which means we obey the schedule exactly
        var lastCompleted = now.plusDays(1);
        verifyAll("1d, 5d, end", lastCompleted, now.plusDays(1), now.plusDays(1).plusDays(5));

        // if the schedule is now+1, now+5 but the lastCompleted is now+3
        // then we get now+1 but the now+5 is past lastCompleted, so reset the schedule at that point, giving lastCompleted+5
        lastCompleted = now.plusDays(3);
        verifyAll("1d, 5d, end", lastCompleted, now.plusDays(1), lastCompleted.plusDays(5));

        // if the schedule is now+1m but our lastCompleted is now+15
        // then we'd expect the 1m from now+15
        lastCompleted = now.plusDays(15);
        verifyAll("1m, 5d, end", lastCompleted, lastCompleted.plusMonths(1), lastCompleted.plusMonths(1).plusDays(5));
    }

    private void verifySimple(String schedule, DateTime expectedNext) {
        var due = TaskStatus.calculateScheduleDate(schedule, now);
        assertThat(due, is(expectedNext));
    }

    private void verifyAll(String schedule, DateTime lastCompleted, DateTime ...expectedFuture) {
        verifyImmediate(schedule, lastCompleted, expectedFuture);

        var dueAll = TaskStatus.calculateAllScheduleDates(schedule, now, lastCompleted, now);
        assertThat(dueAll, contains(expectedFuture));
    }

    private void verifySome(String schedule, long totalSchedules, DateTime lastCompleted, DateTime extendUntil, DateTime ...expectedFuture) {
        // test the immediate next date
        verifyImmediate(schedule, lastCompleted, expectedFuture);

        // verify totalSchedules + 1 (since it goes beyond the end date to ensure we're covered)
        var dueAll = TaskStatus.calculateAllScheduleDates(schedule, now, lastCompleted, extendUntil);
        assertThat((long) dueAll.size(), is(totalSchedules + 1));

        assertThat(dueAll, hasItems(expectedFuture));
    }

    private void verifyImmediate(String schedule, DateTime lastCompleted, DateTime[] expectedFuture) {
        // test the immediate next date
        var nowJdk = JodaToJDKAdapters.localDateToJDk(now.toLocalDate());
        var lastCompletedJdk = lastCompleted != null ? JodaToJDKAdapters.localDateToJDk(lastCompleted.toLocalDate()) : null;
        var nextDate = TaskStatus.calculateNextDueDate(schedule, nowJdk, lastCompletedJdk, nowJdk);
        assertThat(nextDate.toLocalDate(), is(JodaToJDKAdapters.dateTimeToJdk(Arrays.stream(expectedFuture).findFirst().get()).toLocalDate()));
    }

}
