<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ecco-evidence</artifactId>

    <parent>
        <groupId>org.eccosolutions</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0.CI-SNAPSHOT</version>
        <relativePath>../parent/pom.xml</relativePath>
    </parent>

    <dependencies>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-servicerecipient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-service-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-workflow</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>test-support</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
