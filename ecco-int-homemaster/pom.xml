<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ecco.integration</groupId>
    <artifactId>ecco-int-homemaster</artifactId>
    <version>1.0.0.CI-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>ecco-int-homemaster</name>
    <description>ecco-int-homemaster</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <start-class>com.ecco.integration.homemaster.Application</start-class>
        <java.version>17</java.version>
        <mockito.version>3.6.28</mockito.version>
        <tomcat.version>9.0.82</tomcat.version>
        <lombok.version>1.18.20</lombok.version>
        <liquibase.version>3.10.3</liquibase.version>
        <maven-jar-plugin.version>3.1.0</maven-jar-plugin.version>
    </properties>

    <parent>
        <groupId>org.eccosolutions</groupId>
        <artifactId>parent-spring-boot</artifactId>
        <version>1.0.0.CI-SNAPSHOT</version>
        <relativePath>../parent-spring-boot/pom.xml</relativePath>
    </parent>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>${liquibase.version}</version>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ecco.integration</groupId>
            <artifactId>ecco-int-api-default</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-int-core</artifactId>
            <version>1.0.0-CI-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.6</version>
                <configuration>
                    <prefix>git</prefix>
                    <dateFormat>dd.MM.yyyy '@' HH:mm:ss z</dateFormat>
                    <verbose>false</verbose>
                    <dotGitDirectory>${project.basedir}/../.git</dotGitDirectory>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <!-- will use dummy git.properties when deploying to WTP and run-war (expanded)
                         real one is generated direct to target -->
                    <generateGitPropertiesFilename>target/classes/git.properties</generateGitPropertiesFilename>
                    <gitDescribe>
                        <abbrev>7</abbrev>
                        <dirty>-dirty</dirty>
                        <forceLongFormat>true</forceLongFormat>
                    </gitDescribe>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
