package com.ecco.service;

import com.ecco.config.service.ExternalSystemService.ApiType;
import com.ecco.dto.ClientDefinition;
import com.ecco.dto.FlagsDefinition;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.LinkedList;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;


/**
 * In-memory implementation of a client source.
 * Active when NOT running an agent - ie apitType='DEMO' and uri='urn:demo'
 * NB For our ql demo site, we have mapped to a real ql, so if we want these Memoizing entries also
 * then we need to insert duplicate list defs (they can be disabled). For Megan Bryce we just added lagnuage 'en'.
 *
 * @since 23/08/2016
 */
@Component
public class MemoizingDemoClientSource implements ClientImportStrategy {
    private final ConcurrentMap<String, ClientDefinition[]> clients = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, ClientDefinition[]> staff = new ConcurrentHashMap<>();

    // gender businessKey base data will be the id
    private static final String FEMALE = "FEMALE";
    private static final String MALE = "MALE";

    // disability businessKey base data will be the id
    private static final String DISABILITY_NO = "NO";
    private static final String DISABILITY_YES = "YES";
    private static final String DISABILITY_DONTKNOW = "DONTKNOW";
    private static final String DISABILITY_NOTSTATED = "NOTSTATED";

    // sexualOrientation businessKey base data will be the id
    private static final String SEXUALITY_HETEROSEXUAL = "HETERO";
    private static final String SEXUALITY_HOMOSEXUAL = "HOMOSEXUAL";
    private static final String SEXUALITY_LESBIAN = "LESBIAN";
    private static final String SEXUALITY_GAY = "GAY";
    private static final String SEXUALITY_BISEXUAL = "BISEXUAL";
    private static final String SEXUALITY_NOTDISCLOSED = "NOTSTATED";
    private static final String SEXUALITY_NOCAPACITY = "NOTPERSON";

    // language businessKey base data is name
    private static final String LANGUAGE_ENGLISH = "en";
    private static final String LANGUAGE_SPANISH = null; // Spanish TODO Add to base data
    private static final String LANGUAGE_AFRIKAANS = "af";
    private static final String LANGUAGE_URDU = "ur";
    private static final String LANGUAGE_CHINESE = "zh";

    // ethnicOrigin businessKey base data is ethnicOrigin-id
    private static final String ETHNICORIGIN_WHITEBRITISH = "ethnicOrigin-3"; // White: British
    private static final String ETHNICORIGIN_WHITEOTHER = "ethnicOrigin-5"; // White: Other
    private static final String ETHNICORIGIN_ASIAN = "ethnicOrigin-13"; // Asian/Asian British: Pakistani
    private static final String ETHNICORIGIN_MIXED = "ethnicOrigin-7"; // Mixed: White & Black African
    private static final String ETHNICORIGIN_HISPANIC = "ethnicOrigin-9"; // <-Mixed: Other FIXME -> "Hispanic"
    private static final String ETHNICORIGIN_TRAVELLERIRISH = "ethnicOrigin-4"; // <-White: Irish FIXME: -> "Traveller of Irish Origin"
    private static final String ETHNICORIGIN_AFRICAN = "ethnicOrigin-17"; // Black/Black British: African
    private static final String ETHNICORIGIN_WHITEIRISH = "ethnicOrigin-4"; // White: Irish
    private static final String ETHNICORIGIN_CHINESE = "ethnicOrigin-19"; // Chinese/Other: Chinese

    // religion businessKey base data is religion-id
    private static final String RELIGION_CofE = "religion-35"; // Christian - CofE
    private static final String RELIGION_JEWISH = "religion-39"; // Jewish
    private static final String RELIGION_SCIENTOLOGY = "religion-43"; // Other
    private static final String RELIGION_BUDDHIST = "religion-34"; // Buddhist
    private static final String RELIGION_RC = "religion-37"; // Christian - RC
    private static final String RELIGION_C_OTHER = "religion-36"; // Christian - Other
    private static final String RELIGION_JEDI = "religion-43"; // should be  "Jedi"
    private static final String RELIGION_NONE = "religion-33"; // None

    @Override
    public boolean supports(ApiType apiType) {
        return apiType == ApiType.DEMO;
    }

    @Override
    public Iterable<ClientDefinition> queryBuildings(String name, ApiType apiType, URI uri, ClientDefinition exemplar) {
        return queryClients(name, apiType, uri, exemplar); // FIXME: Clearly wrong
    }

    @Override
    public Iterable<ClientDefinition> queryClients(String name, ApiType apiType, URI uri, ClientDefinition exemplar) {
        final ClientDefinition.Builder builder = ClientDefinition.BuilderFactory.create().externalClientSource(name);
        clients.putIfAbsent(name, new ClientDefinition[]{
                builder
                        .firstName("JAMES").lastName("STEWART").birthDate(new LocalDate(1949, 11, 18)).code("47944").genderKey(MALE).externalClientRef("C-1").ni("AD608397Q")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[] {"Old Turnpike"}).town("Norwich").county("Norfolk").postCode("NR16 1SN")
                        .build(),
                builder.firstName("Invalid").lastName("Ni-Number").birthDate(new LocalDate(1939, 2, 2)).code("68930").genderKey(FEMALE).externalClientRef("C-2").ni("LD361476C")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_ASIAN).religionKey(RELIGION_NONE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"14 Chertsey Road"}).town("Woking").county("Surrey").postCode("GU21 5AH")
                        .build(),
                builder.firstName("Jenny").lastName("McHunt").birthDate(new LocalDate(1995, 4, 10)).code("36926").genderKey(FEMALE).externalClientRef("C-3").ni("NG602199P")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_NONE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"1 Nicholson Square"}).town("Lowestoft").county("Suffolk").postCode("NR32 2NN")
                        .build(),
                builder.firstName("Lauryn").lastName("Klein").birthDate(new LocalDate(1943, 10, 10)).code("85606").genderKey(FEMALE).externalClientRef("C-4").ni("AL661102Z")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_JEWISH).disabilityKey(DISABILITY_YES).sexualOrientationKey(SEXUALITY_LESBIAN)
                        .address(new String[]{"4 Akeman Avenue", "Ambrosden"}).town("Bicester").county("Oxfordshire").postCode("OX25 2LJ")
                        .build(),
                builder.firstName("MARYLEE").lastName("MCMAHON").birthDate(new LocalDate(1990, 9, 19)).code("15629").genderKey(FEMALE).externalClientRef("C-5").ni("BY703698S")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEOTHER).religionKey(RELIGION_SCIENTOLOGY).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_BISEXUAL)
                        .address(new String[]{"57 Moorend Park Road"}).town("Cheltenham").county("Gloucestershire").postCode("GL53 0LG")
                        .build(),
                builder.firstName("Shonta").lastName("Owen").birthDate(new LocalDate(1952, 4, 27)).code("98076").genderKey(FEMALE).externalClientRef("C-6").ni("NT533717U")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_MIXED).religionKey(RELIGION_NONE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"18 Fish Street Hill", "London"}).town("London").county("Greater London").postCode("EC3R 6DB")
                        .build(),
                builder.firstName("Mitzi").lastName("Wilcox").birthDate(new LocalDate(1964, 4, 6)).code("26024").genderKey(FEMALE).externalClientRef("C-7").ni("GS307983G")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"69A Sea Road"}).town("Westgate-on-Sea").county("Kent").postCode("CT8 8QG")
                        .build(),
                builder.firstName("Lakendra").lastName("Little").birthDate(new LocalDate(1984, 5, 10)).code("59950").genderKey(FEMALE).externalClientRef("C-8").ni("LA746268W")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_BUDDHIST).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"169 Crown Street"}).town("Aberdeen City").county("Scotland").postCode("AB11")
                        .build(),
                builder.firstName("Shenna").lastName("Gonzales").birthDate(new LocalDate(1993, 3, 28)).code("61234").genderKey(FEMALE).externalClientRef("C-9").ni("GB854986W")
                        .firstLanguageKey(LANGUAGE_SPANISH).ethnicOriginKey(ETHNICORIGIN_HISPANIC).religionKey(RELIGION_RC).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"137 Holborn Street"}).town("Rochdale").county("Greater Manchester").postCode("OL11 4QE")
                        .build(),
                builder.firstName("Megan").lastName("Bryce").birthDate(new LocalDate(1980, 2, 24)).code("21402").genderKey(FEMALE).externalClientRef("C-10").ni("TG703265U")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_TRAVELLERIRISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"Junction Way"}).town("Llandudno Junction").county("Conwy").postCode("LL31")
                        .build(),
                builder.firstName("Leslie").lastName("Peat").birthDate(new LocalDate(1995, 6, 11)).code("13502").genderKey(FEMALE).externalClientRef("C-11").ni("GR507213G")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"7 Nunton"}).town("Isle of Benbecula").county("Na h-Eileanan an Iar").postCode("HS7 5LU")
                        .build(),
                builder.firstName("Jenny").lastName("Rodriguez").birthDate(new LocalDate(1957, 2, 4)).code("20613").genderKey(FEMALE).externalClientRef("C-12").ni("AC862422P")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEOTHER).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_NOTDISCLOSED)
                        .address(new String[]{"31-33 Pant Bryn Isaf"}).town("Llanelli").county("Carmarthenshire").postCode("SA14 9EQ")
                        .build(),
                builder.firstName("Kathleen").lastName("Brewer").birthDate(new LocalDate(1957, 6, 14)).code("94936").genderKey(FEMALE).externalClientRef("C-13").ni("CM460172F")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"3 Ainslie Street"}).town("Dundee").county("Dundee City").postCode("DD5 3RR")
                        .build(),
                builder.firstName("Daniel").lastName("Wissink").birthDate(new LocalDate(1934, 9, 17)).code("13307").genderKey(MALE).externalClientRef("C-14").ni("PR798248W")
                        .firstLanguageKey(LANGUAGE_AFRIKAANS).ethnicOriginKey(ETHNICORIGIN_WHITEOTHER).religionKey(RELIGION_C_OTHER).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HOMOSEXUAL)
                        .address(new String[]{"30 Bennetts Road"}).town("Horsham").county("West Sussex").postCode("RH13 5LA")
                        .build(),
                builder.firstName("Lawrence").lastName("Broers").birthDate(new LocalDate(1992, 9, 8)).code("87805").genderKey(MALE).externalClientRef("C-15").ni("RY522433Q")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_C_OTHER).disabilityKey(DISABILITY_DONTKNOW).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"23B Castlepark Gardens", "Fairlie"}).town("Largs").county("North Ayrshire").postCode("KA29 0BS")
                        .build(),
                builder.firstName("Isaiah").lastName("Poot").birthDate(new LocalDate(1935, 12, 7)).code("99965").genderKey(MALE).externalClientRef("C-16").ni("ZA109347M")
                        .firstLanguageKey(LANGUAGE_URDU).ethnicOriginKey(ETHNICORIGIN_AFRICAN).religionKey(RELIGION_NONE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"98A,Boglemart Street"}).town("Stevenston").county("North Ayrshire").postCode("KA20 3JL")
                        .build(),
                builder.firstName("Marion").lastName("Reilly").birthDate(new LocalDate(1934, 11, 6)).code("45386").genderKey(FEMALE).externalClientRef("C-17").ni("*********")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEIRISH).religionKey(RELIGION_RC).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"Merrington Road", "Bomere Heath"}).town("Shrewsbury").county("Shropshire").postCode("SY4 3PP")
                        .build(),
                builder.firstName("Olivia").lastName("de Vos").birthDate(new LocalDate(1962, 4, 26)).code("90762").genderKey(FEMALE).externalClientRef("C-18").ni("GP697315G")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"Hopcott Road"}).town("Minehead").county("Somerset").postCode("TA24")
                        .build(),
                builder.firstName("Ian").lastName("Abbott").birthDate(new LocalDate(1990, 8, 15)).code("64114").genderKey(MALE).externalClientRef("C-19").ni("WE437665V")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEIRISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_GAY)
                        .address(new String[]{"2 Fox Close", "Hawkesbury Upton"}).town("Badminton").county("South Gloucestershire").postCode("GL9 1EQ")
                        .build(),
                builder.firstName("Kenneth").lastName("Benson").birthDate(new LocalDate(1937, 4, 12)).code("20180").genderKey(MALE).externalClientRef("C-20").ni("QM984323S")
                        .firstLanguageKey(LANGUAGE_CHINESE).ethnicOriginKey(ETHNICORIGIN_CHINESE).religionKey(RELIGION_RC).disabilityKey(DISABILITY_NOTSTATED).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"22 Forth Place"}).town("Newcastle upon Tyne").county("Tyne and Wear").postCode("NE1 4EU")
                        .build(),
                builder.firstName("Phyllis").lastName("Lucas").birthDate(new LocalDate(1984, 6, 17)).code("56590").genderKey(FEMALE).externalClientRef("C-21").ni("PF772228I")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEIRISH).religionKey(RELIGION_JEDI).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_NOCAPACITY)
                        .address(new String[]{"79 Shottery Road"}).town("Stratford-upon-Avon").county("Warwickshire").postCode("CV37 9QQ")
                        .build(),
                // THIS IS THE ITEM THAT IS USED FOR QL DEMOS
                builder.firstName("Allan").lastName("Kirkland").birthDate(new LocalDate(1953, 1, 1)).code("56595")
                        .genderKey(MALE).externalClientRef("13614").ni("PF772248I")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEIRISH).religionKey(RELIGION_JEDI).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_NOCAPACITY).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_NOCAPACITY)
                        .address(new String[]{"Flat 6, Woolf House", "Sandringham Road"}).town("Leatherhead").county("Surrey").postCode("OX11 8TP")
                        .build()
        });

        // Match against our array of clients.
        // See also com.ecco.filters.ClientDetailByExampleFilter.
        LinkedList<ClientDefinition> results = new LinkedList<>();
        for (ClientDefinition client : clients.get(name)) {
            if ((exemplar.getExternalClientRef() == null || client.getExternalClientRef().equals(exemplar.getExternalClientRef()))
                    && (exemplar.getFirstName() == null || StringUtils.startsWithIgnoreCase(client.getFirstName(), exemplar.getFirstName()))
                    && (exemplar.getLastName() == null || StringUtils.startsWithIgnoreCase(client.getLastName(), exemplar.getLastName()))
                    && (exemplar.getBirthDate() == null || client.getBirthDate().equals(exemplar.getBirthDate()))
                    && (exemplar.getGenderKey() == null || client.getGenderKey().equals(exemplar.getGenderKey()))) {
                results.add(client);
            }
        }
        return results;
    }

    @Override
    public Iterable<ClientDefinition> queryStaff(String name, ApiType apiType, URI uri, ClientDefinition exemplar) {
        final ClientDefinition.Builder builder = ClientDefinition.BuilderFactory.create().externalClientSource(name);
        staff.putIfAbsent(name, new ClientDefinition[]{
                builder
                        .firstName("JAMES").lastName("Staff").birthDate(new LocalDate(1949, 11, 18)).code("47944").genderKey(MALE).externalClientRef("W-1").ni("AD608397Q")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[] {"Old Turnpike"}).town("Norwich").county("Norfolk").postCode("NR16 1SN")
                        .build(),
                builder.firstName("Jenny").lastName("McStaff").birthDate(new LocalDate(1995, 4, 10)).code("36926").genderKey(FEMALE).externalClientRef("W-3").ni("NG602199P")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_NONE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"1 Nicholson Square"}).town("Lowestoft").county("Suffolk").postCode("NR32 2NN")
                        .build(),
                builder.firstName("Megan").lastName("Staff").birthDate(new LocalDate(1980, 2, 24)).code("21402").genderKey(FEMALE).externalClientRef("W-10").ni("TG703265U")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_TRAVELLERIRISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"Junction Way"}).town("Llandudno Junction").county("Conwy").postCode("LL31")
                        .build(),
                builder.firstName("Leslie").lastName("Staff").birthDate(new LocalDate(1995, 6, 11)).code("13502").genderKey(FEMALE).externalClientRef("W-11").ni("GR507213G")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"7 Nunton"}).town("Isle of Benbecula").county("Na h-Eileanan an Iar").postCode("HS7 5LU")
                        .build(),
                builder.firstName("Staff").lastName("Rodriguez").birthDate(new LocalDate(1957, 2, 4)).code("20613").genderKey(FEMALE).externalClientRef("W-12").ni("AC862422P")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEOTHER).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_NOTDISCLOSED)
                        .address(new String[]{"31-33 Pant Bryn Isaf"}).town("Llanelli").county("Carmarthenshire").postCode("SA14 9EQ")
                        .build(),
                builder.firstName("Staff").lastName("Brewer").birthDate(new LocalDate(1957, 6, 14)).code("94936").genderKey(FEMALE).externalClientRef("W-13").ni("CM460172F")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"3 Ainslie Street"}).town("Dundee").county("Dundee City").postCode("DD5 3RR")
                        .build(),
                builder.firstName("Staff").lastName("Wissink").birthDate(new LocalDate(1934, 9, 17)).code("13307").genderKey(MALE).externalClientRef("W-14").ni("PR798248W")
                        .firstLanguageKey(LANGUAGE_AFRIKAANS).ethnicOriginKey(ETHNICORIGIN_WHITEOTHER).religionKey(RELIGION_C_OTHER).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HOMOSEXUAL)
                        .address(new String[]{"30 Bennetts Road"}).town("Horsham").county("West Sussex").postCode("RH13 5LA")
                        .build(),
                builder.firstName("Staff").lastName("Broers").birthDate(new LocalDate(1992, 9, 8)).code("87805").genderKey(MALE).externalClientRef("W-15").ni("RY522433Q")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_C_OTHER).disabilityKey(DISABILITY_DONTKNOW).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"23B Castlepark Gardens", "Fairlie"}).town("Largs").county("North Ayrshire").postCode("KA29 0BS")
                        .build(),
                builder.firstName("Isaiah").lastName("Staff").birthDate(new LocalDate(1935, 12, 7)).code("99965").genderKey(MALE).externalClientRef("W-16").ni("ZA109347M")
                        .firstLanguageKey(LANGUAGE_URDU).ethnicOriginKey(ETHNICORIGIN_AFRICAN).religionKey(RELIGION_NONE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"98A,Boglemart Street"}).town("Stevenston").county("North Ayrshire").postCode("KA20 3JL")
                        .build(),
                builder.firstName("Marion").lastName("Staff").birthDate(new LocalDate(1934, 11, 6)).code("45386").genderKey(FEMALE).externalClientRef("W-17").ni("*********")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEIRISH).religionKey(RELIGION_RC).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"Merrington Road", "Bomere Heath"}).town("Shrewsbury").county("Shropshire").postCode("SY4 3PP")
                        .build(),
                builder.firstName("Olivia").lastName("de Vos").birthDate(new LocalDate(1962, 4, 26)).code("90762").genderKey(FEMALE).externalClientRef("W-18").ni("GP697315G")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEBRITISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"Hopcott Road"}).town("Minehead").county("Somerset").postCode("TA24")
                        .build(),
                builder.firstName("Ian").lastName("A Staff").birthDate(new LocalDate(1990, 8, 15)).code("64114").genderKey(MALE).externalClientRef("W-19").ni("WE437665V")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEIRISH).religionKey(RELIGION_CofE).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_GAY)
                        .address(new String[]{"2 Fox Close", "Hawkesbury Upton"}).town("Badminton").county("South Gloucestershire").postCode("GL9 1EQ")
                        .build(),
                builder.firstName("Kenneth").lastName("Benson-Staff").birthDate(new LocalDate(1937, 4, 12)).code("20180").genderKey(MALE).externalClientRef("W-20").ni("QM984323S")
                        .firstLanguageKey(LANGUAGE_CHINESE).ethnicOriginKey(ETHNICORIGIN_CHINESE).religionKey(RELIGION_RC).disabilityKey(DISABILITY_NOTSTATED).sexualOrientationKey(SEXUALITY_HETEROSEXUAL)
                        .address(new String[]{"22 Forth Place"}).town("Newcastle upon Tyne").county("Tyne and Wear").postCode("NE1 4EU")
                        .build(),
                builder.firstName("Phyllis").lastName("Lucas-Staff").birthDate(new LocalDate(1984, 6, 17)).code("56590").genderKey(FEMALE).externalClientRef("W-21").ni("PF772228I")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEIRISH).religionKey(RELIGION_JEDI).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_NOCAPACITY)
                        .address(new String[]{"79 Shottery Road"}).town("Stratford-upon-Avon").county("Warwickshire").postCode("CV37 9QQ")
                        .build(),
                builder.firstName("Allan").lastName("Staff").birthDate(new LocalDate(1953, 1, 1)).code("56595")
                        .genderKey(MALE).externalClientRef("13614").ni("PF772248I")
                        .firstLanguageKey(LANGUAGE_ENGLISH).ethnicOriginKey(ETHNICORIGIN_WHITEIRISH).religionKey(RELIGION_JEDI).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_NOCAPACITY).disabilityKey(DISABILITY_NO).sexualOrientationKey(SEXUALITY_NOCAPACITY)
                        .address(new String[]{"Flat 6, Woolf House", "Sandringham Road"}).town("Leatherhead").county("Surrey").postCode("OX11 8TP")
                        .build()
        });

        // Match against our array of clients.
        // See also com.ecco.filters.ClientDetailByExampleFilter.
        LinkedList<ClientDefinition> results = new LinkedList<>();
        for (ClientDefinition client : staff.get(name)) {
            if ((exemplar.getExternalClientRef() == null || client.getExternalClientRef().equals(exemplar.getExternalClientRef()))
                    && (exemplar.getFirstName() == null || StringUtils.startsWithIgnoreCase(client.getFirstName(), exemplar.getFirstName()))
                    && (exemplar.getLastName() == null || StringUtils.startsWithIgnoreCase(client.getLastName(), exemplar.getLastName()))
                    && (exemplar.getBirthDate() == null || client.getBirthDate().equals(exemplar.getBirthDate()))
                    && (exemplar.getGenderKey() == null || client.getGenderKey().equals(exemplar.getGenderKey()))) {
                results.add(client);
            }
        }
        return results;
    }

    public FlagsDefinition queryClientFlags(String externalSourceName, ApiType apiType, URI uri, String externalClientRef) {
        // TODO
        return null;
    }
}
