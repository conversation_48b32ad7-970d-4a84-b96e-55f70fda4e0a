package com.ecco.data.client.actors;

import com.ecco.webApi.contacts.address.AddressHistoryViewModel;
import com.ecco.webApi.contacts.address.AddressViewModel;
import com.ecco.webApi.contacts.address.ServiceRecipientAddressLocationChangeCommandViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.viewModels.Result;
import com.google.common.collect.ImmutableMap;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;

public class AddressActor extends BaseActor {

    public AddressActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<AddressHistoryViewModel[]> getAddressHistoryByServiceRecipientIdOrderByValidFromDesc(int serviceRecipientId) {
        return restTemplate.getForEntity(
                apiBaseUrl + "service-recipients/{serviceRecipientId}/address-location/history/",
                AddressHistoryViewModel[].class, ImmutableMap.of("serviceRecipientId", serviceRecipientId));
    }

    public ResponseEntity<Result> createAddress(AddressViewModel addressViewModel) {
        ResponseEntity<Result> responseEntity = restTemplate.postForEntity(
                apiBaseUrl + "addresses/", addressViewModel, Result.class);
        Assert.state(responseEntity.getStatusCode().is2xxSuccessful(), responseEntity.getBody().getMessage());
        return responseEntity;
    }

    public ResponseEntity<Result> changeAddress(int addressId, int srId, Integer contactId) {
        ServiceRecipientAddressLocationChangeCommandViewModel cmd = new ServiceRecipientAddressLocationChangeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, srId, contactId);
        cmd.addressLocation = ChangeViewModel.changeNullTo(addressId);
        return executeCommand(cmd);
    }

    public ResponseEntity<Result> deleteAddress(int surrogateId, int srId, Integer contactId) {
        ServiceRecipientAddressLocationChangeCommandViewModel cmd = new ServiceRecipientAddressLocationChangeCommandViewModel(BaseCommandViewModel.OPERATION_REMOVE, srId, contactId);
        cmd.id = surrogateId; // see HistoryItemViewModel#id
        return executeCommand(cmd);
    }

    public ResponseEntity<Result> changeBuilding(int buildingId, int addressId, int srId, Integer contactId, LocalDate validFrom) {
        ServiceRecipientAddressLocationChangeCommandViewModel cmd = new ServiceRecipientAddressLocationChangeCommandViewModel(BaseCommandViewModel.OPERATION_ADD, srId, contactId);
        cmd.addressLocation = ChangeViewModel.changeNullTo(addressId);
        cmd.buildingLocation = ChangeViewModel.changeNullTo(buildingId);
        if (validFrom != null) {
            cmd.validFrom = ChangeViewModel.changeNullTo(validFrom.atStartOfDay());
        }
        return executeCommand(cmd);
    }

}
