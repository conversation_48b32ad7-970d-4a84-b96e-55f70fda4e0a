package com.ecco.data.client.actors;

import com.ecco.webApi.calendar.EventController;
import com.google.common.collect.ImmutableMap;
import com.ecco.calendar.core.webapi.EventResource;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.hateoas.Link;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nullable;
import java.util.List;

import static org.hamcrest.Matchers.equalTo;

/**
 * High level interactions which equate to an action a user might make via a UI, except this does it directly
 * on the API.
 */
public class CalendarActor extends BaseActor {

    public static final DateTimeFormatter YYYYMMDD = DateTimeFormat.forPattern("yyyyMMdd");

    public CalendarActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<String> getAvailability(String calendarId, DateTime from, DateTime to) {

        ImmutableMap<String, String> args = ImmutableMap.of("calendarId", calendarId, "from", YYYYMMDD.print(from), "to", YYYYMMDD.print(to) );
        ResponseEntity<String> response = restTemplate.getForEntity(apiBaseUrl + "calendar/availability/{calendarId}/{from}-{to}",
                String.class, args);
        return response;
    }

    public static String extractEventUuid(String href) {
        return EventController.FIRST_FINDEVENTSID.apply(href);
    }

    public ResponseEntity<EventResource[]> getEntries(String ...entryUuids) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl + "calendar/event/ids/")
                .queryParam("eventIds", entryUuids)
                .build();
        ResponseEntity<EventResource[]> response = restTemplate.getForEntity(uri.toUriString(), EventResource[].class);
        return response;
    }

    public ResponseEntity<EventResource[]> getEntriesByTime(LocalDate start, LocalDate end, @Nullable Long[] contactIds, @Nullable List<String> calendarIds) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl + "calendar/event/search")
                .queryParam("start", start.toString(ISODateTimeFormat.date()))
                .queryParam("end", end.toString(ISODateTimeFormat.date()))
                .queryParam("contactId", contactIds)
                .queryParam("calendarId", calendarIds)
                .build();
        ResponseEntity<EventResource[]> response = restTemplate.getForEntity(uri.toUriString(), EventResource[].class);
        Assert.state(equalTo(HttpStatus.OK).matches(response.getStatusCode()), "getEntriesByTime failed");
        return response;
    }

    public ResponseEntity<EventResource[]> getEntriesByLink(Link link) {
        ResponseEntity<EventResource[]> response = restTemplate.getForEntity(urlAtBaseUrlHost(link.getHref()), EventResource[].class);
        Assert.state(equalTo(HttpStatus.OK).matches(response.getStatusCode()), "getEntriesByLink failed");
        return response;
    }

}
