<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <artifactId>ecco-war</artifactId>
    <packaging>war</packaging>

    <parent>
        <groupId>org.eccosolutions</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0.CI-SNAPSHOT</version>
        <relativePath>../parent/pom.xml</relativePath>
    </parent>

    <profiles>
        <profile>
            <id>check</id> <!-- Check against OWASP vulnerability database and write to target/dependency-check-report.html -->
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.owasp</groupId>
                        <artifactId>dependency-check-maven</artifactId>
                        <configuration>
                            <suppressionFile>${project.basedir}/dependency-check-suppressions.xml</suppressionFile>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>embedded</id>
        </profile>
        <profile>
            <id>dev</id>

            <build>
                <plugins>
                    <plugin>
                        <groupId>org.mortbay.jetty</groupId>
                        <artifactId>jetty-maven-plugin</artifactId>
                        <configuration>
                            <!-- 0 disables hot deployment -->
                            <scanIntervalSeconds>0</scanIntervalSeconds>
                            <connectors>
                                <connector implementation="org.eclipse.jetty.server.nio.SelectChannelConnector">
                                    <port>8888</port>
                                    <maxIdleTime>60000</maxIdleTime>
                                </connector>
                            </connectors>
                        </configuration>
                    </plugin>
                </plugins>
            </build>

        </profile>

        <profile>
            <id>dev-oracle</id>

            <build>
                <plugins>
                    <plugin>
                        <groupId>org.mortbay.jetty</groupId>
                        <artifactId>jetty-maven-plugin</artifactId>
                        <configuration>
                            <!-- 0 disables hot deployment -->
                            <scanIntervalSeconds>0</scanIntervalSeconds>
                            <connectors>
                                <connector implementation="org.eclipse.jetty.server.nio.SelectChannelConnector">
                                    <port>8888</port>
                                    <maxIdleTime>60000</maxIdleTime>
                                </connector>
                            </connectors>
                        </configuration>
                    </plugin>
                </plugins>
            </build>

        </profile>

        <profile>
            <id>remote</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.mortbay.jetty</groupId>
                        <artifactId>jetty-maven-plugin</artifactId>
                        <configuration>
                            <scanIntervalSeconds>0</scanIntervalSeconds>
                            <connectors>
                                <connector implementation="org.eclipse.jetty.server.nio.SelectChannelConnector">
                                    <!-- sanity check - to avoid we are thinking we are on localhost -->
                                    <!-- avoid 8081 since oracle xe operates on that by default -->
                                    <port>8082</port>
                                    <maxIdleTime>60000</maxIdleTime>
                                </connector>
                            </connectors>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- run production with profiles prod,tomcat,...email/calendar... -->
        <profile>
            <id>prod</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <configuration>
                            <debug>false</debug>
                            <optimize>true</optimize>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>dockerBuild</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <phase>install</phase>
                                <goals>
                                    <goal>dockerBuild</goal>
                                </goals>
                            </execution>

                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.6</version>

                <configuration>
                    <prefix>git</prefix>
                    <dateFormat>yyyy-MM-dd'T'HHmmssz</dateFormat>
                    <verbose>false</verbose>
                    <dotGitDirectory>${project.basedir}/../.git</dotGitDirectory>
                    <gitDescribe>
                        <abbrev>7</abbrev>
                        <dirty>-dirty</dirty>
                        <forceLongFormat>true</forceLongFormat>
                    </gitDescribe>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <configuration>
                    <from>
                        <image>gcr.io/google-appengine/tomcat:8</image>
                    </from>
                    <to>
                        <image>gcr.io/ecco-uat/ecco-tomcat8</image>
                        <!-- For GCR helper we can use gcloud components install docker-credential-gcr
                             For either then configure docker with it by:
                             docker-credential-gcloud configure-docker
                             and then use a new shell to pick up the config -->
                        <credHelper>gcloud</credHelper>
                        <tags>
                            <tag>${git.branch}</tag>
                            <tag>${git.commit.id.abbrev}</tag>
                            <tag>built-${git.build.time}</tag>
                        </tags>
                    </to>
                    <container>
                        <appRoot>/var/lib/tomcat/webapps/ROOT</appRoot>
                        <environment>
                            <JAVA_OPTS>-Dlog4j.configuration=log4j-cloud.xml -Duser.timezone=UTC -Djava.locale.providers=JRE,SPI</JAVA_OPTS>
                            <CATALINA_OPTS>-Ddb.extraContexts=acceptanceTests -Denv=dev -Ddb=h2 -Dliquibase=CREATE -Dcookie.insecure=false -Dcookie.samesite=strict</CATALINA_OPTS>
                        </environment>
                    </container>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>appengine-maven-plugin</artifactId>
                <version>1.3.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.cargo</groupId>
                <artifactId>cargo-maven2-plugin</artifactId>
                <configuration>
                    <!-- The rest is set in pluginManagement in parent/pom.xml -->
                    <configuration>
                        <type>standalone</type>
                        <properties>
                            <cargo.servlet.port>8888</cargo.servlet.port>
                            <!--<cargo.logging>high</cargo.logging>-->
                        </properties>
                    </configuration>
                    <deployables>
                        <deployable>
                            <groupId>org.eccosolutions</groupId>
                            <artifactId>ecco-war</artifactId>
                            <type>war</type>
                            <!-- Not compatible with having context.xml - not sure why.
                            <location>ecco/target/ecco-war-1.0.0.CI-SNAPSHOT</location>
                            -->
                            <properties>
                                <context>ecco-war</context>
                            </properties>
                        </deployable>
                    </deployables>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>8.1.10.v20130312</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <id>enforce-ban-duplicate-classes</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <banDuplicateClasses>
                                    <ignoreClasses>
                                        <!-- example of ignoring one specific class -->
                                        <!-- <ignoreClass>com.xyz.i18n.Messages</ignoreClass> -->

                                        <!-- example of ignoring with wildcards -->
                                        <!-- <ignoreClass>org.apache.commons.logging.*</ignoreClass> -->
                                    </ignoreClasses>
                                    <findAllDuplicates>true</findAllDuplicates>
                                </banDuplicateClasses>
                            </rules>
                            <fail>false</fail>
                            <!--  We want to make this true -->
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <version>1.0-alpha-3</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-dom</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-service</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-reports</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-calendar</artifactId>
            <type>jar</type>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-rota</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-hr</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-group-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-hr</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-submissions-sp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>test-support</artifactId>
        </dependency>


        <!-- this gets us the BaseTest -->
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>test-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>test-entities</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-upload-web</artifactId>
        </dependency>

        <!-- tightly integrate calendar code -->
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-calendar-core</artifactId>
            <type>jar</type>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-calendar-cosmo</artifactId>
            <type>jar</type>
            <exclusions>
                <exclusion>
                    <artifactId>quartz</artifactId>
                    <groupId>opensymphony</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-offline</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-security-ldap</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-web-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-workflow-activiti</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <!-- we use ServletRequestUtils -->

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <!-- we use orm - previously this has been test scope -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet.jsp</groupId>
            <artifactId>javax.servlet.jsp-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.github.jknack</groupId>
            <artifactId>handlebars-springmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>

        <!-- pdf - for rotaActivityInvoice report - unused

        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>bouncycastle</groupId>
                    <artifactId>bcprov-jdk14</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>bouncycastle</groupId>
                    <artifactId>bcmail-jdk14</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        -->
        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
        </dependency>

        <!-- simply for the xml namespace - http://forum.springsource.org/showthread.php?t=78586 -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-taglibs</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-jcache</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-transcoder</artifactId>
        </dependency>

        <dependency>
            <!-- For stuff missed out of batik 1.8 -->
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>xmlgraphics-commons</artifactId>
            <version>1.5</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>

