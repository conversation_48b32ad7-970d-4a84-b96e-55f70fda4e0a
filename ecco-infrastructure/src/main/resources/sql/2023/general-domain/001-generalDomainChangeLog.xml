<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
    logicalFilePath="2023/general-domain">

    <include file="classpath:sql/2023/general-domain/003-cleanup.xml"/>

    <changeSet id="DEV-2423-contacts-per-allocationId" author="adamjhamer">
        <createTable tableName="svccat_contacts">
            <column name="serviceAllocationId" type="INT"/>
            <column name="contactId" type="BIGINT"/>
        </createTable>
        <addForeignKeyConstraint baseColumnNames="serviceAllocationId" baseTableName="svccat_contacts"
                                 constraintName="fk_svccatcontacts_cat"
                                 referencedColumnNames="id" referencedTableName="services_projects" referencesUniqueColumn="true"/>
        <addForeignKeyConstraint baseColumnNames="contactId" baseTableName="svccat_contacts"
                                 constraintName="fk_svccatcontacts_cont"
                                 referencedColumnNames="id" referencedTableName="contacts" referencesUniqueColumn="true"/>
    </changeSet>

    <changeSet id="DEV-2433-taskDefinitions-add-jobDetails" author="adamjhamer">
        <insert tableName="taskdefinitions">
            <column name="id" valueNumeric="150"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="DEDICATED_TASK"/>
            <column name="friendlyName" value="jobDetails"/>
            <column name="name" value="jobDetails"/>
        </insert>
    </changeSet>

    <!-- until we did 'delete' below, to manually get past this failure, we can do:
        alter table cal_eventstatus drop column plannedStartInstant
        CREATE INDEX idx_lonework_startEnd ON cal_eventstatus (eventUid);
    -->
    <changeSet author="adamjhamer" id="DEV-2443-cal_eventstatus_plannedStart">
        <validCheckSum>8:3b6d5d5623f90cd172dce69d379c9464</validCheckSum>
        <delete tableName="cal_eventstatus"/>
        <dropNotNullConstraint tableName="cal_eventstatus" columnName="startInstant" columnDataType="DATETIME"/>
        <dropIndex tableName="cal_eventstatus" indexName="idx_lonework_startEnd"/>
        <addColumn tableName="cal_eventstatus">
            <column name="plannedStartInstant" type="DATETIME"/>
        </addColumn>
        <addNotNullConstraint tableName="cal_eventstatus" columnName="plannedStartInstant" columnDataType="DATETIME"/>
        <createIndex tableName="cal_eventstatus" indexName="idx_lonework_planStart">
            <column name="plannedStartInstant"/>
        </createIndex>
    </changeSet>

    <!-- add a buildingId to the services_projects -->
    <!-- we don't especially need ref integrity here to bldg_fixed -->
    <changeSet author="adamjhamer" id="DEV-2456-svccat-bldg">
        <addColumn tableName="services_projects">
            <column name="buildingId" type="INT"/>
        </addColumn>
    </changeSet>

    <!-- with an ordered /usersWithAccessTo we find our first contact needs to match the contacts table for canCreateLinearWorkflow setup initial assessment -->
    <changeSet id="DEV-2079-ecco_adam-calendar-name" author="adamjhamer" context="!test-data AND !1.1-apply-encryption" dbms="!oracle">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="1">select count(1) from cosmo_users where username='ecco_adam'</sqlCheck>
        </preConditions>
        <update tableName="cosmo_users">
            <column name="firstname" value="Adam"/>
            <column name="lastname" value="Hamer"/>
            <where>id=4</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2428-pre-populate-svccat_contacts" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from svccat_contacts</sqlCheck>
        </preConditions>
        <createTable tableName="svccat_contacts_copy">
            <column name="serviceAllocationId" type="INT"/>
            <column name="contactId" type="BIGINT"/>
        </createTable>
        <sql>
            -- referring agencies
            insert into svccat_contacts (contactId, serviceAllocationId)
            select r.agencyId, sr.serviceallocationId from referrals r inner join servicerecipients sr on r.serviceRecipientId = sr.id  where r.agencyid is not null;

            -- referring professionals
            insert into svccat_contacts (contactId, serviceAllocationId)
            select r.referrerId, sr.serviceallocationId from referrals r inner join servicerecipients sr on r.serviceRecipientId = sr.id inner join contacts c on r.referrerId=c.id where r.referrerid is not null and c.companyId is not null;

            -- signposted agencies
            insert into svccat_contacts (contactId, serviceAllocationId)
            select r.signpostedagencyid, sr.serviceallocationId from referrals r inner join servicerecipients sr on r.serviceRecipientId = sr.id  where r.signpostedagencyid is not null;

            -- 'contacts', from svcREC_contacts, but only where professional (can't be agency in this version)
            insert into svccat_contacts (contactId, serviceAllocationId)
            select rc.contactId, sr.serviceAllocationId from svcrec_contacts rc inner join servicerecipients sr on rc.serviceRecipientId = sr.id inner join contacts c on rc.contactId=c.id where rc.contactId is not null and c.companyId is not null;

            -- select * from svccat_contacts;
            insert into svccat_contacts_copy (contactId, serviceAllocationId) select t1.contactId, t1.serviceAllocationId from svccat_contacts t1 group by t1.contactId, t1.serviceAllocationId;
            -- select * from svccat_contacts_copy;
            delete from svccat_contacts;
            insert into svccat_contacts (contactId, serviceAllocationId) select contactId, serviceAllocationId from svccat_contacts_copy;

            -- select contactId from svccat_contacts group by contactId,serviceallocationId having count(contactId) > 1;
        </sql>
        <dropTable tableName="svccat_contacts_copy"/>
    </changeSet>

    <changeSet id="DEV-2476-default-0-version-column" author="nealeu">
        <addNotNullConstraint tableName="actions" columnName="version"
                              columnDataType="int" defaultNullValue="0"/>
        <addDefaultValue tableName="actions" columnName="version"
                         columnDataType="int" defaultValueNumeric="0"/>

        <addNotNullConstraint tableName="actiongroups" columnName="version"
                              columnDataType="int" defaultNullValue="0"/>
        <addDefaultValue tableName="actiongroups" columnName="version"
                         columnDataType="int" defaultValueNumeric="0"/>

        <addNotNullConstraint tableName="outcomes" columnName="version"
                              columnDataType="int" defaultNullValue="0"/>
        <addDefaultValue tableName="outcomes" columnName="version"
                         columnDataType="int" defaultValueNumeric="0"/>
    </changeSet>

    <changeSet id="DEV-2476-default-false-disabled-column" author="nealeu">
        <addDefaultValue tableName="actiongroups" columnName="disabled"
                         columnDataType="int" defaultValueBoolean="false"/>

        <addDefaultValue tableName="outcomes" columnName="disabled"
                         columnDataType="int" defaultValueBoolean="false"/>
    </changeSet>

    <changeSet id="DEV-2476-add-eMAR-outcome-and-actions" author="nealeu"
        context="base-data,acceptanceTests,eMAR">
        <insert tableName="outcomes">
            <column name="discriminator_orm" value="standard"/>
            <column name="id" valueNumeric="89"/>
            <column name="uuid" value="10010000-0089-babe-babe-dadafee1600d"/>
            <column name="name" value="eMAR"/>
        </insert>
        <insert tableName="actiongroups">
            <column name="id" valueNumeric="92"/>
            <column name="uuid" value="10010000-0092-babe-babe-dadafee1600d"/>
            <column name="name" value="group1"/>
            <column name="outcomeId" valueNumeric="89"/>
        </insert>
        <insert tableName="actions">
            <column name="id" valueNumeric="152"/>
            <column name="uuid" value="10010000-0152-babe-babe-dadafee1600d"/>
            <column name="name" value="oral medication"/>
            <column name="actionGroupId" valueNumeric="92"/>
        </insert>
        <insert tableName="actions">
            <column name="id" valueNumeric="153"/>
            <column name="uuid" value="10010000-0153-babe-babe-dadafee1600d"/>
            <column name="name" value="injected medication"/>
            <column name="actionGroupId" valueNumeric="92"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2476-add-eMAR-to-accommodation-test-only" author="nealeu"
               context="acceptanceTests">
        <insert tableName="servicetypes_outcomesupports">
            <column name="servicetypeId" valueNumeric="2"/>
            <column name="outcomeId" valueNumeric="89"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2379-welcome-notice" author="adamjhamer">
        <!-- placeholder for a welcome notice -->
        <insert tableName="cfg_form_definitions">
            <column name="uuid" value="00000000-0000-0000-0000-00000000f001"/>
            <column name="created" valueDate="2023-06-16T09:00:00"/>
            <column name="name" value="notice"/>
            <column name="body" value="{}"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-2379-welcome-notice-validUuid" author="adamjhamer">
        <update tableName="cfg_form_definitions">
            <column name="uuid" value="*************-babe-babe-dadafee1600d"/>
            <where>uuid='00000000-0000-0000-0000-00000000f001'</where>
        </update>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-2501-cal_eventstatus_more">
        <addColumn tableName="cal_eventstatus">
            <column name="plannedEndInstant" type="DATETIME"/>
        </addColumn>
        <addNotNullConstraint tableName="cal_eventstatus" columnName="plannedEndInstant" columnDataType="DATETIME"/>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-2501-cal_eventstatus_more2">
        <addColumn tableName="cal_eventstatus">
            <column name="resourceCalendarId" type="VARCHAR(40)"/>
            <!--<column name="type"/>-->
        </addColumn>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-2501-cal_eventstatus_more3">
        <dropColumn tableName="cal_eventstatus">
            <column name="resourceCalendarId"/>
        </dropColumn>
        <addColumn tableName="cal_eventstatus">
            <column name="resourceContactId" type="BIGINT"/>
            <column name="demandContactId" type="BIGINT"/>
        </addColumn>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-2501-cal_eventstatus_schedId">
        <addColumn tableName="cal_eventstatus">
            <column name="demandScheduleId" type="BIGINT"/>
        </addColumn>
        <addForeignKeyConstraint baseTableName="cal_eventstatus" baseColumnNames="demandScheduleId"
                                 constraintName="fk_evtstatus_schedId"
                                 referencedTableName="appointmentschedules" referencedColumnNames="id"/>
    </changeSet>

    <changeSet author="adamjhamer" id="DEV-2455-schedules_intervalType">
        <addColumn tableName="appointmentschedules">
            <column name="intervalType" type="VARCHAR(5)"/>
        </addColumn>
    </changeSet>
    <changeSet author="adamjhamer" id="DEV-2455-schedules_intervalType-update">
        <update tableName="appointmentschedules">
            <column name="intervalType" value="WK"/>
            <where>intervalType is null</where>
        </update>
    </changeSet>

    <changeSet id="DEV-1576-referralaspects-agreement9" author="adamjhamer">
        <insert tableName="taskdefinitions">
            <column name="id" valueNumeric="151"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="AGREEMENT"/>
            <column name="friendlyName" value="agreement9"/>
            <column name="name" value="agreement9"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1576-add_svcrec_agreement9_signature_uuids" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="agreement9SignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1576-add_agreement9_date" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="agreement9signed" type="DATETIME"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="agreement9Status" type="BOOLEAN"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-1576-referralaspects-agreement10" author="adamjhamer">
        <insert tableName="taskdefinitions">
            <column name="id" valueNumeric="152"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="AGREEMENT"/>
            <column name="friendlyName" value="agreement10"/>
            <column name="name" value="agreement10"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1576-add_svcrec_agreement10_signature_uuids" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="agreement10SignatureId" type="CHAR(36)"/> <!-- Remains nullable as it's optional -->
        </addColumn>
    </changeSet>
    <changeSet id="DEV-1576-add_agreement10_date" author="adamjhamer">
        <addColumn tableName="referrals">
            <column name="agreement10signed" type="DATETIME"/>
        </addColumn>
        <addColumn tableName="referrals">
            <column name="agreement10Status" type="BOOLEAN"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2511-add-individual-knownAs" author="nealeu">
        <addColumn tableName="contacts">
            <column name="knownAs" type="VARCHAR(128)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2511-add-individual-pronouns" author="nealeu">
        <addColumn tableName="contacts">
            <column name="pronounsId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint
                constraintName="fk_cntct_prnn_listdef"
                baseTableName="contacts"
                baseColumnNames="pronounsId"
                referencedTableName="cfg_list_definitions"
                referencedColumnNames="id"
        />
    </changeSet>
    <changeSet id="DEV-2511-default-pronouns" author="nealeu">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="199"/>
            <column name="businessKey" valueNumeric="199"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="pronouns"/>
            <column name="name" value="she/her"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="200"/>
            <column name="businessKey" valueNumeric="200"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="pronouns"/>
            <column name="name" value="he/him"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="201"/>
            <column name="businessKey" valueNumeric="201"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="pronouns"/>
            <column name="name" value="they/them"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="202"/>
            <column name="businessKey" valueNumeric="202"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="pronouns"/>
            <column name="name" value="name/name"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-1965-guidance-form-placeholder" author="adamjhamer">
        <!-- placeholder for a welcome notice -->
        <insert tableName="cfg_form_definitions">
            <column name="uuid" value="00000000-0000-0000-0000-00000000f002"/>
            <column name="created" valueDate="2023-10-18T09:00:00"/>
            <column name="name" value="guidance"/>
            <column name="body" value="{}"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-1965-guidance-form-validUuid" author="adamjhamer">
        <update tableName="cfg_form_definitions">
            <column name="uuid" value="*************-babe-babe-dadafee1600d"/>
            <where>uuid='00000000-0000-0000-0000-00000000f002'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2379-validVersion" author="adamjhamer">
        <update tableName="cfg_form_definitions">
            <column name="version" value="0"/>
            <where>version is null</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2529-contract-servicecharges" author="adamjhamer">
        <insert tableName="cfg_list_definitions">
            <column name="id" valueNumeric="203"/>
            <column name="businessKey" valueNumeric="203"/>
            <column name="version" valueNumeric="0"/>
            <column name="listName" value="contract-types"/>
            <column name="name" value="service charges"/>
            <column name="disabled" valueBoolean="false"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2561-questions-parameters" author="adamjhamer">
        <addColumn tableName="questions">
            <column name="parameters" type="CLOB">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <update tableName="questions">
            <column name="parameters" value="{}"/>
            <where>parameters is null</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2520-grp-discriminator" author="adamjhamer">
        <addColumn tableName="grp_activities">
            <column name="discriminator_orm" type="VARCHAR(4)"/>
        </addColumn>
        <update tableName="grp_activities">
            <column name="discriminator_orm" value="supp"/>
            <where>discriminator_orm is null</where>
        </update>
        <addNotNullConstraint tableName="grp_activities" columnName="discriminator_orm" columnDataType="VARCHAR(4)"/>
    </changeSet>

    <!-- create 'contacts' for source of referrals since the last time (DEV-2428-pre-populate-svccat_contacts) -->
    <!-- we did create the 'contacts' for the db, so match in the code -->
    <changeSet id="DEV-2604-pre-populate-source-contacts" author="adamjhamer">
        <validCheckSum>8:66077b345a493a5cc535de204763a70c</validCheckSum>
        <validCheckSum>8:597f6b69c19f31b94e22da063477d0e4</validCheckSum>
        <sql>
            -- referring agencies, where there isn't a professional
            insert into svcrec_contacts (serviceRecipientId, contactId)
            select r.servicerecipientId, r.agencyId from referrals r inner join servicerecipients sr on r.serviceRecipientId = sr.id
                left join svcrec_contacts sc on sc.serviceRecipientId=sr.id and sc.contactId=r.agencyId
                where sc.servicerecipientId is null
                and r.agencyid is not null
                and r.referrerId is null
                ;

            -- referring professionals, where there is no agency
            insert into svcrec_contacts (serviceRecipientId, contactId)
            select r.servicerecipientId, r.referrerId from referrals r inner join servicerecipients sr on r.serviceRecipientId = sr.id
                inner join contacts c on r.referrerId = c.id
                left join svcrec_contacts sc on sc.serviceRecipientId=sr.id and sc.contactId=r.referrerId
                where sc.servicerecipientId is null
                and r.referrerId is not null
                and r.agencyId is not null
                and c.companyId is not null
                ;

            -- referring individuals
            insert into svcrec_contacts (serviceRecipientId, contactId)
            select r.servicerecipientId, r.referrerId from referrals r inner join servicerecipients sr on r.serviceRecipientId = sr.id
                                                                       inner join contacts c on r.referrerId = c.id
                                                                       left join svcrec_contacts sc on sc.serviceRecipientId=sr.id and sc.contactId=r.referrerId
            where sc.servicerecipientId is null
              and r.referrerId is not null
              and r.agencyId is null
              and c.companyId is null
            ;

            -- signposted agencies
            insert into svcrec_contacts (serviceRecipientId, contactId)
            select r.servicerecipientId, r.signpostedagencyId from referrals r inner join servicerecipients sr on r.serviceRecipientId = sr.id
                                                                     left join svcrec_contacts sc on sc.serviceRecipientId=sr.id and sc.contactId=r.signpostedagencyid
            where sc.servicerecipientId is null
              and r.signpostedagencyid is not null
            ;
        </sql>
    </changeSet>
    <!-- we didn't mean to add agencies and professionals above -->
    <changeSet id="DEV-2604-pre-populate-source-contacts-fix" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="DEV-2604-pre-populate-source-contacts" author="adamjhamer"/>
        </preConditions>
        <sql>
            -- delete all organisations (hopefully no extra data yet saved on few sites without this fix)
            delete from svcrec_contacts sc inner join referrals r on sc.serviceRecipientId=r.servicerecipientid and sc.contactId=r.agencyId
            ;

            -- RE-ADD organisations from above fixed changed
            -- referring agencies, where there isn't a professional
            insert into svcrec_contacts (serviceRecipientId, contactId)
            select r.servicerecipientId, r.agencyId from referrals r inner join servicerecipients sr on r.serviceRecipientId = sr.id
                                                                     left join svcrec_contacts sc on sc.serviceRecipientId=sr.id and sc.contactId=r.agencyId
            where sc.servicerecipientId is null
              and r.agencyid is not null
              and r.referrerId is null
            ;

            -- ADD individuals missed from above fixed changed
            -- referring individuals
            insert into svcrec_contacts (serviceRecipientId, contactId)
            select r.servicerecipientId, r.referrerId from referrals r inner join servicerecipients sr on r.serviceRecipientId = sr.id
                                                                       inner join contacts c on r.referrerId = c.id
                                                                       left join svcrec_contacts sc on sc.serviceRecipientId=sr.id and sc.contactId=r.referrerId
            where sc.servicerecipientId is null
              and r.referrerId is not null
              and r.agencyId is null
              and c.companyId is null
            ;

        </sql>
    </changeSet>

    <changeSet id="DEV-2611-listDef-pendingstatuses-prep-fk" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="referrals" constraintName="FKC8E0F87695A1B6A"/>
    </changeSet>
    <changeSet id="DEV-2611-listDef-pendingstatuses-prep-mssql-remove" author="adamjhamer" dbms="!mysql">
        <dropDefaultValue tableName="pendingstatuses" columnName="id"/>
        <dropPrimaryKey tableName="pendingstatuses"/>
    </changeSet>
    <changeSet id="DEV-2611-listDef-pendingstatuses-prep2" author="adamjhamer">
        <modifyDataType tableName="pendingstatuses" columnName="id" newDataType="INT"/>
        <modifyDataType tableName="referrals" columnName="pendingstatusId" newDataType="INT"/>
    </changeSet>
    <changeSet id="DEV-2611-listDef-pendingstatuses-prep-mssql-reinstate" author="adamjhamer" dbms="!mysql">
        <addNotNullConstraint tableName="pendingstatuses" columnName="id" columnDataType="INT"/>
        <addPrimaryKey tableName="pendingstatuses" columnNames="id"/>
    </changeSet>
    <changeSet id="DEV-2611-listDef-pendingstatuses-migrationId" author="adamjhamer">
        <addColumn tableName="pendingstatuses">
            <column name="migrationId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <!-- STEP 2: fill that sequence -->
    <changeSet id="DEV-2611-listDef-pendingstatuses-enable" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="pendingstatuses" uniqueKeyColumns="id"
                      newIdColumn="migrationId"
                      sequenceName="id_name"
        />
    </changeSet>
    <changeSet id="DEV-2611-listDef-pendingstatuses-copy-rows" author="adamjhamer">
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey)
            SELECT migrationId, 0, 'pendingStatus', name, concat('pendingStatus-', migrationId) FROM pendingstatuses;
        </sql>
    </changeSet>
    <changeSet id="DEV-2611-listDef-pendingstatuses-update-refs" author="adamjhamer">
        <update tableName="referrals">
            <column name="pendingstatusid" valueComputed="(SELECT d.migrationId FROM pendingstatuses d WHERE d.id = referrals.pendingstatusid)"/>
        </update>
        <addForeignKeyConstraint baseTableName="referrals" baseColumnNames="pendingstatusid" constraintName="FK_ref_psId" referencedTableName="cfg_list_definitions"
                                 referencedColumnNames="id"/>
    </changeSet>

    <!-- apply incidents to existing manager/senior-manager -->
    <changeSet id="DEV-2628-incidents-permission-managers" author="adamjhamer">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager -->
            <column name="authority" value="ROLE_ADMININCIDENT"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_ADMININCIDENT"/>
        </insert>
    </changeSet>
    <!-- have 'incidents' access (like reports) to show 'my' incidents -->
    <!-- have all incidents / adminincident for seeing all -->
    <changeSet id="DEV-2628-incidents-permission-aaa" author="adamjhamer">
        <delete tableName="group_authorities">
            <where>authority='ROLE_ADMININCIDENT' and group_id!=34</where>
        </delete>
    </changeSet>
    <changeSet id="DEV-2628-incidents-permission-view" author="adamjhamer">
        <validCheckSum>8:5dd0d418cc6b32c1b9ea2a9d7fa041f3</validCheckSum>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager -->
            <column name="authority" value="ROLE_INCIDENTS"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_INCIDENTS"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-2628-incidents-permission-team" author="adamjhamer">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_TEAMINCIDENTS"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2632-actions-defaultText" author="adamjhamer">
        <addColumn tableName="actions">
            <column name="initialText" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-2632-actions-defaultText-more" author="adamjhamer">
        <renameColumn tableName="actions" oldColumnName="initialText" newColumnName="initialText" columnDataType="VARCHAR(1024)"/>
    </changeSet>

    <changeSet id="DEV-2619-sr-servicetype" author="adamjhamer">
        <addColumn tableName="servicerecipients">
            <column name="servicetypeid" type="BIGINT">
                <!-- null means use the current mechanism - see BaseServiceRecipient -->
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseTableName="servicerecipients" baseColumnNames="servicetypeid" constraintName="FK_sr_servicetype"
                                 referencedTableName="servicetypes" referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-2667-listdef-order-populate-byalpha" author="adamjhamer">
        <!-- order everything by name, NB we need the idColumnName - it needs 'id' in the query -->
        <customChange class="com.ecco.infrastructure.liquibase.AddOrderColumnChange"
                      tableName="cfg_list_definitions"
                      collectionColumnName="listName" entryColumnName="name"
                      idColumnName="id"
                      orderColumnName="orderBy"
                      orderJumpBy="5"
        />
    </changeSet>
    <!-- anything with 'scoreListName' setting should be amended to be sorted by id -->
    <!-- anything with the below should be amended to be sorted by id
        //  - select statusChangeReasonListName from actions;
        //  - select * from servicetypes_taskdefsettings where name='actionDefaultListName';
    -->
    <changeSet id="DEV-2667-listdef-order-populate-byid" author="adamjhamer">
        <!-- order some by id -->
        <customChange class="com.ecco.infrastructure.liquibase.AddOrderColumnChange"
                      tableName="cfg_list_definitions"
                      collectionColumnName="listName" entryColumnName="id"
                      orderColumnName="orderBy"
                      orderJumpBy="5"
                      where="listName in ('default-checks', 'riskMatrixLikelihood', 'riskMatrixSeverity')"
        />
    </changeSet>
    <!-- set orderby to null so the server listdef order of name is obeyed -->
    <!-- as if these were included in 'DEV-2667-listdef-order-populate-byid' changeset -->
    <changeSet id="DEV-2667-listDef-orderby-grp-tidy" author="adamjhamer">
        <update tableName="cfg_list_definitions">
            <column name="orderby" valueNumeric="NULL" />
            <where>listName in ('activityType', 'venue')</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2637-referrals-status" author="adamjhamer">
        <renameColumn tableName="referrals" oldColumnName="receivingService" newColumnName="arch_receivingService" columnDataType="boolean"/>
    </changeSet>

    <changeSet id="DEV-2637-servicetype-accom" author="adamjhamer">
        <validCheckSum>8:c70bc8845e723a9f56682c21b19fe2c9</validCheckSum>
        <renameColumn tableName="servicetypes" oldColumnName="accommodation" newColumnName="arch_accommodation" columnDataType="boolean"/>
        <renameColumn tableName="servicetypes" oldColumnName="anonymousSupport" newColumnName="arch_anonymousSupport" columnDataType="boolean"/>
        <renameColumn tableName="servicetypes" oldColumnName="contractRequired" newColumnName="arch_contractRequired" columnDataType="boolean"/>
        <renameColumn tableName="servicetypes" oldColumnName="logTime" newColumnName="arch_logTime" columnDataType="boolean"/>
    </changeSet>
    <changeSet id="DEV-2637-servicetype-accom2" author="adamjhamer">
        <dropNotNullConstraint tableName="servicetypes" columnName="arch_accommodation" columnDataType="boolean"/>
        <dropNotNullConstraint tableName="servicetypes" columnName="arch_anonymousSupport" columnDataType="boolean"/>
        <dropNotNullConstraint tableName="servicetypes" columnName="arch_contractRequired" columnDataType="boolean"/>
        <dropNotNullConstraint tableName="servicetypes" columnName="arch_logTime" columnDataType="boolean"/>
    </changeSet>

    <changeSet id="DEV-2637-referralaspects-friendlyname" author="adamjhamer">
        <renameColumn tableName="taskdefinitions" oldColumnName="friendlyName" newColumnName="arch_friendlyName" columnDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="DEV-2638-taskDefinitions-add-clientDetails2" author="adamjhamer">
        <validCheckSum>8:32fe81a681332ea146170d62611753f4</validCheckSum>
        <insert tableName="taskdefinitions">
            <column name="id" valueNumeric="153"/>
            <column name="version" valueNumeric="0"/>
            <column name="display" valueBoolean="true"/>
            <column name="displayOverview" valueBoolean="true"/>
            <column name="isexternal" valueBoolean="false"/>
            <column name="internal" valueBoolean="true"/>
            <column name="type" value="DEDICATED_TASK"/>
            <column name="name" value="clientWithContact2"/>
            <column name="metadata" value="{}"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-614-commands-rename" author="adamjhamer">
        <renameTable oldTableName="clientcommands" newTableName="clnt_commands"/>
        <renameTable oldTableName="reportdefinitioncommands" newTableName="repdef_commands"/>
    </changeSet>

    <changeSet id="DEV-614-commands-draft2" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="DEV-614-commands-draft" author="adamjhamer" changeLogFile="2023/general-domain"/>
            </not>
        </preConditions>
        <addColumn tableName="bldg_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="cfg_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="clnt_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="cont_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="fin_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="grp_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="hr_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="repdef_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="svcrec_commands_archive">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="usr_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-614-commands-draft-idx" author="adamjhamer">
        <createIndex tableName="bldg_commands" indexName="idx_bldgcmd_draft">
            <column name="draft"/>
            <column name="serviceRecipientId"/>
            <column name="userId"/>
        </createIndex>
        <!--cfg_commands-->
        <createIndex tableName="clnt_commands" indexName="idx_clntcmd_draft">
            <column name="draft"/>
            <column name="clientId"/>
            <column name="userId"/>
        </createIndex>
        <createIndex tableName="cont_commands" indexName="idx_contcmd_draft">
            <column name="draft"/>
            <column name="contactId"/>
            <column name="userId"/>
        </createIndex>
        <createIndex tableName="fin_commands" indexName="idx_fincmd_draft">
            <column name="draft"/>
            <column name="serviceRecipientId"/>
            <column name="userId"/>
        </createIndex>
        <createIndex tableName="grp_commands" indexName="idx_grpcmd_draft">
            <column name="draft"/>
            <column name="activityUuid"/>
            <column name="userId"/>
        </createIndex>
        <createIndex tableName="hr_commands" indexName="idx_hrcmd_draft">
            <column name="draft"/>
            <column name="workerId"/>
            <column name="userId"/>
        </createIndex>
        <!--
        repdef_commands
        svcrec_commands_archive
        usr_commands
        -->
    </changeSet>

    <!--
    select max(orderexecuted) from DATABASECHANGELOG;
    insert into DATABASECHANGELOG (id, author, filename, dateexecuted, orderexecuted, exectype, md5sum, description, comments, tag, liquibase)
    values ('DEV-614-commands-draft', 'adamjhamer', '2023/general-domain', now(), <ID>, 'EXECUTED', null, 'manual ignored', '', null, '3.5.3');
    -->
    <changeSet id="DEV-614-commands-draft-bigone" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="DEV-614-commands-draft" author="adamjhamer" changeLogFile="2023/general-domain"/>
            </not>
        </preConditions>
        <addColumn tableName="svcrec_commands">
            <column name="draft" type="boolean" defaultValueBoolean="false" valueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <!-- index on find for delete - DraftableCommandHandler -->
    <!-- index on find for restore - eg findAllCommandsByServiceRecipientAndTaskName -->
    <!-- index need not be so precise, to allow read/write/space balance -->
    <!-- the key items (draft / userId) are indexed - the rest can be selectable -->
    <!-- this created a md5sum diff:<output target="WARN">Creating index on svcrec_commands. This may take a while</output> -->
    <changeSet id="DEV-614-commands-svc-draft-idx" author="adamjhamer">
        <createIndex tableName="svcrec_commands" indexName="idx_svcreccmd_draft">
            <column name="draft"/>
            <column name="serviceRecipientId"/>
            <column name="userId"/>
        </createIndex>
    </changeSet>

    <!-- apply admin repairs (aaa) to existing manager/senior-manager -->
    <!-- this is unlike incidents which only gives managers their incidents, but senior do see all -->
    <changeSet id="DEV-2658-repairs-permission-managers" author="adamjhamer">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager -->
            <column name="authority" value="ROLE_ADMINREPAIR"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_ADMINREPAIR"/>
        </insert>
    </changeSet>
    <!-- add the ability to view the menu/controller -->
    <changeSet id="DEV-2658-repairs-permission-view" author="adamjhamer">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager -->
            <column name="authority" value="ROLE_REPAIRS"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_REPAIRS"/>
        </insert>
    </changeSet>

    <!-- agencyCategory -->
    <!--
     - NB id constraint of <constraints nullable="false" primaryKey="true"/>
     - causes mssql to create explicit constraint:
        - CONSTRAINT [PK_<TABLE>] PRIMARY KEY CLUSTERED ([id] ASC)
        - '[id] [bigint] NOT NULL CONSTRAINT [DF_ethnicorigins_id]  DEFAULT ((0))'
     - We need to drop these constraints before modifying the data type.
     - We can use dropDefaultValue to drop the default constraint.
     - We need to use dropPrimaryKey to drop the primary key constraint.
    -->
    <!-- ? remove known duplicate in base data - search "prep-dupl"-->
    <changeSet id="DEV-2700-listDef-agencycat-prep-fk" author="adamjhamer">
        <dropForeignKeyConstraint baseTableName="contacts" constraintName="FKDE2D6053D03E8844"/>
    </changeSet>
    <changeSet id="DEV-2700-listDef-agencycat-prep-mssql-remove" author="adamjhamer" dbms="!mysql">
        <dropDefaultValue tableName="agencyCategories" columnName="id"/>
        <dropPrimaryKey tableName="agencyCategories"/>
    </changeSet>
    <changeSet id="DEV-2700-listDef-agencycat-prep" author="adamjhamer">
        <modifyDataType tableName="agencyCategories" columnName="id" newDataType="INT"/>
        <modifyDataType tableName="contacts" columnName="agencyCategoryId" newDataType="INT"/>
    </changeSet>
    <changeSet id="DEV-2700-listDef-agencycat-prep-mssql-reinstate" author="adamjhamer" dbms="!mysql">
        <addNotNullConstraint tableName="agencyCategories" columnName="id" columnDataType="INT"/>
        <addPrimaryKey tableName="agencyCategories" columnNames="id"/>
    </changeSet>
    <changeSet id="DEV-2700-listDef-agencycat-migrationId" author="adamjhamer">
        <addColumn tableName="agencyCategories">
            <column name="migrationId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <!-- STEP 2: fill that sequence -->
    <changeSet id="DEV-2700-listDef-agencycat-enable" author="adamjhamer">
        <customChange class="com.ecco.infrastructure.liquibase.PopulateHibernateSequenceChange"
                      tableName="agencyCategories" uniqueKeyColumns="id"
                      newIdColumn="migrationId"
                      sequenceName="id_name"
        />
    </changeSet>
    <!-- select * from table group by name having count(*) > 1 -->
    <changeSet id="DEV-2700-listDef-agencycat-copy-rows" author="adamjhamer">
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, businessKey)
            SELECT migrationId, 0, 'agencyCategory', name, concat('agencyCategory-', migrationId) FROM agencyCategories;
        </sql>
    </changeSet>
    <changeSet id="DEV-2700-listDef-agencycat-update-refs" author="adamjhamer">
        <update tableName="contacts">
            <column name="agencyCategoryId" valueComputed="(SELECT eo.migrationId FROM agencyCategories eo WHERE eo.id = contacts.agencyCategoryId)"/>
        </update>
        <addForeignKeyConstraint baseTableName="contacts" baseColumnNames="agencyCategoryId" constraintName="FK_cont_agcatId" referencedTableName="cfg_list_definitions"
                                 referencedColumnNames="id"/>
    </changeSet>

    <changeSet id="DEV-2696-address-disable" author="adamjhamer">
        <addColumn tableName="bldg_addresses">
            <column name="disabled" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- see DEV-9xx-bldg_fixed-srId-nonnull -->
    <changeSet id="DEV-2696-bldg_commands-relax" author="adamjhamer">
        <dropNotNullConstraint tableName="bldg_commands" columnName="serviceRecipientId" columnDataType="INT"/>
    </changeSet>
    <changeSet id="DEV-2696-bldg_commands-adr" author="adamjhamer">
        <addColumn tableName="bldg_commands">
            <column name="addressLocationId" type="INT"/>
        </addColumn>

        <!-- TODO fk's on bldg_commands? -->
    </changeSet>

    <changeSet id="DEV-2711-services-disabled" author="adamjhamer">
        <addColumn tableName="services">
            <column name="disabled" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2683-feature-adr-enabled" author="adamjhamer">
        <update tableName="cfg_feature">
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
            <where>name='referral.list.clientAddress'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2683-feature-usersList-enabled" author="adamjhamer">
        <update tableName="cfg_feature">
            <column name="defaultVote" value="ENABLED_BY_DEFAULT"/>
            <where>name='decideFinal.useAccessToFile'</where>
        </update>
    </changeSet>

    <changeSet id="DEV-2717-notifications" author="adamjhamer">
        <createTable tableName="usr_notifications">
            <!--<column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>-->
            <column name="commandUuid" type="CHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="userId" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="readAt" type="DATETIME">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="DEV-2717-notifications-idx" author="adamjhamer">
        <createIndex tableName="usr_notifications" indexName="idx_usrnotify_id">
            <column name="commandUuid"/>
            <column name="userId"/>
        </createIndex>
        <createIndex tableName="usr_notifications" indexName="idx_usrnotify_user">
            <column name="userId"/>
        </createIndex>
    </changeSet>

    <changeSet id="DEV-2717-notifications-audits" author="adamjhamer">
        <dropNotNullConstraint tableName="usr_commands" columnName="userIdSubject" columnDataType="BIGINT"/>
    </changeSet>
    <changeSet id="DEV-2717-notifications-usr-cmds" author="adamjhamer">
        <!-- don't FK - we can't reference several audit tables -->
        <addColumn tableName="usr_commands">
            <column name="notificationUuid" type="CHAR(36)"/>
        </addColumn>
    </changeSet>

    <!-- apply admin managed-voids (aaa) to existing manager/senior-manager (hence general changelog) -->
    <!-- this is like DEV-2658-repairs-permission-managers -->
    <!-- this is unlike incidents which only gives managers their incidents, but senior do see all -->
    <changeSet id="DEV-2686-managedvoids-permission-managers" author="adamjhamer">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager -->
            <column name="authority" value="ROLE_ADMINMANAGEDVOID"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_ADMINMANAGEDVOID"/>
        </insert>
    </changeSet>
    <!-- add the ability to view the menu/controller -->
    <changeSet id="DEV-2686-managedvoids-permission-view" author="adamjhamer">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager -->
            <column name="authority" value="ROLE_MANAGEDVOIDS"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_MANAGEDVOIDS"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2721-hact-activities" author="adamjhamer" context="acceptanceTests,hact">
        <sqlFile path="classpath:sql/2023/general-domain/hact-surveys-activities.sql"/>
    </changeSet>

</databaseChangeLog>
