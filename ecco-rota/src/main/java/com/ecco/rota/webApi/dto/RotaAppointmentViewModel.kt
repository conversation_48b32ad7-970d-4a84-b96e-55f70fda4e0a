package com.ecco.rota.webApi.dto

import com.ecco.buildings.dom.FixedContainer
import com.ecco.calendar.core.AvailableInterval
import com.ecco.calendar.core.Entry
import com.ecco.calendar.core.Recurrence
import com.ecco.calendar.core.Recurrence.Status
import com.ecco.calendar.core.webapi.CalendarableEventDto
import com.ecco.calendar.core.webapi.EventAttendee
import com.ecco.calendar.core.webapi.EventAttendeeToViewModel
import com.ecco.dom.agreements.DemandSchedule
import com.ecco.infrastructure.time.JodaToJDKAdapters
import com.ecco.infrastructure.time.toJDK
import org.springframework.hateoas.RepresentationModel
import java.net.URI
import java.time.LocalDateTime
import java.util.stream.Collectors

/**
 * Dto for an appointment on the rota (doesn't have to be a rota event).
 *
 * NB This does not include the attendees, because the rota currently does not need them. If the rota was quick at loading
 * all events in a date range then attendees would be useful - there is a method CosmoCalendarService#findEntries used by
 * sync-entries, but at some point this approach wouldn't scale well. Currently the rota loads per resource then per
 * service recipient then dropped.
 */
class RotaAppointmentViewModel : CalendarableEventDto, RepresentationModel<RotaAppointmentViewModel> {

    var requiredAttributes: List<Int>? = null

    /** See RotaController. This comes from RotaAppointmentViewModel.ref which can be itemUuid, calendarId, recurring handle. */
    lateinit var ref: String
    override var title: String? = null
    var eventTypeName: String? = null
    var serviceRecipientName: String? = null
    override var serviceRecipientId: Int? = null
    var serviceRecipientDiscriminator: String? = null
    override var location: String? = null
    override var start: LocalDateTime? = LocalDateTime.now()
    override var end: LocalDateTime? = LocalDateTime.now()
    var nonRotaEvent: Boolean = false
    override var allDay: Boolean = false
    override var status: Status? = null
    var agreementId: Long? = null
    var scheduleId: Long? = null
    override var managedByUri: URI? = null
    override var updatedByUri: URI? = null

    /**
     * OPTIONAL - see class level javadoc. This allows us to get the attendees when requested.
     * As per EventResource.java:
     *  Who is associated with this event.
     *  This will be the user who created the appointment, and any others.
     *  See as-was ItemIntegrationAdvice which calls postEntry, which uses the default rootCalendarId as the 'collection' to attach to.
     */
    var attendees: List<EventAttendee>? = null
    private val eventAttendeeToViewModel = EventAttendeeToViewModel()

    constructor() // Used by deserialization in API tests

    /** Rota demand */
    constructor(demandSchedule: DemandSchedule?, recurrence: Recurrence?, withAttendees: Boolean? = false) {
        if (demandSchedule == null || recurrence == null) {
            return
        }
        this.add(recurrence.getLinks())

        this.start = JodaToJDKAdapters.localDateTimeToJDk(recurrence.start.toLocalDateTime())
        this.end = JodaToJDKAdapters.localDateTimeToJDk(recurrence.end.toLocalDateTime())
        val agreement = demandSchedule.agreement
        val serviceRecipient = agreement.serviceRecipient

        this.serviceRecipientName = serviceRecipient.displayName
        this.serviceRecipientId = agreement.serviceRecipientId
        this.serviceRecipientDiscriminator = serviceRecipient.discriminator
        // NB we always override the ical location of rota events (albeit from the cache)
        this.location = serviceRecipient.address?.toCommaSepString()
        this.agreementId = agreement.id
        this.scheduleId = demandSchedule.id
        this.ref = recurrence.recurrenceHandle.toString() // unique id to the schedule
        if (recurrence.managedBy == null || recurrence.managedBy.toString().contains("AppointmentSchedule/null")) {
            this.managedByUri = demandSchedule.managedByReconstructed
        } else {
            this.managedByUri = recurrence.managedBy
        }
        this.updatedByUri = recurrence.updatedBy
        this.title = recurrence.title
        this.eventTypeName = demandSchedule.title
        this.status = recurrence.status
        // TODO: Can we make requiredAttributes only populated for autoplanner .. or even attach the demand schedule for perhaps lazy load?
        this.requiredAttributes = demandSchedule.requiredAttributes.map { it.id } // This should do for now (ListDefEntryDto isn't visible in this module)

        if (withAttendees == true) {
            this.attendees = recurrence.attendees.stream().map(eventAttendeeToViewModel).collect(Collectors.toList())
        }
    }

    /** Rota availability */
    constructor(careRun: FixedContainer?, availability: AvailableInterval?, serviceRecipientDiscriminator: String?) {
        if (careRun == null || availability == null) {
            return
        }
        this.start = availability.interval.start.toLocalDateTime().toJDK()
        this.end = availability.interval.end.toLocalDateTime().toJDK()
        this.serviceRecipientName = careRun.displayName
        this.serviceRecipientId = careRun.serviceRecipientId
        this.serviceRecipientDiscriminator = serviceRecipientDiscriminator
        this.agreementId = null
        this.scheduleId = null
        this.ref = careRun.calendarId
        this.title = careRun.displayName
        this.eventTypeName = careRun.displayName
        this.status = null
        this.requiredAttributes = null
    }

    /** Used for non-rota events */
    constructor(entry: Entry) {
        this.start = JodaToJDKAdapters.localDateTimeToJDk(entry.start.toLocalDateTime())
        this.end = JodaToJDKAdapters.localDateTimeToJDk(entry.end?.toLocalDateTime())
        this.nonRotaEvent = true
        this.allDay = entry.isAllDay
        this.title = entry.title
        this.eventTypeName = null
        this.ref = entry.itemUid
    }
}
