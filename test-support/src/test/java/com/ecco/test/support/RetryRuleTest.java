package com.ecco.test.support;

import static org.junit.Assert.*;

import org.junit.Rule;
import org.junit.Test;

import com.ecco.test.support.RetryRule.Retries;

@Retries(3)
public class RetryRuleTest {

    private int tryCount = 0;

    @Rule
    public RetryRule retryRule = new RetryRule(0);

    @Test
    @Retries(4)
    public void testMethodAnnotation() {
        if (++tryCount < 4) {
            fail("Failure number #" + tryCount);
        }
    }

    @Test
    public void testClassAnnotation() {
        if (++tryCount < 3) {
            fail("Failure number #" + tryCount);
        }
    }
}
