
#jdbc.dialect=org.hibernate.dialect.H2Dialect

#jdbc.url=jdbc:h2:mem:ecco
#jdbc.username=sa
#jdbc.password=
#jdbc.showSql=true
#jdbc.showSqlComments=true


#cosmo.jdbc.dialect=org.hibernate.dialect.H2Dialect


# NB avoid enabling P6SpyDriver since it won't display the calendar items!


# mysql 5
jdbc.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
#jdbc.dialect=org.hibernate.dialect.MySQLInnoDBDialect

# we got UTC by copying the files from the link to the mysql database folder, http://dev.mysql.com/downloads/timezones.html
jdbc.url=*******************************************************************************************************
jdbc.username=ecco
jdbc.password=ecco
jdbc.showSql=false
jdbc.showSqlComments=true


