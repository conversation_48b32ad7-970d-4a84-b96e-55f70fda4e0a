<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-configuration PUBLIC "-//Hibernate/Hibernate Configuration DTD//EN" "http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">

<!-- ***************** -->
<!-- this configuration is used when we don't need to use spring in the tests -->
<!-- it is copied in various other projects (eg ecco/src/test/resources-env/hsqldb and mysql) to be able to specify different databases -->
<!-- and to use the same classes with different db configuration - this filename needs to alter -->
<!-- ***************** -->
<hibernate-configuration>

    <session-factory>

        <property name="hibernate.connection.driver_class">org.h2.Driver</property>
        <!-- url includes the schema, #database.schemaNames=PUBLIC -->
        <property name="hibernate.connection.url"><![CDATA[jdbc:h2:mem:PUBLIC]]></property>
        <property name="hibernate.hbm2ddl.auto">create</property>

        <!-- properties which match the spring app - so we are testing like against like -->
        <!-- TODO to avoid repeating we could specify different cfg files from configLocation in spring -->
        <property name="hibernate.max_fetch_depth">3</property>
        <!-- we say false because we log.hibernate.SQL which is the same -->
        <property name="hibernate.show_sql">true</property>
        <property name="hibernate.use_sql_comments">true</property>
        <property name="hibernate.bytecode.use_reflection_optimizer">false</property>

        <!-- add for stats on the cache -->
        <property name="hibernate.generate_statistics">true</property>
        <!-- add to stop the stats spamming the log -->
        <property name="hibernate.session.events.log">false</property>
        <!-- we enable caching since some projects use cache settings -->
        <property name="hibernate.cache.use_query_cache">true</property>
        <property name="hibernate.cache.use_second_level_cache">true</property>
        <property name="hibernate.cache.use_structured_entities">true</property>
        <property name="hibernate.cache.region.factory_class">org.hibernate.cache.jcache.internal.JCacheRegionFactory</property>
        <property name="hibernate.jdbc.batch_size">25</property>

        <!-- properties which are defaulted by spring - so we are testing like against like -->

        <!-- this setting only affects sessions opened through sessionFactory.openSession, according to http://www.hibernate.org/hib_docs/v3/reference/en/html_single/#session-configuration -->
        <!-- spring's HibernateTransactionManager uses on_close hibernate defaults to auto (becomes after_transaction unless jta) -->
        <!-- this setting only affects sessions opened through sessionFactory.openSession, according to http://www.hibernate.org/hib_docs/v3/reference/en/html_single/#session-configuration -->
        <!-- and this is what spring's SessionFactoryUtils code does, which is what the HibernateTransactionManager uses -->
        <!-- TODO is this used by jta system? - see the flow from SessionFactoryUtils api -->
        <property name="hibernate.connection.release_mode">on_close</property>

    </session-factory>

</hibernate-configuration>

<!-- other suggested settings -->
<!--
    <property name="hibernate.dbcp.*"></property>
    <property name="hibernate.connection.pool_size">10</property>
    <property name="hibernate.hbm2ddl.auto">update</property>
-->
