<?xml version="1.0" encoding="ISO-8859-1"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" xmlns="ecco_test" targetNamespace="ecco_test">
    <xsd:element name="jobs" type="jobs__type" />
    <xsd:element name="risks" type="risks__type" />
    <xsd:element name="supportthreatactions" type="supportthreatactions__type" />
    <xsd:element name="supportplanwork_actions" type="supportplanwork_actions__type" />
    <xsd:element name="languages" type="languages__type" />
    <xsd:element name="revision" type="revision__type" />
    <xsd:element name="supportplanoutcomes" type="supportplanoutcomes__type" />
    <xsd:element name="servicetypes_outcomesupports" type="servicetypes_outcomesupports__type" />
    <xsd:element name="activities" type="activities__type" />
    <xsd:element name="groups" type="groups__type" />
    <xsd:element name="signature" type="signature__type" />
    <xsd:element name="threatoutcomes_supportplanactions" type="threatoutcomes_supportplanactions__type" />
    <xsd:element name="individual_individualtypes" type="individual_individualtypes__type" />
    <xsd:element name="services" type="services__type" />
    <xsd:element name="reportattachments" type="reportattachments__type" />
    <xsd:element name="supportplanwork" type="supportplanwork__type" />
    <xsd:element name="venues" type="venues__type" />
    <xsd:element name="questions" type="questions__type" />
    <xsd:element name="users_aud" type="user_aud__type" />
    <xsd:element name="clientdetails" type="clientdetails__type" />
    <xsd:element name="referralcomments" type="referralcomments__type" />
    <xsd:element name="questiongroups" type="questiongroups__type" />
    <xsd:element name="projects" type="projects__type" />
    <xsd:element name="servicetypes_referralaspects" type="servicetypes_referralaspects__type" />
    <xsd:element name="accommodationcategories" type="accommodationcategories__type" />
    <xsd:element name="referrals_contacts" type="referrals_contacts__type" />
    <xsd:element name="supporthrwork_actions" type="supporthrwork_actions__type" />
    <xsd:element name="questionanswerchoices" type="questionanswerchoices__type" />
    <xsd:element name="group_members_aud" type="group_members_aud__type" />
    <xsd:element name="hrsettings" type="hrsettings__type" />
    <xsd:element name="audits" type="audits__type" />
    <xsd:element name="outcomethreats_actionsupports" type="outcomethreats_actionsupports__type" />
    <xsd:element name="servicetypes_outcomethreats" type="servicetypes_outcomethreats__type" />
    <xsd:element name="questionanswerfrees" type="questionanswerfrees__type" />
    <xsd:element name="events" type="events__type" />
    <xsd:element name="threatoutcomes_questions" type="threatoutcomes_questions__type" />
    <xsd:element name="supporthrwork" type="supporthrwork__type" />
    <xsd:element name="servicetypes_outcomeservices" type="servicetypes_outcomeservices__type" />
    <xsd:element name="pendingstatuses" type="pendingstatuses__type" />
    <xsd:element name="supporthrcomments" type="supporthrcomments__type" />
    <xsd:element name="localauthorities" type="localauthorities__type" />
    <xsd:element name="hrfromtos" type="hrfromtos__type" />
    <xsd:element name="flags" type="flags__type" />
    <xsd:element name="workers_jobs" type="workers_jobs__type" />
    <xsd:element name="commenttypes" type="commenttypes__type" />
    <xsd:element name="questions_questionanswerfrees" type="questions_questionanswerfrees__type" />
    <xsd:element name="supportthreatflags" type="supportthreatflags__type" />
    <xsd:element name="outcomes_st_referralaspects" type="outcomes_servicetypes_referralaspects__type" />
    <xsd:element name="group_members" type="group_members__type" />
    <xsd:element name="supportplanactions" type="supportplanactions__type" />
    <xsd:element name="servicetypes_questiongroups" type="servicetypes_questiongroups__type" />
    <xsd:element name="referralactivityworker" type="referralactivityworker__type" />
    <xsd:element name="supportthreatwork" type="supportthreatwork__type" />
    <xsd:element name="ldapgroupmapping" type="ldapgroupmapping__type" />
    <xsd:element name="activities_workers" type="activities_workers__type" />
    <xsd:element name="actions" type="actions__type" />
    <xsd:element name="group_authorities" type="group_authorities__type" />
    <xsd:element name="st_referralaspectsettings" type="servicetypes_referralaspectsettings__type" />
    <xsd:element name="supporthroutcomes" type="supporthroutcomes__type" />
    <xsd:element name="workers" type="workers__type" />
    <xsd:element name="contacts" type="contacts__type" />
    <xsd:element name="contacts_events" type="contacts_events__type" />
    <xsd:element name="actionfakes" type="actionfakes__type" />
    <xsd:element name="hr_outcomes" type="hr_outcomes__type" />
    <xsd:element name="supportplanrisks" type="supportplanrisks__type" />
    <xsd:element name="uploadfile" type="uploadfile__type" />
    <xsd:element name="supportthreatoutcomes" type="supportthreatoutcomes__type" />
    <xsd:element name="work" type="work__type" />
    <xsd:element name="workerattachments" type="workerattachments__type" />
    <xsd:element name="dbmaintain_scripts" type="dbmaintain_scripts__type" />
    <xsd:element name="reports" type="reports__type" />
    <xsd:element name="referralprojects" type="referralprojects__type" />
    <xsd:element name="reviews" type="reviews__type" />
    <xsd:element name="referralaspects" type="referralaspects__type" />
    <xsd:element name="supportthreatcomments" type="supportthreatcomments__type" />
    <xsd:element name="offences" type="offences__type" />
    <xsd:element name="supportplancomments" type="supportplancomments__type" />
    <xsd:element name="outcomethreats_questions" type="outcomethreats_questions__type" />
    <xsd:element name="referrals" type="referrals__type" />
    <xsd:element name="supportplananswers" type="supportplananswers__type" />
    <xsd:element name="servicetypes" type="servicetypes__type" />
    <xsd:element name="leavereasons" type="leavereasons__type" />
    <xsd:element name="activities_clients" type="activities_clients__type" />
    <xsd:element name="signpostreasons" type="signpostreasons__type" />
    <xsd:element name="economicstatuses" type="economicstatuses__type" />
    <xsd:element name="supportthreatwork_actions" type="supportthreatwork_actions__type" />
    <xsd:element name="hr" type="hr__type" />
    <xsd:element name="uploadbytes" type="uploadbytes__type" />
    <xsd:element name="children" type="children__type" />
    <xsd:element name="departments" type="departments__type" />
    <xsd:element name="qg_st_referralaspects" type="questiongroups_servicetypes_referralaspects__type" />
    <xsd:element name="supporthractions" type="supporthractions__type" />
    <xsd:element name="regions" type="regions__type" />
    <xsd:element name="setting" type="setting__type" />
    <xsd:element name="referralactivities" type="referralactivities__type" />
    <xsd:element name="activitytypes" type="activitytypes__type" />
    <xsd:element name="religions" type="religions__type" />
    <xsd:element name="partners" type="partners__type" />
    <xsd:element name="referralactivitytypes" type="referralactivitytypes__type" />
    <xsd:element name="agencycategories" type="agencycategories__type" />
    <xsd:element name="servicetypes_flagthreats" type="servicetypes_flagthreats__type" />
    <xsd:element name="projectcomments" type="projectcomments__type" />
    <xsd:element name="svcrec_attachments" type="svcrec_attachments__type" />
    <xsd:element name="accommodations" type="accommodations__type" />
    <xsd:element name="exitreasons" type="exitreasons__type" />
    <xsd:element name="referrersources" type="referrersources__type" />
    <xsd:element name="services_projects" type="services_projects__type" />
    <xsd:element name="outcomes" type="outcomes__type" />
    <xsd:element name="questiongroups_questions" type="questiongroups_questions__type" />
    <xsd:element name="questions_questionanswrchoices" type="questions_questionanswerchoices__type" />
    <xsd:element name="users" type="user__type" />
    <xsd:complexType name="jobs__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="departmentId" use="optional" />
        <xsd:attribute name="sessional" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="risks__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="outcomeId" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportthreatactions__type">
        <xsd:attribute name="workDate" use="optional" />
        <xsd:attribute name="status" use="optional" />
        <xsd:attribute name="expiryDate" use="optional" />
        <xsd:attribute name="statusChange" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="target" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="quantity" use="optional" />
        <xsd:attribute name="actionId" use="optional" />
        <xsd:attribute name="ga_comment" use="optional" />
        <xsd:attribute name="activity" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportplanwork_actions__type">
        <xsd:attribute name="supportplanworkId" use="optional" />
        <xsd:attribute name="actionId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="languages__type">
        <xsd:attribute name="iso6391" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="iso6392" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="revision__type">
        <xsd:attribute name="timestamp" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="username" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportplanoutcomes__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="status" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="outcomeId" use="optional" />
        <xsd:attribute name="statusChangeDate" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="servicetypes_outcomesupports__type">
        <xsd:attribute name="outcomeId" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="activities__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="activityTypeId" use="optional" />
        <xsd:attribute name="fromDate" use="optional" />
        <xsd:attribute name="hours" use="optional" />
        <xsd:attribute name="value" use="optional" />
        <xsd:attribute name="capacity" use="optional" />
        <xsd:attribute name="toDate" use="optional" />
        <xsd:attribute name="venueId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="groups__type">
        <xsd:attribute name="group_name" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="signature__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="svgXml" use="optional" />
        <xsd:attribute name="individualId" use="optional" />
        <xsd:attribute name="signeddate" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="threatoutcomes_supportplanactions__type">
        <xsd:attribute name="supportplanactionId" use="optional" />
        <xsd:attribute name="threatoutcomeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="individual_individualtypes__type">
        <xsd:attribute name="individualType" use="optional" />
        <xsd:attribute name="individualId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="services__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="reportattachments__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="bytesId" use="optional" />
        <xsd:attribute name="filename" use="optional" />
        <xsd:attribute name="reportId" use="optional" />
        <xsd:attribute name="upload_size" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportplanwork__type">
        <xsd:attribute name="workDate" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="asRisk" use="optional" />
        <xsd:attribute name="reviewId" use="optional" />
        <xsd:attribute name="sourcePageGroup" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="signatureId" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="outcomePage" use="optional" />
        <xsd:attribute name="sourcePage" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="venues__type">
        <xsd:attribute name="addressline2" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="addressline3" use="optional" />
        <xsd:attribute name="addresscounty" use="optional" />
        <xsd:attribute name="addresscountry" use="optional" />
        <xsd:attribute name="addressline1" use="optional" />
        <xsd:attribute name="addresstown" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="addresspostcode" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="questions__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="answerType" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="user_aud__type">
        <xsd:attribute name="REV" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="enabled" use="optional" />
        <xsd:attribute name="timezone" use="optional" />
        <xsd:attribute name="calendarId" use="optional" />
        <xsd:attribute name="registered" use="optional" />
        <xsd:attribute name="locale" use="optional" />
        <xsd:attribute name="domain" use="optional" />
        <xsd:attribute name="activeDirectoryUser" use="optional" />
        <xsd:attribute name="password" use="optional" />
        <xsd:attribute name="REVTYPE" use="optional" />
        <xsd:attribute name="country" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="clientdetails__type">
        <xsd:attribute name="birthmonth" use="optional" />
        <xsd:attribute name="birthday" use="optional" />
        <xsd:attribute name="avatarId" use="optional" />
        <xsd:attribute name="emergencyDetails" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="minute" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="ethnicoriginsId" use="optional" />
        <xsd:attribute name="paris" use="optional" />
        <xsd:attribute name="keyCode" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="disability" use="optional" />
        <xsd:attribute name="gender" use="optional" />
        <xsd:attribute name="sexuality" use="optional" />
        <xsd:attribute name="militaryNumber" use="optional" />
        <xsd:attribute name="religionsId" use="optional" />
        <xsd:attribute name="dentistDetails" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="hour" use="optional" />
        <xsd:attribute name="nhs" use="optional" />
        <xsd:attribute name="birthyear" use="optional" />
        <xsd:attribute name="languagesId" use="optional" />
        <xsd:attribute name="doctorDetails" use="optional" />
        <xsd:attribute name="mothersFirstName" use="optional" />
        <xsd:attribute name="medicationDetails" use="optional" />
        <xsd:attribute name="contactsId" use="optional" />
        <xsd:attribute name="ni" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="referralcomments__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="discriminator_orm" use="optional" />
        <xsd:attribute name="minutesSpent" use="optional" />
        <xsd:attribute name="bc_comment" use="optional" />
        <xsd:attribute name="typeId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="questiongroups__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="discriminator_orm" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="headertext" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="projects__type">
        <xsd:attribute name="addressline2" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="addressline3" use="optional" />
        <xsd:attribute name="addresscounty" use="optional" />
        <xsd:attribute name="addresscountry" use="optional" />
        <xsd:attribute name="addressline1" use="optional" />
        <xsd:attribute name="addresstown" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="addresspostcode" use="optional" />
        <xsd:attribute name="regionId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="servicetypes_referralaspects__type">
        <xsd:attribute name="orderby" use="optional" />
        <xsd:attribute name="referralaspectId" use="optional" />
        <xsd:attribute name="allowNext" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="accommodationcategories__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="referrals_contacts__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supporthrwork_actions__type">
        <xsd:attribute name="supporthrworkId" use="optional" />
        <xsd:attribute name="actionId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="questionanswerchoices__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="displayImage" use="optional" />
        <xsd:attribute name="value" use="optional" />
        <xsd:attribute name="hiddenDefault" use="optional" />
        <xsd:attribute name="displayvalue" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="group_members_aud__type">
        <xsd:attribute name="REV" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="username" use="optional" />
        <xsd:attribute name="group_id" use="optional" />
        <xsd:attribute name="REVTYPE" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="hrsettings__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="hrId" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="value" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="audits__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="usersId" use="optional" />
        <xsd:attribute name="entityId" use="optional" />
        <xsd:attribute name="action" use="optional" />
        <xsd:attribute name="class" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="outcomethreats_actionsupports__type">
        <xsd:attribute name="supportplanactionId" use="optional" />
        <xsd:attribute name="threatoutcomeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="servicetypes_outcomethreats__type">
        <xsd:attribute name="threatoutcomeId" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="questionanswerfrees__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="minimum" use="optional" />
        <xsd:attribute name="maximum" use="optional" />
        <xsd:attribute name="valueType" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="events__type">
        <xsd:attribute name="cal_uid" use="optional" />
        <xsd:attribute name="eventyear" use="optional" />
        <xsd:attribute name="eventendday" use="optional" />
        <xsd:attribute name="eventendyear" use="optional" />
        <xsd:attribute name="eventType" use="optional" />
        <xsd:attribute name="eventendminute" use="optional" />
        <xsd:attribute name="workerId" use="optional" />
        <xsd:attribute name="type" use="optional" />
        <xsd:attribute name="eventday" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="eventminute" use="optional" />
        <xsd:attribute name="serviceId" use="optional" />
        <xsd:attribute name="repeatYears" use="optional" />
        <xsd:attribute name="generated" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="eventmonth" use="optional" />
        <xsd:attribute name="eventendmonth" use="optional" />
        <xsd:attribute name="eventendhour" use="optional" />
        <xsd:attribute name="actionId" use="optional" />
        <xsd:attribute name="eventhour" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="threatoutcomes_questions__type">
        <xsd:attribute name="questionId" use="optional" />
        <xsd:attribute name="threatoutcomeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supporthrwork__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="workDate" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="workerId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="servicetypes_outcomeservices__type">
        <xsd:attribute name="outcomeId" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="pendingstatuses__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supporthrcomments__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="minutesSpent" use="optional" />
        <xsd:attribute name="workerId" use="optional" />
        <xsd:attribute name="bc_comment" use="optional" />
        <xsd:attribute name="typeId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="localauthorities__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="hrfromtos__type">
        <xsd:attribute name="leavereasonId" use="optional" />
        <xsd:attribute name="entitlementChangeSessional" use="optional" />
        <xsd:attribute name="jobId" use="optional" />
        <xsd:attribute name="toDate" use="optional" />
        <xsd:attribute name="workerId" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="workerJob_jobId" use="optional" />
        <xsd:attribute name="fromDate" use="optional" />
        <xsd:attribute name="workerJob_workerId" use="optional" />
        <xsd:attribute name="discriminator_orm" use="optional" />
        <xsd:attribute name="value" use="optional" />
        <xsd:attribute name="entitlementChange" use="optional" />
        <xsd:attribute name="comments" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="flags__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="workers_jobs__type">
        <xsd:attribute name="rate" use="optional" />
        <xsd:attribute name="jobId" use="optional" />
        <xsd:attribute name="fromDate" use="optional" />
        <xsd:attribute name="volunteer" use="optional" />
        <xsd:attribute name="toDate" use="optional" />
        <xsd:attribute name="workerId" use="optional" />
        <xsd:attribute name="entitlement" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="commenttypes__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="questions_questionanswerfrees__type">
        <xsd:attribute name="questionanswerfreeId" use="optional" />
        <xsd:attribute name="questionId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportthreatflags__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="flagId" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="value" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="ethnicorigins__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="outcomes_servicetypes_referralaspects__type">
        <xsd:attribute name="outcomeId" use="optional" />
        <xsd:attribute name="referralaspectId" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="group_members__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="username" use="optional" />
        <xsd:attribute name="group_id" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportplanactions__type">
        <xsd:attribute name="workDate" use="optional" />
        <xsd:attribute name="status" use="optional" />
        <xsd:attribute name="expiryDate" use="optional" />
        <xsd:attribute name="statusChange" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="target" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="quantity" use="optional" />
        <xsd:attribute name="actionId" use="optional" />
        <xsd:attribute name="ga_comment" use="optional" />
        <xsd:attribute name="activity" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="servicetypes_questiongroups__type">
        <xsd:attribute name="questiongroupId" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="referralactivityworker__type">
        <xsd:attribute name="referralActivityId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="calendarEntryId" use="optional" />
        <xsd:attribute name="supportWorkerId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportthreatwork__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="workDate" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="sourcePageGroup" use="optional" />
        <xsd:attribute name="sourcePage" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="ldapgroupmapping__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="ldapGroup" use="optional" />
        <xsd:attribute name="localGroup" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="activities_workers__type">
        <xsd:attribute name="activityId" use="optional" />
        <xsd:attribute name="buddy" use="optional" />
        <xsd:attribute name="attended" use="optional" />
        <xsd:attribute name="leader" use="optional" />
        <xsd:attribute name="workerId" use="optional" />
        <xsd:attribute name="attending" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="actions__type">
        <xsd:attribute name="riskName" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="riskId" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="group_authorities__type">
        <xsd:attribute name="authority" use="optional" />
        <xsd:attribute name="group_id" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="servicetypes_referralaspectsettings__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="value" use="optional" />
        <xsd:attribute name="referralaspectId" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supporthroutcomes__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="status" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="outcomeId" use="optional" />
        <xsd:attribute name="statusChangeDate" use="optional" />
        <xsd:attribute name="workerId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="workers__type">
        <xsd:attribute name="birthmonth" use="optional" />
        <xsd:attribute name="birthday" use="optional" />
        <xsd:attribute name="startDate" use="optional" />
        <xsd:attribute name="avatarId" use="optional" />
        <xsd:attribute name="emergencyDetails" use="optional" />
        <xsd:attribute name="endDate" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="ethnicoriginsId" use="optional" />
        <xsd:attribute name="minute" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="paris" use="optional" />
        <xsd:attribute name="keyCode" use="optional" />
        <xsd:attribute name="crbNumber" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="disability" use="optional" />
        <xsd:attribute name="gender" use="optional" />
        <xsd:attribute name="sexuality" use="optional" />
        <xsd:attribute name="militaryNumber" use="optional" />
        <xsd:attribute name="workCapacity" use="optional" />
        <xsd:attribute name="religionsId" use="optional" />
        <xsd:attribute name="disclosureNumber" use="optional" />
        <xsd:attribute name="dentistDetails" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="hour" use="optional" />
        <xsd:attribute name="nhs" use="optional" />
        <xsd:attribute name="birthyear" use="optional" />
        <xsd:attribute name="languagesId" use="optional" />
        <xsd:attribute name="doctorDetails" use="optional" />
        <xsd:attribute name="mothersFirstName" use="optional" />
        <xsd:attribute name="medicationDetails" use="optional" />
        <xsd:attribute name="contactsId" use="optional" />
        <xsd:attribute name="ni" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="contacts__type">
        <xsd:attribute name="birthday" use="optional" />
        <xsd:attribute name="birthmonth" use="optional" />
        <xsd:attribute name="usersId" use="optional" />
        <xsd:attribute name="deliveryPartner" use="optional" />
        <xsd:attribute name="lastname" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="minute" use="optional" />
        <xsd:attribute name="addresscounty" use="optional" />
        <xsd:attribute name="economicStatusId" use="optional" />
        <xsd:attribute name="mobilenumber" use="optional" />
        <xsd:attribute name="addresstown" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="keyHolder" use="optional" />
        <xsd:attribute name="internal" use="optional" />
        <xsd:attribute name="phonenumber" use="optional" />
        <xsd:attribute name="addresscountry" use="optional" />
        <xsd:attribute name="outOfArea" use="optional" />
        <xsd:attribute name="agencycategoryId" use="optional" />
        <xsd:attribute name="hour" use="optional" />
        <xsd:attribute name="firstname" use="optional" />
        <xsd:attribute name="companyname" use="optional" />
        <xsd:attribute name="addressline2" use="optional" />
        <xsd:attribute name="birthyear" use="optional" />
        <xsd:attribute name="partnersId" use="optional" />
        <xsd:attribute name="addressline3" use="optional" />
        <xsd:attribute name="addressline1" use="optional" />
        <xsd:attribute name="email" use="optional" />
        <xsd:attribute name="discriminator_orm" use="optional" />
        <xsd:attribute name="companyId" use="optional" />
        <xsd:attribute name="livingWithClient" use="optional" />
        <xsd:attribute name="preferredContactMethod" use="optional" />
        <xsd:attribute name="addresspostcode" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="contacts_events__type">
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="eventId" use="optional" />
        <xsd:attribute name="eventStatus" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="actionfakes__type">
        <xsd:attribute name="riskName" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="riskId" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="realActionId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="hr_outcomes__type">
        <xsd:attribute name="hrId" use="optional" />
        <xsd:attribute name="outcomeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportplanrisks__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="riskId" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="relevant" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="uploadfile__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="bytesId" use="optional" />
        <xsd:attribute name="filename" use="optional" />
        <xsd:attribute name="mediaType" use="optional" />
        <xsd:attribute name="upload_size" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportthreatoutcomes__type">
        <xsd:attribute name="customObjectData" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="status" use="optional" />
        <xsd:attribute name="textMap" use="optional" />
        <xsd:attribute name="statusChangeDate" use="optional" />
        <xsd:attribute name="outcomeId" use="optional" />
        <xsd:attribute name="levelMeasure" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="sto_level" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="workId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="work__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="jobId" use="optional" />
        <xsd:attribute name="hours" use="optional" />
        <xsd:attribute name="workerId" use="optional" />
        <xsd:attribute name="work_date" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="workerattachments__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="bytesId" use="optional" />
        <xsd:attribute name="filename" use="optional" />
        <xsd:attribute name="workerId" use="optional" />
        <xsd:attribute name="upload_size" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="dbmaintain_scripts__type">
        <xsd:attribute name="succeeded" use="optional" />
        <xsd:attribute name="file_last_modified_at" use="optional" />
        <xsd:attribute name="executed_at" use="optional" />
        <xsd:attribute name="checksum" use="optional" />
        <xsd:attribute name="file_name" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="reports__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="referralprojects__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="accommodationId" use="optional" />
        <xsd:attribute name="projectId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="reviews__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="startDate" use="optional" />
        <xsd:attribute name="reviewPage" use="optional" />
        <xsd:attribute name="complete" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="referralaspects__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="external" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="displayOverview" use="optional" />
        <xsd:attribute name="friendlyName" use="optional" />
        <xsd:attribute name="display" use="optional" />
        <xsd:attribute name="internal" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportthreatcomments__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="levelOverall" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="minutesSpent" use="optional" />
        <xsd:attribute name="bc_comment" use="optional" />
        <xsd:attribute name="typeId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="offences__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportplancomments__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="threatWorkId" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="requiresThreatManagement" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="minutesSpent" use="optional" />
        <xsd:attribute name="bc_comment" use="optional" />
        <xsd:attribute name="typeId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="outcomethreats_questions__type">
        <xsd:attribute name="questionId" use="optional" />
        <xsd:attribute name="threatoutcomeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="referrals__type">
        <xsd:attribute name="sourceRegionId" use="optional" />
        <xsd:attribute name="exitCommentId" use="optional" />
        <xsd:attribute name="external" use="optional" />
        <xsd:attribute name="decisionMadeOn" use="optional" />
        <xsd:attribute name="contractStartDate" use="optional" />
        <xsd:attribute name="firstOfferedInterviewDate" use="optional" />
        <xsd:attribute name="interviewersOther" use="optional" />
        <xsd:attribute name="agencyId" use="optional" />
        <xsd:attribute name="requiredAcceptanceAsap" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="catTraveller" use="optional" />
        <xsd:attribute name="signpostedBack" use="optional" />
        <xsd:attribute name="receivingService" use="optional" />
        <xsd:attribute name="drugs" use="optional" />
        <xsd:attribute name="projectPreferredId" use="optional" />
        <xsd:attribute name="signpostedCommentId" use="optional" />
        <xsd:attribute name="decisionFundingDate" use="optional" />
        <xsd:attribute name="disabilityNotDisclosed" use="optional" />
        <xsd:attribute name="receivedDate" use="optional" />
        <xsd:attribute name="visual" use="optional" />
        <xsd:attribute name="disabledOther" use="optional" />
        <xsd:attribute name="offender" use="optional" />
        <xsd:attribute name="alcohol" use="optional" />
        <xsd:attribute name="catDisabledFamilies" use="optional" />
        <xsd:attribute name="interviewDna" use="optional" />
        <xsd:attribute name="exited" use="optional" />
        <xsd:attribute name="referrerId" use="optional" />
        <xsd:attribute name="mentalHealth" use="optional" />
        <xsd:attribute name="autistic" use="optional" />
        <xsd:attribute name="mappa" use="optional" />
        <xsd:attribute name="pendingStatusId" use="optional" />
        <xsd:attribute name="chronic" use="optional" />
        <xsd:attribute name="maritalStatus" use="optional" />
        <xsd:attribute name="decision" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="substance" use="optional" />
        <xsd:attribute name="catLoneParent" use="optional" />
        <xsd:attribute name="life" use="optional" />
        <xsd:attribute name="acceptedFunding" use="optional" />
        <xsd:attribute name="domesticViolence" use="optional" />
        <xsd:attribute name="fundingSourceId" use="optional" />
        <xsd:attribute name="fundingBand" use="optional" />
        <xsd:attribute name="mobility" use="optional" />
        <xsd:attribute name="catFather" use="optional" />
        <xsd:attribute name="violence" use="optional" />
        <xsd:attribute name="debt" use="optional" />
        <xsd:attribute name="decisionExternalMadeOn" use="optional" />
        <xsd:attribute name="selfReferral" use="optional" />
        <xsd:attribute name="violenceFromOutside" use="optional" />
        <xsd:attribute name="accommodationCategoryId" use="optional" />
        <xsd:attribute name="catTemporaryAccommodation" use="optional" />
        <xsd:attribute name="comments" use="optional" />
        <xsd:attribute name="customObjectData" use="optional" />
        <xsd:attribute name="physicalHealth" use="optional" />
        <xsd:attribute name="supportWorkerId" use="optional" />
        <xsd:attribute name="location" use="optional" />
        <xsd:attribute name="signpostedServiceId" use="optional" />
        <xsd:attribute name="exitReasonId" use="optional" />
        <xsd:attribute name="eligible" use="optional" />
        <xsd:attribute name="currentReferralAspect" use="optional" />
        <xsd:attribute name="asbo" use="optional" />
        <xsd:attribute name="firstResponseMadeOn" use="optional" />
        <xsd:attribute name="signpostedReasonId" use="optional" />
        <xsd:attribute name="catMinority" use="optional" />
        <xsd:attribute name="requiredAcceptanceDate" use="optional" />
        <xsd:attribute name="sexOffender" use="optional" />
        <xsd:attribute name="catWorkless" use="optional" />
        <xsd:attribute name="sourceTypeId" use="optional" />
        <xsd:attribute name="interviewSetupComments" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="signpostedParentReferralId" use="optional" />
        <xsd:attribute name="clientId" use="optional" />
        <xsd:attribute name="acceptedExternal" use="optional" />
        <xsd:attribute name="hearing" use="optional" />
        <xsd:attribute name="anger" use="optional" />
        <xsd:attribute name="catMigrant" use="optional" />
        <xsd:attribute name="interviewer1Id" use="optional" />
        <xsd:attribute name="children" use="optional" />
        <xsd:attribute name="referralReason" use="optional" />
        <xsd:attribute name="interviewer2Id" use="optional" />
        <xsd:attribute name="referredServiceId" use="optional" />
        <xsd:attribute name="decisionDate" use="optional" />
        <xsd:attribute name="deliveredById" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="learningDifficulty" use="optional" />
        <xsd:attribute name="marac" use="optional" />
        <xsd:attribute name="mhaStatus" use="optional" />
        <xsd:attribute name="fundingHoursOfSupport" use="optional" />
        <xsd:attribute name="decisionReferralMadeOn" use="optional" />
        <xsd:attribute name="acceptedOnService" use="optional" />
        <xsd:attribute name="waitingListScore" use="optional" />
        <xsd:attribute name="emotionalHealth" use="optional" />
        <xsd:attribute name="textMap" use="optional" />
        <xsd:attribute name="deliveredByStartDate" use="optional" />
        <xsd:attribute name="acceptedReferral" use="optional" />
        <xsd:attribute name="signpostedAgencyId" use="optional" />
        <xsd:attribute name="primaryReferralId" use="optional" />
        <xsd:attribute name="gambling" use="optional" />
        <xsd:attribute name="arson" use="optional" />
        <xsd:attribute name="catTeenageParent" use="optional" />
        <xsd:attribute name="offenceId" use="optional" />
        <xsd:attribute name="interviewDnaComments" use="optional" />
        <xsd:attribute name="receivingServiceDate" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportplananswers__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="workDate" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="questionId" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="answer" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="servicetypes__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="multipleReferrals" use="optional" />
        <xsd:attribute name="accommodation" use="optional" />
        <xsd:attribute name="logTime" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="contractRequired" use="optional" />
        <xsd:attribute name="anonymousSupport" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="leavereasons__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="activities_clients__type">
        <xsd:attribute name="activityId" use="optional" />
        <xsd:attribute name="attended" use="optional" />
        <xsd:attribute name="collected" use="optional" />
        <xsd:attribute name="cost" use="optional" />
        <xsd:attribute name="attending" use="optional" />
        <xsd:attribute name="clientId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="signpostreasons__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="organisationDecision" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="clientDecision" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="economicstatuses__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supportthreatwork_actions__type">
        <xsd:attribute name="supportthreatworkId" use="optional" />
        <xsd:attribute name="actionId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="hr__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="uploadbytes__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="bytes" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="children__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="testContactId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="departments__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="questiongroups_servicetypes_referralaspects__type">
        <xsd:attribute name="questiongroupId" use="optional" />
        <xsd:attribute name="referralaspectId" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="supporthractions__type">
        <xsd:attribute name="workDate" use="optional" />
        <xsd:attribute name="status" use="optional" />
        <xsd:attribute name="expiryDate" use="optional" />
        <xsd:attribute name="statusChange" use="optional" />
        <xsd:attribute name="workerId" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="target" use="optional" />
        <xsd:attribute name="workId" use="optional" />
        <xsd:attribute name="quantity" use="optional" />
        <xsd:attribute name="actionId" use="optional" />
        <xsd:attribute name="ga_comment" use="optional" />
        <xsd:attribute name="activity" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="regions__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="setting__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="keyvalue" use="optional" />
        <xsd:attribute name="keyname" use="optional" />
        <xsd:attribute name="readOnStartup" use="optional" />
        <xsd:attribute name="namespace" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="referralactivities__type">
        <xsd:attribute name="activityTypeId" use="optional" />
        <xsd:attribute name="clientCalendarEntryId" use="optional" />
        <xsd:attribute name="PONumber" use="optional" />
        <xsd:attribute name="supportplanworkId" use="optional" />
        <xsd:attribute name="toDate" use="optional" />
        <xsd:attribute name="agencyId" use="optional" />
        <xsd:attribute name="agreementInPlace" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="fromDate" use="optional" />
        <xsd:attribute name="complete" use="optional" />
        <xsd:attribute name="plannedEnd" use="optional" />
        <xsd:attribute name="value" use="optional" />
        <xsd:attribute name="actionId" use="optional" />
        <xsd:attribute name="fundingBand" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="activitytypes__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="serviceId" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="religions__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="partners__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="referralactivitytypes__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="schedulable" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="agencycategories__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="code" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="servicetypes_flagthreats__type">
        <xsd:attribute name="flagId" use="optional" />
        <xsd:attribute name="servicetypeId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="projectcomments__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="contactId" use="optional" />
        <xsd:attribute name="restriction" use="optional" />
        <xsd:attribute name="created" use="optional" />
        <xsd:attribute name="logDate" use="optional" />
        <xsd:attribute name="projectId" use="optional" />
        <xsd:attribute name="minutesSpent" use="optional" />
        <xsd:attribute name="bc_comment" use="optional" />
        <xsd:attribute name="typeId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="svcrec_attachments__type">
        <xsd:attribute name="referralId" use="optional" />
        <xsd:attribute name="showOnSupportPlan" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="bytesId" use="optional" />
        <xsd:attribute name="filename" use="optional" />
        <xsd:attribute name="showOnReferral" use="optional" />
        <xsd:attribute name="upload_size" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="accommodations__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="projectId" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="exitreasons__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="referrersources__type">
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="services_projects__type">
        <xsd:attribute name="serviceId" use="optional" />
        <xsd:attribute name="projectId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="outcomes__type">
        <xsd:attribute name="questiongroupId" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="discriminator_orm" use="optional" />
        <xsd:attribute name="weighting" use="optional" />
        <xsd:attribute name="name" use="optional" />
        <xsd:attribute name="disabled" use="optional" />
        <xsd:attribute name="version" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="questiongroups_questions__type">
        <xsd:attribute name="questiongroupId" use="optional" />
        <xsd:attribute name="questionId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="questions_questionanswerchoices__type">
        <xsd:attribute name="questionId" use="optional" />
        <xsd:attribute name="questionanswerchoiceId" use="optional" />
    </xsd:complexType>
    <xsd:complexType name="user__type">
        <xsd:attribute name="lastLoggedIn" use="optional" />
        <xsd:attribute name="enabled" use="optional" />
        <xsd:attribute name="calendarId" use="optional" />
        <xsd:attribute name="locale" use="optional" />
        <xsd:attribute name="activeDirectoryUser" use="optional" />
        <xsd:attribute name="password" use="optional" />
        <xsd:attribute name="version" use="optional" />
        <xsd:attribute name="country" use="optional" />
        <xsd:attribute name="id" use="optional" />
        <xsd:attribute name="username" use="optional" />
        <xsd:attribute name="timezone" use="optional" />
        <xsd:attribute name="registered" use="optional" />
        <xsd:attribute name="domain" use="optional" />
    </xsd:complexType>
</xsd:schema>
