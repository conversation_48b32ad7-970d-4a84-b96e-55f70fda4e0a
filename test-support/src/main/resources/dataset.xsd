<?xml version="1.0" encoding="ISO-8859-1"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" xmlns:dflt="ecco_test">
    <xsd:import namespace="ecco_test" schemaLocation="ecco_test.xsd" />
    <xsd:element name="dataset">
        <xsd:complexType>
            <xsd:choice minOccurs="0" maxOccurs="unbounded">
                <xsd:element name="jobs" type="dflt:jobs__type" />
                <xsd:element name="risks" type="dflt:risks__type" />
                <xsd:element name="supportthreatactions" type="dflt:supportthreatactions__type" />
                <xsd:element name="supportplanwork_actions" type="dflt:supportplanwork_actions__type" />
                <xsd:element name="languages" type="dflt:languages__type" />
                <xsd:element name="revision" type="dflt:revision__type" />
                <xsd:element name="supportplanoutcomes" type="dflt:supportplanoutcomes__type" />
                <xsd:element name="servicetypes_outcomesupports" type="dflt:servicetypes_outcomesupports__type" />
                <xsd:element name="activities" type="dflt:activities__type" />
                <xsd:element name="groups" type="dflt:groups__type" />
                <xsd:element name="signature" type="dflt:signature__type" />
                <xsd:element name="threatoutcomes_supportplanactions" type="dflt:threatoutcomes_supportplanactions__type" />
                <xsd:element name="individual_individualtypes" type="dflt:individual_individualtypes__type" />
                <xsd:element name="services" type="dflt:services__type" />
                <xsd:element name="reportattachments" type="dflt:reportattachments__type" />
                <xsd:element name="supportplanwork" type="dflt:supportplanwork__type" />
                <xsd:element name="venues" type="dflt:venues__type" />
                <xsd:element name="questions" type="dflt:questions__type" />
                <xsd:element name="users_aud" type="dflt:user_aud__type" />
                <xsd:element name="clientdetails" type="dflt:clientdetails__type" />
                <xsd:element name="referralcomments" type="dflt:referralcomments__type" />
                <xsd:element name="questiongroups" type="dflt:questiongroups__type" />
                <xsd:element name="projects" type="dflt:projects__type" />
                <xsd:element name="servicetypes_referralaspects" type="dflt:servicetypes_referralaspects__type" />
                <xsd:element name="accommodationcategories" type="dflt:accommodationcategories__type" />
                <xsd:element name="referrals_contacts" type="dflt:referrals_contacts__type" />
                <xsd:element name="supporthrwork_actions" type="dflt:supporthrwork_actions__type" />
                <xsd:element name="questionanswerchoices" type="dflt:questionanswerchoices__type" />
                <xsd:element name="group_members_aud" type="dflt:group_members_aud__type" />
                <xsd:element name="hrsettings" type="dflt:hrsettings__type" />
                <xsd:element name="audits" type="dflt:audits__type" />
                <xsd:element name="outcomethreats_actionsupports" type="dflt:outcomethreats_actionsupports__type" />
                <xsd:element name="servicetypes_outcomethreats" type="dflt:servicetypes_outcomethreats__type" />
                <xsd:element name="questionanswerfrees" type="dflt:questionanswerfrees__type" />
                <xsd:element name="events" type="dflt:events__type" />
                <xsd:element name="threatoutcomes_questions" type="dflt:threatoutcomes_questions__type" />
                <xsd:element name="supporthrwork" type="dflt:supporthrwork__type" />
                <xsd:element name="servicetypes_outcomeservices" type="dflt:servicetypes_outcomeservices__type" />
                <xsd:element name="pendingstatuses" type="dflt:pendingstatuses__type" />
                <xsd:element name="supporthrcomments" type="dflt:supporthrcomments__type" />
                <xsd:element name="localauthorities" type="dflt:localauthorities__type" />
                <xsd:element name="hrfromtos" type="dflt:hrfromtos__type" />
                <xsd:element name="flags" type="dflt:flags__type" />
                <xsd:element name="workers_jobs" type="dflt:workers_jobs__type" />
                <xsd:element name="commenttypes" type="dflt:commenttypes__type" />
                <xsd:element name="questions_questionanswerfrees" type="dflt:questions_questionanswerfrees__type" />
                <xsd:element name="supportthreatflags" type="dflt:supportthreatflags__type" />
                <xsd:element name="outcomes_st_referralaspects" type="dflt:outcomes_servicetypes_referralaspects__type" />
                <xsd:element name="group_members" type="dflt:group_members__type" />
                <xsd:element name="supportplanactions" type="dflt:supportplanactions__type" />
                <xsd:element name="servicetypes_questiongroups" type="dflt:servicetypes_questiongroups__type" />
                <xsd:element name="referralactivityworker" type="dflt:referralactivityworker__type" />
                <xsd:element name="supportthreatwork" type="dflt:supportthreatwork__type" />
                <xsd:element name="ldapgroupmapping" type="dflt:ldapgroupmapping__type" />
                <xsd:element name="activities_workers" type="dflt:activities_workers__type" />
                <xsd:element name="actions" type="dflt:actions__type" />
                <xsd:element name="group_authorities" type="dflt:group_authorities__type" />
                <xsd:element name="st_referralaspectsettings" type="dflt:servicetypes_referralaspectsettings__type" />
                <xsd:element name="supporthroutcomes" type="dflt:supporthroutcomes__type" />
                <xsd:element name="workers" type="dflt:workers__type" />
                <xsd:element name="contacts" type="dflt:contacts__type" />
                <xsd:element name="contacts_events" type="dflt:contacts_events__type" />
                <xsd:element name="actionfakes" type="dflt:actionfakes__type" />
                <xsd:element name="hr_outcomes" type="dflt:hr_outcomes__type" />
                <xsd:element name="supportplanrisks" type="dflt:supportplanrisks__type" />
                <xsd:element name="uploadfile" type="dflt:uploadfile__type" />
                <xsd:element name="supportthreatoutcomes" type="dflt:supportthreatoutcomes__type" />
                <xsd:element name="work" type="dflt:work__type" />
                <xsd:element name="workerattachments" type="dflt:workerattachments__type" />
                <xsd:element name="dbmaintain_scripts" type="dflt:dbmaintain_scripts__type" />
                <xsd:element name="reports" type="dflt:reports__type" />
                <xsd:element name="referralprojects" type="dflt:referralprojects__type" />
                <xsd:element name="reviews" type="dflt:reviews__type" />
                <xsd:element name="referralaspects" type="dflt:referralaspects__type" />
                <xsd:element name="supportthreatcomments" type="dflt:supportthreatcomments__type" />
                <xsd:element name="offences" type="dflt:offences__type" />
                <xsd:element name="supportplancomments" type="dflt:supportplancomments__type" />
                <xsd:element name="outcomethreats_questions" type="dflt:outcomethreats_questions__type" />
                <xsd:element name="referrals" type="dflt:referrals__type" />
                <xsd:element name="supportplananswers" type="dflt:supportplananswers__type" />
                <xsd:element name="servicetypes" type="dflt:servicetypes__type" />
                <xsd:element name="leavereasons" type="dflt:leavereasons__type" />
                <xsd:element name="activities_clients" type="dflt:activities_clients__type" />
                <xsd:element name="signpostreasons" type="dflt:signpostreasons__type" />
                <xsd:element name="economicstatuses" type="dflt:economicstatuses__type" />
                <xsd:element name="supportthreatwork_actions" type="dflt:supportthreatwork_actions__type" />
                <xsd:element name="hr" type="dflt:hr__type" />
                <xsd:element name="uploadbytes" type="dflt:uploadbytes__type" />
                <xsd:element name="children" type="dflt:children__type" />
                <xsd:element name="departments" type="dflt:departments__type" />
                <xsd:element name="qg_st_referralaspects" type="dflt:questiongroups_servicetypes_referralaspects__type" />
                <xsd:element name="supporthractions" type="dflt:supporthractions__type" />
                <xsd:element name="regions" type="dflt:regions__type" />
                <xsd:element name="setting" type="dflt:setting__type" />
                <xsd:element name="referralactivities" type="dflt:referralactivities__type" />
                <xsd:element name="activitytypes" type="dflt:activitytypes__type" />
                <xsd:element name="religions" type="dflt:religions__type" />
                <xsd:element name="partners" type="dflt:partners__type" />
                <xsd:element name="referralactivitytypes" type="dflt:referralactivitytypes__type" />
                <xsd:element name="agencycategories" type="dflt:agencycategories__type" />
                <xsd:element name="servicetypes_flagthreats" type="dflt:servicetypes_flagthreats__type" />
                <xsd:element name="projectcomments" type="dflt:projectcomments__type" />
                <xsd:element name="svcrec_attachments" type="dflt:svcrec_attachments__type" />
                <xsd:element name="accommodations" type="dflt:accommodations__type" />
                <xsd:element name="exitreasons" type="dflt:exitreasons__type" />
                <xsd:element name="referrersources" type="dflt:referrersources__type" />
                <xsd:element name="services_projects" type="dflt:services_projects__type" />
                <xsd:element name="outcomes" type="dflt:outcomes__type" />
                <xsd:element name="questiongroups_questions" type="dflt:questiongroups_questions__type" />
                <xsd:element name="questions_questionanswrchoices" type="dflt:questions_questionanswerchoices__type" />
                <xsd:element name="users" type="dflt:user__type" />
                <xsd:any namespace="ecco_test" />
            </xsd:choice>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>
