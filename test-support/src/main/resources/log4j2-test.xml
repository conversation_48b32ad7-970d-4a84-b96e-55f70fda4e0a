<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appenders>
        <Console name="syncStdout" target="SYSTEM_OUT">
            <PatternLayout pattern="%d %p [%c] %-15X{username} - %m%n"/>
        </Console>
        <Async name="stdout" includeLocation="true" blocking="true" bufferSize="262144">
            <AppenderRef ref="syncStdout"/>
        </Async>
    </appenders>

    <Loggers> <!-- NOTE: We could make all loggers async - see https://logging.apache.org/log4j/2.x/manual/async.html
or for an individual logger we could use asyncLogger or asyncRoot -->

        <!-- this file is used for integration testing -->
        <logger name="liquibase.changelog.ChangeSet" level="WARN"/>
        <logger name="liquibase.executor.jvm.JdbcExecutor" level="WARN"/>
        <!--<logger name="org.hibernate.SQL" level="DEBUG"/>-->
        <!--<logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE"/>-->
        <logger name="org.springframework.transaction" level="INFO"/>
        <logger name="org.hibernate.tool.hbm2ddl" level="DEBUG"/>
        <logger name="org.ehcache.jsr107.Eh107CacheManager" level="WARN"/>
        <logger name="org.ehcache.core.EhcacheManager" level="WARN"/>
        <Root level="INFO">
            <AppenderRef ref="stdout"/>
        </Root>
    </Loggers>
</configuration>
<!--

# DEBUG

# showing sql values
# keep this as it shows the sql (since we've disabled the show_sql)
#log4j.category.org.hibernate.SQL=TRACE
#log4j.category.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
# this is equivalent to format_sql in properties
#log4j.category.org.hibernate.hql.ast.AST=DEBUG
#log4j.category.org.hibernate.pretty.Printer=DEBUG
# show the object status - transient et
#log4j.category.org.hibernate.event.def=TRACE
log4j.category.org.hibernate.tool.hbm2ddl=DEBUG

# address lookup
#log4j.category.org.springframework.ws=DEBUG
#log4j.category.com.med.service.address.AddressFinderHidden=DEBUG
#log4j.category.com.med.web.CustomSpringRedirectView=DEBUG
# dwr
#log4j.category.org.directwebremoting=DEBUG
#log4j.category.com.med.web.UploadHiddenController=DEBUG
#log4j.category.org.springframework.web.servlet.handler.SimpleMappingExceptionResolver=DEBUG
#log4j.category.org.springframework.web.multipart.commons.CommonsFileUploadSupport=DEBUG

# problems with sending mail...
#log4j.category.org.springframework.mail.javamail.JavaMailSenderImpl=DEBUG
# problems with stale state exceptions
#log4j.category.com.med.service.threads.TaskJob=INFO
#log4j.category.com.med.service.threads.ExpiryJob=INFO
#log4j.category.com.med.service.threads.SendMessageThread=INFO
#log4j.category.com.med.service.TaskServiceImpl=INFO
#log4j.category.com.med.web.acknowledgments.ClickATellController=INFO

# 2nd level cache
log4j.category.org.hibernate.cache=INFO

# don't log the logger
#log4j.debug=true

# what to log
#log4j.category.com.med=DEBUG
log4j.category.org.springframework=INFO
#log4j.category.org.hibernate=DEBUG

# standard
#log4j.category.org.springframework=INFO
#log4j.category.org.acegisecurity=INFO
#log4j.category.org.springframework=INFO
#log4j.category.org.quartz=INFO

# showing transactions
# showing dbase see http://www.hibernate.org/hib_docs/reference/en/html/session-configuration.html
#log4j.category.org.springframework.transaction=TRACE
#log4j.category.org.springframework.transaction.interceptor.TransactionInterceptor=TRACE
#log4j.category.org.springframework.orm.hibernate4=DEBUG
#log4j.category.org.hibernate=INFO
#log4j.logger.org.hibernate.SQL=DEBUG
#log4j.org.hibernate.impl.SessionImpl=DEBUG
#log4j.org.hibernate.engine.QueryParameters=DEBUG
#log4j.additivity.org.hibernate.SQL=true
# Changing the log level to DEBUG will result in the PreparedStatement bound variable values to be logged.
#log4j.logger.org.hibernate.type=DEBUG
-->