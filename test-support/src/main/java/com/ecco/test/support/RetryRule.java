package com.ecco.test.support;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import lombok.extern.slf4j.Slf4j;
import org.junit.rules.TestRule;
import org.junit.runner.Description;
import org.junit.runners.model.Statement;

import asl.fm.last.commons.RetryTemplate;
import asl.fm.last.commons.RetryTemplate.RetryCallback;

/**
 * Allow a test to be retried on failure, and for the number of default retries to be overridden by using
 * {@link Retries} annotation at method, class or package level.
 */
@Slf4j
public final class RetryRule implements TestRule {


    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.METHOD, ElementType.TYPE, ElementType.PACKAGE})
    public @interface Retries {

        int value();
    }


    private final int forceMaxRetries;

    /** Set forceMaxRetries to zero if we are leaving it to annotations and default of no-retry (i.e. 1) */
    public RetryRule(int forceMaxRetries) {
        this.forceMaxRetries = forceMaxRetries;
    }

    @Override
    public Statement apply(final Statement base, Description description) {
        Retries annotation = AnnotationUtils.findMostSpecificAnnotation(description, Retries.class);

        final int retries = Math.min(annotation == null ? 1 : annotation.value(), forceMaxRetries);
        return new Statement() {
            @Override
            public void evaluate() {
                log.info("{} will retry {} times", description.getMethodName(), retries);
                new RetryTemplate(retries).execute( new RetryCallback<Void>() {
                    @Override
                    public Void doWithRetry() throws Throwable {
                        base.evaluate();
                        return null;
                    }
                });
            }
        };
    }
}
