package com.ecco.test.support;

import org.springframework.context.ApplicationContext;
import org.springframework.test.context.MergedContextConfiguration;
import org.springframework.test.context.support.DelegatingSmartContextLoader;

import com.ecco.infrastructure.util.StackTraceSanitizer;


public class StackTraceSanitisingContextLoader extends DelegatingSmartContextLoader {

    private final StackTraceSanitizer sanitizer = new StackTraceSanitizer();

    @Override
    public ApplicationContext loadContext(MergedContextConfiguration mergedConfig) throws Exception {

        try {
            ApplicationContext applicationContext = super.loadContext(mergedConfig);
            return applicationContext;
        } catch (Exception e) {
            sanitizer.sanitizeException(e);
            throw e;
        }
    }
}