package com.ecco.test.support

import org.apache.commons.lang.StringUtils
import org.springframework.util.Assert
import java.nio.ByteBuffer
import java.util.*

/**
 * Provides translation to test data (such as username, password, name, surname etc)
 * to a form such as "user-1-password".
 *
 * Varies per thread, but fixed within thread - i.e. not random
 *
 * <AUTHOR>
 */
class UniqueDataService private constructor() {
    private val threadUniqueId = ThreadLocal<String>()

    /** Varies per thread, but fixed within thread - i.e. not random */
    fun idFor(id: String): String {
        return "$id-$uniqueId"
    }

    fun userNameFor(user: String): String {
        val username = "$user-$uniqueId"
        // also see @Email used by cosmo's HibUser - which is a validation by hiberante at org/hibernate/validator/internal/constraintvalidators/AbstractEmailValidator.java
        Assert.isTrue(
            username.matches(Regex("^[\\u0020-\\ud7ff\\ue000-\\ufffd&&[^\\u007f\\u003a;/\\\\]]+$")),
            "Cosmo-incompatible username generated: $username"
        )
        return username
    }

    fun nameFor(name: String): String {
        return "$name-$uniqueId-name"
    }

    fun passwordFor(user: String): String {
        return StringUtils.left("$user-$uniqueId-password", 50)
    }

    fun firstNameFor(user: String): String {
        return "$user$uniqueId-first"
    }

    fun lastNameFor(user: String): String {
        return "$user-$uniqueId-last"
    }

    fun appendId(text: String): String {
        return "$text-$uniqueId"
    }

    fun clientFirstNameFor(clientFirstName: String): String {
        return clientFirstName + "-" + System.currentTimeMillis()
    }

    fun clientLastNameFor(clientLastName: String): String {
        return clientLastName + "-" + System.currentTimeMillis()
    }

    /** Varies per thread, but fixed within thread - i.e. not random */
    private val uniqueId: String
        get() {
            var id = threadUniqueId.get()
            if (id == null) {
                id = runId + Thread.currentThread().id
            }
            return id
        }

    companion object {
        private val runId = base64CurrentTimeMillis()

        /**
         * Shortened unique timestamp of when we started this session
         */
        private fun base64CurrentTimeMillis(): String {
            val base64 = Base64.getUrlEncoder()
                .encodeToString(ByteBuffer.allocate(8).putLong(System.currentTimeMillis()).array())
            return if (base64.startsWith("AAA")) {
                base64.substring(3) // feel free to do the math on when it becomes AAB
            } else base64
        }

        @JvmField
        var instance = UniqueDataService()
    }
}