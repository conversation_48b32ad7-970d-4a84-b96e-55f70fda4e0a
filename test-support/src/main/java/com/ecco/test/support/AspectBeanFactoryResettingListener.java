package com.ecco.test.support;

import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.support.GenericApplicationContext;
import org.springframework.test.context.TestContext;
import org.springframework.test.context.support.AbstractTestExecutionListener;
import org.springframework.transaction.aspectj.AnnotationTransactionAspect;

/**
 * This attempts to get around the issues mentioned in https://jira.springsource.org/browse/SPR-6121,
 * but still doesn't completely solve the problem.  Instead the solution seems to be to stick to one app context
 * per module - which is what we've done.
 */
public final class AspectBeanFactoryResettingListener extends AbstractTestExecutionListener {
    @Override
    public void prepareTestInstance(TestContext testContext) throws Exception {

        DefaultListableBeanFactory factory = ((GenericApplicationContext) testContext.getApplicationContext())
                .getDefaultListableBeanFactory();
        AnnotationBeanConfigurerAspect.aspectOf().destroy();
        AnnotationBeanConfigurerAspect.aspectOf().setBeanFactory(factory);
        AnnotationTransactionAspect.aspectOf().setBeanFactory(factory);
    }
}