package com.ecco.test.support;

import com.ecco.infrastructure.config.root.ConfigurableEccoEnvironment;
import com.ecco.infrastructure.config.root.EccoApplicationContextInitializer;
import com.ecco.infrastructure.config.root.SchemaVersion;

/**
 * Handle the environment for tests.
 * Override so that we control the db, profiles, log locations etc.
 * NB See log4j2-test.xml for logging in test-support.
 */
public class TestAppContextInitializer extends EccoApplicationContextInitializer {

    @Override
    protected void configureEnvironment(ConfigurableEccoEnvironment environment) {
        SpringTestSupport.configureMockPropertySource(environment)
                .withProperty("db.version", SchemaVersion.LATEST.getAsText());
    }

}
