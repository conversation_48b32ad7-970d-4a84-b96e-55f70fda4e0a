package com.ecco.test.support;

import java.lang.annotation.Annotation;

import org.junit.runner.Description;

public abstract class AnnotationUtils {

    public static <A extends Annotation> A findMostSpecificAnnotation(Description description, Class<A> annotationType) {
        A annotation = description.getAnnotation(annotationType);
        if (annotation == null) {
            annotation = description.getTestClass().getAnnotation(annotationType);
        }
        if (annotation == null) {
            annotation = description.getTestClass().getPackage().getAnnotation(annotationType);
        }
        return annotation;
    }

}
