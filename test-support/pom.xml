<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>test-support</artifactId>

    <parent>
        <groupId>org.eccosolutions</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0.CI-SNAPSHOT</version>
        <relativePath>../parent/pom.xml</relativePath>
    </parent>

    <dependencies>
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-jcache</artifactId>
        </dependency>

        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
        </dependency>

        <dependency>
            <groupId>org.dbunit</groupId>
            <artifactId>dbunit</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-nop</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.springtestdbunit</groupId>
            <artifactId>spring-test-dbunit</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency> <!-- Now only used for ReflectionAssert -->
            <groupId>org.unitils</groupId>
            <artifactId>unitils-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ognl</groupId>
                    <artifactId>ognl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Do we even need this?  It's for unitils above .. and DO we still use that? -->
        <dependency>
            <groupId>opensymphony</groupId>
            <artifactId>ognl</artifactId>
            <version>2.6.11</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>compile</scope>
        </dependency>

    </dependencies>
</project>
