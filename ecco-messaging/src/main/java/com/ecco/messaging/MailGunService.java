package com.ecco.messaging;

import com.ecco.infrastructure.rest.RestClient;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import java.net.URI;
import java.nio.charset.Charset;
import java.util.Base64;

@RequiredArgsConstructor
public class MailGunService {

    private final String apiKey;
    private final String domain;

    private HttpHeaders headersWithLogin;
    private RestTemplate restTemplate;
    private String mailGunApiBase;

    public URI sendMessage(String emailAddrs, String subject, String bodyText) {
        lazyInit();
        InternetAddress[] recipients;
        try {
            recipients = InternetAddress.parse(emailAddrs);
        } catch (AddressException e) {
            throw new RuntimeException("Unable to parse email addresses:" + emailAddrs);
        }


        LinkedMultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("from", "ecco-notifications <postmaster@"+ domain + ">");
        for (InternetAddress recipient : recipients) {
            formData.add("to", recipient.toString());
        }
        formData.add("subject", subject);
        formData.add("html", bodyText); // see https://documentation.mailgun.com/en/latest/api-sending.html#sending
        formData.add("text", bodyText); // shows as html, but better than nothing if client not compatible

        // NB getting the IP this way can only ever be a guess, without accessing the underlying connection
        /*
        // be sure we know the IP - since we're being told its caching issues
        String ipAddress = null;
        try {
            ipAddress = InetAddress.getByName(mailGunApiBase).getHostAddress();
        } catch (UnknownHostException e) {
            log.error("EMAIL FAILED - finding IP: " + ipAddress);
            throw new RuntimeException(e);
        }
        log.error("EMAIL IP - mailgun IP guess: " + ipAddress);
        */

        ResponseEntity<String> result = restTemplate.exchange(mailGunApiBase + "/messages", HttpMethod.POST,
                new HttpEntity<>(formData, headersWithLogin), String.class);
        return result.getHeaders().getLocation();
    }

    /** Most clients don't use this so we don't bother hitting database for settings until we want to send first mail */
    private void lazyInit() {
        if (headersWithLogin != null) {
            return;
        }
        String userName = "api";

        mailGunApiBase = "https://api.mailgun.net/v3/" + domain;
        RestClient client = new RestClient(mailGunApiBase, true);
        restTemplate = client.template();

        String auth = userName + ":" + apiKey;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(Charset.forName("US-ASCII")));
        String authHeader = "Basic " + encodedAuth;
        headersWithLogin = new HttpHeaders();
        headersWithLogin.set("Authorization", authHeader);
    }
}
