package com.ecco.messaging;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;


@Slf4j
@RequiredArgsConstructor
public class DelegatingEmailService implements EmailService {

    @Value("${ecco.email.enabled:false}")
    private boolean emailEnabled = false;

    private final String emailDomain;

    private final JavaMailSender mailSender;

    private final MailGunService mailGunService;

    @Override
    public void sendMessage(String emailAddrs, String subject, String bodyText) {
        // cached

        if (!this.emailEnabled) {
            log.info("EMAIL NOT SENT - ecco.email.enabled is false: " + subject);
            return;
        }

        // mailgun seems to require a body, but it's likely we don't expect a failure, so just make it clear
        if (bodyText == null) {
            bodyText = "- no content -";
        }
        if (mailSender != null) {
            log.info("EMAIL SENDING - mailSender: " + subject);
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper message = null;
            try {
                message = new MimeMessageHelper(mimeMessage, true, "UTF-8");
                message.setTo(emailAddrs);
                message.setFrom("ecco-notifications <postmaster@" + emailDomain + ">");
                message.setSubject(subject);
                message.setText(bodyText, true);
                mailSender.send(message.getMimeMessage());
            } catch (MessagingException e) {
                log.error("EMAIL FAILED - mailSender: " + subject, e);
                throw new RuntimeException(e);
            }
        }
        else if (mailGunService != null) {
            log.info("EMAIL SENDING - mailgun: " + subject);
            // ? display returned URI
            try {
                mailGunService.sendMessage(emailAddrs, subject, bodyText);
            } catch (RuntimeException e) {
                log.error("EMAIL FAILED - mailgun: " + subject, e);
                throw e;
            }
        }
        else {
            log.info("EMAIL NOT SENT - no mail service configured: {}", subject);
        }
    }
}
