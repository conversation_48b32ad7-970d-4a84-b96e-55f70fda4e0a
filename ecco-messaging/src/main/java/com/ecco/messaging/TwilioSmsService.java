package com.ecco.messaging;

import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.util.Objects;

import static org.springframework.util.StringUtils.hasText;


@SuppressWarnings("SpringElInspection")
@ConditionalOnProperty(value = "ecco.messaging.twilio.enabled", havingValue = "true")
@Component
@Slf4j
public class TwilioSmsService {

    @Value("#{environment.TWILIO_ACCOUNT_SID}")
    private String ACCOUNT_SID;

    @Value("#{environment.TWILIO_AUTH_TOKEN}")
    private String AUTH_TOKEN;

    @Value("#{environment.TWILIO_PHONE_NUMBER}")
    private String FROM_NUMBER;

    @PostConstruct
    public void init() {
        if (hasText(ACCOUNT_SID)) {
            Twilio.init(ACCOUNT_SID, AUTH_TOKEN);
        }
    }

    public @Nonnull MessageResult sendSms(String toMobileNumber, String smsBody) {
        if (!hasText(ACCOUNT_SID)) {
            log.warn("Twilio not configured.  Cannot send SMS: {}\nto {}", smsBody, toMobileNumber);
            return MessageResult.failed(toMobileNumber, "Outbound SMS not configured. Message not really sent.");
        }

        var toMobileNumberClean = StringUtils.trimAllWhitespace(toMobileNumber);
        log.debug("Submitting SMS message");// Unique resource ID created to manage this transaction
        Message message = Message.creator(new PhoneNumber(toMobileNumberClean), new PhoneNumber(FROM_NUMBER), smsBody)
                .create();
        log.debug("Submitted SMS message: {}", message.getSid());// Unique resource ID created to manage this transaction
        log.debug("status: {}", message.getStatus());
        if (hasText(message.getErrorMessage())) {
            log.error("Twilio error: {}", message.getErrorMessage());
            return MessageResult.failed(toMobileNumber, message.getErrorMessage());
        }
        return MessageResult.success(toMobileNumber);
    }

    public void validateAccount(String accountSid) {
        Assert.state(Objects.equals(accountSid, ACCOUNT_SID), "Invalid inbound account for webhook");
    }
}