package com.ecco.messaging;

import com.ecco.config.service.SettingsService;
import org.springframework.boot.autoconfigure.mail.MailSenderAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.mail.javamail.JavaMailSender;

import java.util.Optional;

import static org.springframework.util.StringUtils.hasText;

@Import({
        MailSenderAutoConfiguration.class,
})
@ComponentScan( basePackageClasses = {
        DelegatingEmailService.class, DelegatingMessageService.class
})
public class MessagingConfig {

    @Bean
    public MailGunService mailGunService(SettingsService settingsService) {
        String apiKey = settingsService.settingFor(SettingsService.MailGun.NAMESPACE, SettingsService.MailGun.API_KEY).getValue();
        String domain = settingsService.settingFor(SettingsService.MailGun.NAMESPACE, SettingsService.MailGun.DOMAIN).getValue();
        return new MailGunService(apiKey, domain);
    }

    @Bean
    public EmailService emailService(Optional<JavaMailSender> mailSender, Optional<MailGunService> mailGunService, SettingsService settingsService) {
        // TODO: Offer some configuration for a default "from address" rather than abusing Mailgun setting
        String emailDomain = settingsService.settingFor(SettingsService.MailGun.NAMESPACE, SettingsService.MailGun.DOMAIN).getValue();
        if (!hasText(emailDomain)) {
            emailDomain = "eccosolutions.co.uk";
        }
        return new DelegatingEmailService(emailDomain, mailSender.orElse(null), mailGunService.orElse(null));
    }

    /*@Bean
    public MessageService smsService(Optional<TwilioSmsService> mailSender, Optional<MailGunService> mailGunService, SettingsService settingsService) {
        // TODO: Offer some configuration for a default "from address" rather than abusing Mailgun setting
        String emailDomain = settingsService.settingFor(SettingsService.MailGun.NAMESPACE, SettingsService.MailGun.DOMAIN).getValue();
        if (!hasText(emailDomain)) {
            emailDomain = "eccosolutions.co.uk";
        }
        return new DelegatingEmailService(emailDomain, mailSender.orElse(null), mailGunService.orElse(null));
    }*/

}
