package com.ecco.messaging;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MessageResult {

    public static String SUCCESS = "success";
    public static String FAILED = "failed";

    private String status;
    private String id; // the identifier, eg mobile number
    private String message = "";

    public static MessageResult success(String id) {
        return new MessageResult(SUCCESS, id, null);
    }
    public static MessageResult success(String id, String message) {
        return new MessageResult(SUCCESS, id, message);
    }
    public static MessageResult failed(String id, String message) {
        return new MessageResult(FAILED, id, message);
    }
    public static MessageResult error(String id, Exception e) {
        return new MessageResult(FAILED, id, e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName());
    }

    private MessageResult(String status, String id, String message) {
        super();
        this.status = status;
        this.id = id;
        this.message = message;
    }

    public boolean isSuccessful() {
        return MessageResult.SUCCESS.equals(status);
    }

}
