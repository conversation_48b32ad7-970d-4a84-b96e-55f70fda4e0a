package com.ecco.dao;

import com.ecco.evidence.ParentChildResolver;
import com.ecco.evidence.ParentChildResolverDefault;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Repository;

@Configuration(proxyBeanMethods = false)
@ComponentScan(basePackageClasses=ReferralDaoHibernate.class, includeFilters= @Filter(Repository.class))
public class EccoDaoConfig {

    @Bean
    public ParentChildResolver parentChildResolver(ServiceRepository serviceRepository) {
        return new ParentChildResolverDefault(serviceRepository);
    }

}
