package com.ecco.dao.querydsl;

import com.ecco.dom.*;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.DateTimePath;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;

import javax.annotation.Nullable;
import java.time.Instant;
import java.time.LocalDateTime;

import static com.querydsl.core.types.ExpressionUtils.and;

/**
 * Some helper methods in creating predicates. Currently this is exclusivly about date ranges, and timezone complications.
 *
 * Date ranges converts various types of date range into an expression on fields of various date types. This is needed because
 * of ECCO-1110 (timezones) where we need to understand how the incoming date relates to the database column.
 * Currently all fields are saved and read as UTC - all we've done is translate things on views (see medFn.tld).
 * We don't currently address querying the database correctly - which this class hopes to address.
 *
 * So, as per ECCO-575, if we have a system 'created' datetime during BST of 2015-08-03 17:00 UTC (18:00 on our clock),
 * then if a user asks for a report from 2015-08-03 18:00 (which is in BST)- it will be treated as 2015-08-03 18:00 UTC
 * which is in fact 19:00 on our clock. So this needs to be converted to UTC 17:00 to match the db, else that record is
 * not going to be included. Currently this isn't noticed around reports, since we only miss an hour of data when applying
 * user dates to system ones. So 2015-08-03 is treated as 2015-08-03 00:00 UTC and not 2015-08-02 23:00 UTC.
 *
 * Therefore (see ECCO-1110):
 *   1: presented local user date put against utc system datetime:
 *      eg 2015-08-03 against 2015-08-03 00:43 UTC, query needs to be 2015-08-02 23:00 (in BST) to capture all UTC records
 *      TODO db datetime fields which are meant to be date only can simply be converted to date only
 *      TODO and refactor jsp to use local date so there is no tz conversion confusion
 *   2: presented local user date+time put against utc system datetime:
 *      eg 2015-08-03 00:00 against 2015-08-03 00:43 UTC, query needs to be 2015-08-02 23:00 (in BST) to capture all UTC records
 *      TODO modify code so that the users dates are supplied with their timezone, so time based queries are correct
 *   3: presented local user date put against utc user datetime (but should be date only):
 *      eg 2015-08-03 against 2015-08-03 00:43 UTC, do no translation since both are incorrect so work fine as-is
 *      TODO db datetime fields which are meant to be date only can simply be converted to date only
 *      TODO and refactor jsp to use local date so there is no tz conversion confusion
 *      TODO and refactor code of datetime to use local date so there is no tz conversion confusion
 *   4: presented local user date+time put against utc user datetime:
 *      eg 2015-08-03 00:00 against 2015-08-03 00:43 UTC, do no translation since both are incorrect so work fine as-is
 *      TODO modify users db fields for user dates that occur during BST to be UTC
 *      TODO modify code so that the users dates are supplied with their timezone, so time based queries are correct
 */

public abstract class PredicateSupport {

    /**
     * Apply a user-defined date against instants.
     *
     * @param p Predicate to add to
     * @param dateField Path to apply the range
     * @param localFrom The users local date
     * @param localTo The users local date
     * @return new expression
     */
    public static Predicate applyLocalDateRangeOnInstant(@Nullable Predicate p, DateTimePath<Instant> dateField,
                                                         LocalDate localFrom, LocalDate localTo) {

        // act as a system date, because we are an Instant
        DateTime from = localFrom == null ? null : localFrom.toLocalDateTime(LocalTime.MIDNIGHT).toDateTime(DateTimeZone.forID("Europe/London"));
        DateTime to = localTo == null ? null : localTo.toLocalDateTime(LocalTime.MIDNIGHT).toDateTime(DateTimeZone.forID("Europe/London"));

        if (from != null) {
            BooleanExpression dte = dateField.goe(Instant.ofEpochMilli(from.getMillis()));
            p = and(p, dte);
        }
        if (to != null) {
            BooleanExpression dte = dateField.before(Instant.ofEpochMilli(to.plusDays(1).getMillis()));
            p = and(p, dte);
        }

        return p;
    }

    /**
     * Capture the fields which need a translation, that is user-presented dates against system generated dates.
     * So we searched for 'userFriendlyDateFromUtc_' since the jsp already has this logic.
     *
     * NB We ignore scenario 3 and 4 above as they cancel-out, but a proper solution is needed, see ECCO-1110.
     *
     * @param p Predicate to add to
     * @param dateField Path to apply the range
     * @param localFrom The users local date
     * @param localTo The users local date
     * @return new expression
     */
    public static Predicate applyLocalDateRange(@Nullable Predicate p, DateTimePath<DateTime> dateField,
                                                LocalDate localFrom, LocalDate localTo) {

        // if isSystemField:
        //  converts to datetime, applying the users timezone so the result has the correct UTC
        //  1: captured in this method
        //  2: captured in this method if provided dates included time
        // else
        //  3: captured in this method
        //  4: captured in this method if provided dates included time

        // NB see also DateTimeUtils.convertFromUsersLocalDateTime_ForLocalDateTime for how jadira reads the dates

        boolean systemField = isSystemField(dateField);
        DateTime from = systemField ?
                localFrom == null ? null : localFrom.toLocalDateTime(LocalTime.MIDNIGHT).toDateTime(DateTimeZone.forID("Europe/London"))
                :
                localFrom == null ? null : localFrom.toLocalDateTime(LocalTime.MIDNIGHT).toDateTime(DateTimeZone.UTC);

        DateTime to = systemField ?
                localTo == null ? null : localTo.toLocalDateTime(LocalTime.MIDNIGHT).toDateTime(DateTimeZone.forID("Europe/London"))
                :
                localTo == null ? null : localTo.toLocalDateTime(LocalTime.MIDNIGHT).toDateTime(DateTimeZone.UTC);

        if (from != null) {
            BooleanExpression dte = dateField.goe(from);
            p = and(p, dte);
        }
        if (to != null) {
            BooleanExpression dte = dateField.before(to.plusDays(1));
            p = and(p, dte);
        }
        return p;
    }

    /**
     * NB Can't use generics here for LocalDate and DateTime since they are separate class hierarchies
     * @param localFrom DateTime that has the user's timezone
     * @param localTo DateTime that has the user's timezone EXCLUSIVE datetime (in that < is applied)
     */
    public static BooleanExpression applyLocalDateTimeRange(BooleanExpression p, DateTimePath<DateTime> dateField,
            DateTime localFrom, DateTime localTo) {

        // if isSystemField:
        //  use the datetime tz since both are tz-aware
        //  1: captured in this method
        //  2: captured in this method if provided dates included time
        // else
        //  3: captured in this method by 'undo-ing' the user supplied timezone so that we match incorrect arg with incorrect db
        //  4: captured in this method if provided dates included time

        boolean systemField = isSystemField(dateField);
        DateTime from = systemField ?
                localFrom
                :
                localFrom == null ? null : localFrom.withZone(DateTimeZone.UTC); // preserves ms
        DateTime to = systemField ?
                localTo
                :
                localTo == null ? null : localTo.withZone(DateTimeZone.UTC);

        if (from != null) {
            p = p.and(dateField.goe(from));
        }
        if (to != null) {
            p = p.and(dateField.before(to));
        }
        return p;
    }

    public static Predicate applyLocalDateJdkRange(Predicate p, DateTimePath<LocalDateTime> dateField,
                                                        org.joda.time.LocalDate localFrom, org.joda.time.LocalDate localTo) {
        if (localFrom != null) {
            BooleanExpression dte = dateField.goe(JodaToJDKAdapters.localDateToJDk(localFrom).atStartOfDay());
            p = and(p, dte);
        }
        if (localTo != null) {
            BooleanExpression dte = dateField.before(JodaToJDKAdapters.localDateToJDk(localTo).atStartOfDay());
            p = and(p, dte);
        }
        return p;
    }

    /**
     * Mimic applyLocalDateTimeRange but for indiviual fields, not ranges
     */
    public static DateTime applyLocalDateTime(DateTimePath<DateTime> dateField, DateTime localDateTime) {
        if (isSystemField(dateField)) {
            return localDateTime;
        }
        // preserves ms
        return localDateTime == null ? null : localDateTime.withZone(DateTimeZone.UTC);
    }

    /**
     * List the fields which need a translation, that is assuming a local date/time against a system generated date field.
     */
    public static boolean isSystemField(DateTimePath<DateTime> dateField) {

        // We searched for 'userFriendlyDateFromUtc_' in jsps since the jsp already has this logic.
        if (dateField.equals(QReferral.referral.decisionMadeOnDT) ||
            (dateField.equals(QReferral.referral.decisionReferralMadeOnDT)) ||
            (dateField.equals(QReferral.referral.serviceRecipient.created)) ||
            (dateField.equals(QReferralBaseComment.referralBaseComment.created)) ||
            (dateField.equals(QSignpostComment.signpostComment.created)) ||
            (dateField.equals(QExitComment.exitComment.created))) {
            // QUser.user.registered not used in query crtieria? also revision.user.registered
            return true;
        }

        // We don't need to map the support work created, since workDate is the only one used when converting
        // (but we do anyway - only for work)
        return dateField.equals(QEvidenceSupportWork.evidenceSupportWork.created) ||
                (dateField.equals(QEvidenceThreatWork.evidenceThreatWork.created));

    }

}
