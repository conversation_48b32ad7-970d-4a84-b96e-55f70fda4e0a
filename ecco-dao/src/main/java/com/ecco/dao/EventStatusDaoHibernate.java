package com.ecco.dao;

import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@Repository
public class EventStatusDaoHibernate implements EventStatusDao {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public void setStatusDNA(long eventId, long contactId) {
        Session session = (Session)entityManager;
        session.createQuery("update versioned Contact_Event e set e.eventContactStatusId=:statusId where e.event.id=:eventId and e.contact.id=:contactId")
            .setLong("eventId", eventId)
            .setLong("contactId", contactId)
            .executeUpdate();
    }

}
