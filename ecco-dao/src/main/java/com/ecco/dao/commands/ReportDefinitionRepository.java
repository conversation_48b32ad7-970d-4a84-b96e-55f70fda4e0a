package com.ecco.dao.commands;

import com.ecco.dom.ReportDefinition;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.UUID;

/**
 * CRUD Persitence for report definitions
 */
public interface ReportDefinitionRepository extends JpaRepository<ReportDefinition, UUID> {

    @Override
    @Query("select rd from ReportDefinition rd ORDER BY rd.orderby ASC")
    List<ReportDefinition> findAll();
}
