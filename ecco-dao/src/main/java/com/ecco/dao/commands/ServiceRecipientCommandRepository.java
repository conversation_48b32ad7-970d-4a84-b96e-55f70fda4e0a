package com.ecco.dao.commands;

import com.ecco.dom.commands.*;
import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import org.joda.time.DateTime;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;

import javax.persistence.QueryHint;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface ServiceRecipientCommandRepository extends BaseCommandRepository<ServiceRecipientCommand, Long> {

    ServiceRecipientCommand findOneByUuid(UUID uuid);
    Optional<ServiceRecipientCommand> findOneOptionalByUuid(UUID uuid);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<ServiceRecipientCommand> findAllByServiceRecipientIdOrderByCreatedDesc(int serviceRecipientId, Pageable pageable);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT e FROM ServiceRecipientCommand e WHERE TYPE(e) = DeleteRequestServiceRecipientCommand AND e.serviceRecipientId = ?1 order by e.created asc")
    List<DeleteRequestServiceRecipientCommand> findDeleteRequestsByServiceRecipientId(int serviceRecipientId);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT e FROM ServiceRecipientCommand e WHERE TYPE(e) = DeleteRequestEvidenceCommand AND e.serviceRecipientId = ?1 order by e.created asc")
    List<DeleteRequestEvidenceCommand> findDeleteEvidenceRequestsByServiceRecipientId(int serviceRecipientId);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT e FROM ServiceRecipientCommand e WHERE TYPE(e) = DeleteEvidenceCommand AND e.serviceRecipientId = ?1 order by e.created asc")
    List<DeleteEvidenceCommand> findDeleteEvidenceByServiceRecipientId(int serviceRecipientId);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT src FROM ServiceRecipientCommand src WHERE src.serviceRecipientId = ?1 AND src.evidenceGroupKey = ?2 order by src.created asc")
    List<ServiceRecipientCommand> findAllByServiceRecipientIdAndEvidenceGroup(
            int serviceRecipientId,
            String evidenceGroupKey);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT src FROM EvidenceAssociatedContactCommand src WHERE src.serviceRecipientId = ?1 AND src.evidenceGroupKey = ?2 order by src.created asc")
    List<EvidenceAssociatedContactCommand> findAssociatedContactCommandByServiceRecipientIdAndEvidenceGroup(
            int serviceRecipientId,
            String evidenceGroupKey);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT src FROM ServiceRecipientCommand src WHERE src.serviceRecipientId = ?1 AND src.taskName = ?2 order by src.created asc")
    List<ServiceRecipientCommand> findAllByServiceRecipientIdAndTaskName(
            int serviceRecipientId,
            String taskName);

    @Query("SELECT src FROM ServiceRecipientCommand src"
            + " WHERE src.serviceRecipientId = ?1 AND src.evidenceGroupKey = ?2 AND src.taskName = ?3 order by src.created asc")
    List<ServiceRecipientCommand> findAllByServiceRecipientIdAndEvidenceGroupAndTaskName(
            int serviceRecipientId,
            String evidenceGroupKey,
            String taskName);


    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT cc FROM CommentCommand cc WHERE cc.serviceRecipientId = ?1 AND cc.evidenceGroupKey = ?2 order by cc.created asc")
    List<CommentCommand> findAllCommentsByServiceRecipientIdAndEvidenceGroup(
            int serviceRecipientId,
            String evidenceGroupKey);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT e FROM ServiceRecipientAssociatedContactCommand e WHERE e.serviceRecipientId = ?1 and e.contactId = ?2 order by e.created asc")
    List<ServiceRecipientAssociatedContactCommand> findAssociatedContactByServiceRecipientIdOrderByCreatedAsc(int serviceRecipientId, int contactId);

    /** Get latest command so we can use timestamp in If-Modified-Since */
    // TODO: Just get created field here so it'll just hit the index on the database
    // TODO: Create index (servicerecipientid, created desc)
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    Optional<ServiceRecipientCommand> findFirst1ByServiceRecipientIdOrderByCreatedDesc(int serviceRecipientId);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<ServiceRecipientCommand> findAllByServiceRecipientIdAndCreatedGreaterThanEqualOrderByCreatedDesc(
            int serviceRecipientId, DateTime instant);


}
