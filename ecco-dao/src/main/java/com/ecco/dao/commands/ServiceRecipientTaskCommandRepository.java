package com.ecco.dao.commands;

import com.ecco.dom.commands.ServiceRecipientTaskCommand;
import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.Repository;

import javax.persistence.QueryHint;
import java.util.List;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

/**
 * Internal usage - please see ServiceRecipientTaskCommandService.
 */
public interface ServiceRecipientTaskCommandRepository extends Repository<ServiceRecipientTaskCommand, Long> {

    /**
     * Get the latest command for a referralId and taskName.
     * NB This doesn't take parent srId into account - see ServiceRecipientTaskCommandRepository
     * An attempt to mimic http://stackoverflow.com/questions/2751941/hibernate-query-get-latest-versions-by-timestamp
     * @see WhereExpressionVisitorHql.visit(GroupCriteria e)
     * @see GenericTypeSupportActionsFromReferralFilter
     *
     * NB the previous repository was querysl based on these useful links:
     * http://stackoverflow.com/questions/********/querydsl-join-subquery
     * http://www.solved.online/3573818/path-references-in-querydsl-join-to-subquery/
     * http://stackoverflow.com/questions/7269010/jpa-hibernate-subquery-in-from-clause
     */
    @Query(nativeQuery = true, value="SELECT svc1.* from svcrec_commands svc1 " +
            "INNER JOIN (SELECT serviceRecipientId, taskName, max(id) as maxid " +
                "from svcrec_commands " +
                "where serviceRecipientId=?1 " +
                "and commandName <> 'userAccessAudit' " +
                "group by serviceRecipientId, taskName) svcj " +
            "on svc1.serviceRecipientId=svcj.serviceRecipientId " +
            "and svc1.taskName=svcj.taskName " +
            "and svc1.id=svcj.maxid " +
            "where svc1.serviceRecipientId=?1 " +
            "and commandName <> 'userAccessAudit'")
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    // ? draft false
    List<ServiceRecipientTaskCommand> findAllLatestCommandPerTaskNameExcludingUserAudit(int serviceRecipientId);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    // ? draft false
    List<ServiceRecipientCommand> findAllByServiceRecipientIdAndTaskNameOrderByCreatedDesc(int serviceRecipientId, String taskName, Pageable pageable);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<ServiceRecipientCommand> findAllByServiceRecipientIdAndTaskNameAndDraftTrueOrderByCreatedDesc(int serviceRecipientId, String taskName, Pageable pageable);

}
