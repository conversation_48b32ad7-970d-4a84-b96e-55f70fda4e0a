package com.ecco.dao.commands;

import com.ecco.dom.commands.ReportDefinitionCommand;
import com.ecco.dom.commands.ReportDefinitionUpdateCommand;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;

import java.util.List;
import java.util.UUID;

/**
 * Persitence for report definition commands
 */
public interface ReportDefinitionCommandRepository extends Repository<ReportDefinitionCommand, UUID> {

    <TReportDefinitionCommand extends ReportDefinitionCommand> TReportDefinitionCommand save(TReportDefinitionCommand command);

    @Query("SELECT ru FROM ReportDefinitionUpdateCommand ru WHERE ru.reportDefUuid = ?1")
    List<ReportDefinitionUpdateCommand> findAllUpdatesByReportDefUuid(UUID reportDefUuid);

    @Query("SELECT ru FROM ReportDefinitionUpdateCommand ru")
    List<ReportDefinitionUpdateCommand> findAllUpdates();

    ReportDefinitionCommand findOneByUuid(UUID uuid);

}
