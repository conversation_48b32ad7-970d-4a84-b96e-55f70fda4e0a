package com.ecco.dao.commands;

import com.ecco.dom.commands.ServiceRecipientTaskCommand;
import com.ecco.infrastructure.config.root.CacheConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ServiceRecipientTaskCommandServiceImpl implements ServiceRecipientTaskCommandService {

    @Autowired
    private ServiceRecipientTaskCommandRepository taskCommandRepository;

    @Cacheable(cacheNames = CacheConfig.CACHE_SR_TASK_COMMANDS, key = "#serviceRecipientId")
    public List<ServiceRecipientTaskCommand> findAllLatestCommandPerTaskName(int serviceRecipientId) {

        List<ServiceRecipientTaskCommand> allCommands = taskCommandRepository
                .findAllLatestCommandPerTaskNameExcludingUserAudit(serviceRecipientId);

        return allCommands;
    }

}
