package com.ecco.dao.commands;

import com.ecco.dom.servicerecipients.commands.ServiceRecipientCommandArchive;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.Repository;

import javax.persistence.QueryHint;
import java.util.List;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface ServiceRecipientCommandArchiveRepository extends Repository<ServiceRecipientCommandArchive, Long> {

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<ServiceRecipientCommandArchive> findByServiceRecipientIdOrderByCreatedDesc(int serviceRecipientId);

}
